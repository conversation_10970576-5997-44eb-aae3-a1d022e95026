warning package.json: No license field
$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use <PERSON><PERSON>'s "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/services/backoffice-services-impl[39m

(node:65932) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:9872) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:19276) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:5796) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:103352) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:84944) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:84888) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:125104) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:3576) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:89928) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:58112) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:109076) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:118536) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:70140) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/customer-service/ClaimCensusServiceImpl.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 15[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimCustomerRelationshipsServiceImpl.test.ts [2m([22m[2m11 tests[22m[2m)[22m[90m 20[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimIndividualCustomerServiceImpl.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 16[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimProviderServiceImpl.test.ts [2m([22m[2m16 tests[22m[2m)[22m[90m 21[2mms[22m[39m
 [32m✓[39m test/policy-service/ClaimPolicyServiceImpl.test.ts [2m([22m[2m15 tests[22m[2m)[22m[90m 22[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimOrganiztionCustomerServiceImpl.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 15[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimCustomerCommonServiceImpl.test.ts [2m([22m[2m12 tests[22m[2m)[22m[90m 20[2mms[22m[39m
 [32m✓[39m test/siderbar-service/task-service/DXPUserTaskService.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 6[2mms[22m[39m
 [32m✓[39m test/policy-service/ClaimDentalPolicyServiceImpl.test.ts [2m([22m[2m29 tests[22m[2m)[22m[90m 29[2mms[22m[39m
 [32m✓[39m test/siderbar-service/efolder-service/DXPEfolderService.test.ts [2m([22m[2m11 tests[22m[2m)[22m[90m 17[2mms[22m[39m
 [32m✓[39m test/siderbar-service/task-service/DXPCaseService.test.ts [2m([22m[2m10 tests[22m[2m)[22m[90m 18[2mms[22m[39m
 [32m✓[39m test/siderbar-service/bam-service/DXPActivityService.test.ts [2m([22m[2m6 tests[22m[2m)[22m[90m 18[2mms[22m[39m
 [32m✓[39m test/siderbar-service/note-service/DXPNoteService.test.ts [2m([22m[2m35 tests[22m[2m)[22m[90m 63[2mms[22m[39m
 [32m✓[39m test/siderbar-service/task-service/DXPQueueService.test.ts [2m([22m[2m9 tests[22m[2m)[22m[90m 7[2mms[22m[39m

[2m Test Files [22m [1m[32m14 passed[39m[22m[90m (14)[39m
[2m      Tests [22m [1m[32m182 passed[39m[22m[90m (182)[39m
[2m   Start at [22m 16:06:42
[2m   Duration [22m 7.66s[2m (transform 4.05s, setup 59.09s, collect 6.61s, tests 286ms, environment 23.07s, prepare 4.99s)[22m

