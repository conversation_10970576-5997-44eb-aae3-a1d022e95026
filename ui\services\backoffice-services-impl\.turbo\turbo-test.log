warning package.json: No license field
$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use <PERSON><PERSON>'s "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/services/backoffice-services-impl[39m

(node:22100) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:72344) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:41272) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:82468) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:30372) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:39408) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:45808) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:62080) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:35968) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:36876) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:65976) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:64064) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:40696) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:51836) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/customer-service/ClaimCustomerCommonServiceImpl.test.ts [2m([22m[2m12 tests[22m[2m)[22m[90m 13[2mms[22m[39m
 [32m✓[39m test/policy-service/ClaimPolicyServiceImpl.test.ts [2m([22m[2m15 tests[22m[2m)[22m[90m 14[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimCensusServiceImpl.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 13[2mms[22m[39m
 [32m✓[39m test/policy-service/ClaimDentalPolicyServiceImpl.test.ts [2m([22m[2m29 tests[22m[2m)[22m[90m 24[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimCustomerRelationshipsServiceImpl.test.ts [2m([22m[2m11 tests[22m[2m)[22m[90m 20[2mms[22m[39m
 [32m✓[39m test/siderbar-service/task-service/DXPUserTaskService.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 8[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimOrganiztionCustomerServiceImpl.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 14[2mms[22m[39m
 [32m✓[39m test/siderbar-service/efolder-service/DXPEfolderService.test.ts [2m([22m[2m11 tests[22m[2m)[22m[90m 16[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimProviderServiceImpl.test.ts [2m([22m[2m16 tests[22m[2m)[22m[90m 17[2mms[22m[39m
 [32m✓[39m test/customer-service/ClaimIndividualCustomerServiceImpl.test.ts [2m([22m[2m8 tests[22m[2m)[22m[90m 13[2mms[22m[39m
 [32m✓[39m test/siderbar-service/task-service/DXPCaseService.test.ts [2m([22m[2m10 tests[22m[2m)[22m[90m 16[2mms[22m[39m
 [32m✓[39m test/siderbar-service/task-service/DXPQueueService.test.ts [2m([22m[2m9 tests[22m[2m)[22m[90m 12[2mms[22m[39m
 [32m✓[39m test/siderbar-service/bam-service/DXPActivityService.test.ts [2m([22m[2m6 tests[22m[2m)[22m[90m 14[2mms[22m[39m
 [32m✓[39m test/siderbar-service/note-service/DXPNoteService.test.ts [2m([22m[2m35 tests[22m[2m)[22m[90m 20[2mms[22m[39m

[2m Test Files [22m [1m[32m14 passed[39m[22m[90m (14)[39m
[2m      Tests [22m [1m[32m182 passed[39m[22m[90m (182)[39m
[2m   Start at [22m 09:33:33
[2m   Duration [22m 7.14s[2m (transform 1.70s, setup 55.74s, collect 3.58s, tests 214ms, environment 23.03s, prepare 4.26s)[22m

