{"name": "@eisgroup/dental-product", "version": "25.7.0-SNAPSHOT.202508120630", "description": "Dental Product Module", "license": "UNLICENSED", "publishConfig": {"registry": "https://genesis-npm-release.exigengroup.com/repository/genesis-npm-release/"}, "main": "target/dist/js/src/index.js", "typings": "target/dist/definitions/src/index.d.ts", "files": ["target/dist/"], "dependencies": {"@eisgroup/cap-gateway-client": "25.10.0-SNAPSHOT.202507152214", "@eisgroup/cap-models": "25.10.0-SNAPSHOT.202507152214", "@eisgroup/dental-dxp-gateway-api": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/dental-core": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/claim-dental-policy-service-api": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/claim-dental-customer-service-api": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/dental-models": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/dental-product-services": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/builder": "25.8.0-SNAPSHOT.202507152214", "@eisgroup/builder-integration": "25.8.0-SNAPSHOT.202507152214", "@eisgroup/form": "100.0.0", "@eisgroup/form-core": "100.0.0", "@eisgroup/models-api": "100.0.0", "@eisgroup/kraken-form-api": "100.0.0", "@eisgroup/kraken-form-runtime": "100.0.0", "@eisgroup/dispatch": "100.0.0", "@eisgroup/cache": "100.0.0", "@eisgroup/data.either": "100.0.0", "@eisgroup/ui-temporals": "100.0.0", "@eisgroup/form-kraken-core": "25.8.0-202506231223", "@eisgroup/kraken-core": "25.8.0-202506231223", "@eisgroup/i18n": "100.0.0", "@eisgroup/ioc": "100.0.0", "@eisgroup/auth": "100.0.0", "@eisgroup/sidebar-service-api": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/ui-kit-icons": "^100.0.0", "@eisgroup/common-policy-shares": "25.10.0-SNAPSHOT.202507152214", "@eisgroup/bam-ui": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/workium-ui": "25.10.0-SNAPSHOT.202507212214", "classnames": "2.2.6", "data.either": "~1.3.0", "downloadjs": "^1.4.7", "history": "~3.3.0", "inversify": "^3.3.0", "lodash": "~4.17.15", "mobx": "4.15.4", "mobx-react": "^6.1.8", "moment": "~2.29.4", "object-path": "^0.11.4", "react-router": "3.2.3", "rxjs": "5.5.7", "single-spa-react": "^2.12.0", "final-form-arrays": "3.0.2"}, "devDependencies": {"@eisgroup/infra-scripts": "4.0.6", "@testing-library/react": "^10.0.0", "@types/single-spa-react": "^2.12.0", "cross-env": "^6.0.3"}, "peerDependencies": {"@eisgroup/auth": "100.0.0", "@eisgroup/common": "100.0.0", "@eisgroup/common-types": "100.0.0", "@eisgroup/dispatch": "100.0.0", "@eisgroup/form": "100.0.0", "@eisgroup/i18n": "100.0.0", "@eisgroup/ioc": "100.0.0", "@eisgroup/lookups": "100.0.0", "@eisgroup/react-application": "100.0.0", "@eisgroup/react-components": "100.0.0", "@eisgroup/react-routing": "100.0.0", "@eisgroup/ui-kit": "100.0.0", "react": "^16.8.6", "react-dom": "^16.8.6"}, "scripts": {"build": "tsc --project tsconfig.build.json", "postbuild": "cpx \"./src/**/*.{json,less,ico,html,gif,svg,png,ttf,woff,woff2,js,jsx}\" ./target/dist/js/src", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "lint": "infra-scripts lint --eslint", "test": "vitest run --passWithNoTests", "test:watch": "vitest"}}