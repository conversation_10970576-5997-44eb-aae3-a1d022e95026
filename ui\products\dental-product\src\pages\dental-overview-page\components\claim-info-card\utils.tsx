/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'

import {IndividualCustomer} from '@eisgroup/claim-dental-customer-service-api'
import {PolicyState} from '@eisgroup/common-policy-shares'
import {opt} from '@eisgroup/common-types'
import {CapDentalLoss, CapDentalPaymentDefinition, CapDentalSettlement} from '@eisgroup/dental-models'
import {t} from '@eisgroup/i18n'
import {LookupLabel} from '@eisgroup/react-components'
import {Badge} from '@eisgroup/ui-kit'

import type {DentalIndividualPolicy} from '@eisgroup/claim-dental-policy-service-api'

import {InfoCardSection, RelativeSpinner} from '../../../../shared/components'
import {goToCustomerPortfolioHome} from '../../../../utils/common/urls'
import {formatDate, moneyByLocale} from '../../../../utils/helpers'
import {formCustomerFullName} from '../../../../utils/helpers/customers'
import {EntityLink} from '../../../../utils/helpers/entities'
import {PaymentStatus} from '../../../../utils/types/enums'
import {BADGE_STATUS_TYPE} from '../../../../utils/types/types'
import {DentalClaimParties} from '../../store/DentalOverviewPageStore'
import {DentalState} from '../claim-header/dental-state/DentalState'
import {PolicyHolder} from '../claim-header/policy-holder/PolicyHolder'

import CapDentalLossEntity = CapDentalLoss.CapDentalLossEntity
import CapDentalSettlementEntity = CapDentalSettlement.CapDentalSettlementEntity
import CapDentalResultEntryEntity = CapDentalSettlement.CapDentalResultEntryEntity
import CapDentalPaymentEntity = CapDentalPaymentDefinition.CapDentalPaymentEntity

export function mapInfoCardSectionsFromLoss(
    loss: CapDentalLossEntity,
    settlement: CapDentalSettlementEntity | null,
    payments: CapDentalPaymentEntity[],
    parties: DentalClaimParties,
    dnIndividual?: DentalIndividualPolicy,
    productName?: string
): InfoCardSection[] {
    const charges = settlement?.settlementResult?.entries
        ? moneyByLocale(calculateTotalCharges(opt(settlement?.settlementResult?.entries).orElse([])))
        : t('dental-product:not_available')
    const procedure = opt(loss?.lossDetail?.submittedProcedures)
        .orElse([])
        .sort((a, b) =>
            b.dateOfService && a.dateOfService
                ? new Date(a.dateOfService).getTime() - new Date(b.dateOfService).getTime()
                : 0
        )?.[0]
    const totalPaid = moneyByLocale(calculateTotalPaid(opt(payments).orElse([])))
    return [
        {
            title: t('dental-product:dental-claim-info-card-status'),
            content: (
                <DentalState
                    dentalStatus={loss?.state}
                    dentalSubStatusCd={loss?.lossSubStatusCd}
                    reasonCd={loss?.reasonCd}
                />
            )
        },
        {
            title: t('dental-product:dental-claim-info-card-policyholder'),
            content: (
                <PolicyHolder
                    policyHolder={parties.policyholder as IndividualCustomer}
                    onViewDetail={() => goToCustomerPortfolioHome(parties.policyholder?.customerNumber)}
                />
            )
        },
        {
            title: t('dental-product:dental-claim-info-card-policy'),
            content: (
                <a
                    href={loss?.policy?.policyNumber ? `/pas/${loss?.policy?.policyNumber}` : ''}
                    target='_blank'
                    rel='noreferrer'
                >
                    {opt(loss?.policy?.policyNumber).orElse(t('dental-product:not_available'))}
                </a>
            ),
            contentSecondary: (
                <Badge
                    status={getPolicyBadgeStatus(loss?.policy?.policyStatus as PolicyState)}
                    text={getPolicyDisplayStatus(loss?.policy?.policyStatus)}
                />
            )
        },
        {
            title: t('dental-product:dental-claim-info-card-patient'),
            content: opt(parties.patient)
                .map(customer => formCustomerFullName(customer))
                .orElse(t('dental-product:not_available')),
            contentSecondary: (
                <LookupLabel
                    lookup='DNIndividualRelationshipToPrimaryInsuredCd'
                    code={getRelationshipFromPolicy(parties.patient?._key?.rootId, dnIndividual)}
                    emptyLabel={t('dental-product:not_available')}
                />
            )
        },
        {
            title: t('dental-product:dental-claim-info-card-product-plan'),
            content: opt(productName).orElse(t('dental-product:not_available')),
            contentSecondary: opt(loss?.policy?.planName).orElse(t('dental-product:not_available'))
        },
        {
            title: t('dental-product:dental-claim-info-card-received-date'),
            content: opt(formatDate(loss?.lossDetail?.claimData?.receivedDate)).orElse(
                t('dental-product:not_available')
            )
        },
        {
            title: t('dental-product:dental-claim-info-card-date-of-service'),
            content: opt(formatDate(procedure?.dateOfService)).orElse(t('dental-product:not_available'))
        },
        {
            title: t('dental-product:dental-claim-info-card-source'),
            content: (
                <LookupLabel
                    lookup='CapDNClaimSource'
                    code={loss?.lossDetail?.claimData?.source}
                    emptyLabel={t('dental-product:not_available')}
                />
            )
        },
        {
            title: t('dental-product:dental-claim-info-card-claim-type'),
            content: (
                <LookupLabel
                    lookup='CapDNTransactionType'
                    code={loss?.lossDetail?.claimData?.transactionType}
                    emptyLabel={t('dental-product:not_available')}
                />
            )
        },
        {
            title: t('dental-product:dental-claim-info-card-payee-type'),
            content: (
                <LookupLabel
                    lookup='CapDNPayeeType'
                    code={loss?.lossDetail?.claimData?.payeeType}
                    emptyLabel={t('dental-product:not_available')}
                />
            )
        },
        {
            title: t('dental-product:dental-claim-info-card-total-charges'),
            content: settlement ? charges : <RelativeSpinner />
        },
        {
            title: t('dental-product:dental-claim-info-card-total-paid'),
            content: payments ? totalPaid : <RelativeSpinner />
        }
    ]
}

function calculateTotalCharges(entries: CapDentalResultEntryEntity[]): number {
    return entries.reduce((acc, entry) => opt(entry.calculationResult?.charge?.amount).orElse(0) + acc, 0)
}

function calculateTotalPaid(payments: CapDentalPaymentEntity[]): number {
    return payments
        .filter(payment => payment.state === PaymentStatus.Issued)
        .map(item => item.paymentNetAmount)
        .filter(Boolean)
        .reduce((sum, item) => sum + opt(item?.amount).orElse(0), 0)
}

function getPolicyBadgeStatus(policyState?: PolicyState): BADGE_STATUS_TYPE {
    switch (policyState) {
        case PolicyState.Active:
        case PolicyState.Issued:
        case PolicyState.CancellationPending:
        case PolicyState.Pending:
            return 'success'
        case PolicyState.Cancelled:
        case PolicyState.Expired:
            return 'error'
        default:
            return 'warning'
    }
}

function getPolicyDisplayStatus(policyState?: string) {
    switch (policyState) {
        case PolicyState.Active:
        case PolicyState.Issued:
        case PolicyState.CancellationPending:
        case PolicyState.Pending:
            return 'Active'
        case PolicyState.Cancelled:
        case PolicyState.Expired:
            return 'Inactive'
        default:
            return policyState
    }
}

const getRelationshipFromPolicy = (orgRootId?: string, dnIndividual?: DentalIndividualPolicy) => {
    const parties = dnIndividual?.parties
    const insureds = dnIndividual?.insureds
    const personParty = parties?.filter(party => {
        const partyUriRootId = EntityLink.from(party.partyInfo?.[0]?.personBaseDetails?.registryTypeId!).rootId
        return partyUriRootId === orgRootId
    })[0]
    const resInsured = insureds?.filter(insured => insured.insuredInfo?._ref === personParty?._key?.id)[0]

    return resInsured ? RelationshipMap[resInsured.insuredRoleNameCd!] : null
}

const RelationshipMap = {
    PrimaryInsured: 'Self',
    SpouseInsured: 'SpouseDomesticPartner',
    ChildInsured: 'DependentChild'
}
