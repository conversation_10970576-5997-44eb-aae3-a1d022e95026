import React, {useState} from 'react'
import {observer} from 'mobx-react'

import {MoneyFormat} from '@eisgroup/dental-core'
import {CapDentalBalanceChangeLog} from '@eisgroup/dental-models'
import {useTranslate} from '@eisgroup/i18n'
import {LookupLabel} from '@eisgroup/react-components'
import {Collapse, ColumnProps, Table, Tooltip} from '@eisgroup/ui-kit'

import {formatDate} from '../../../../../utils/helpers'
import {
    BALANCE_ACTIVITIES,
    BALANCE_ACTIVITIES_COLUMN_DESC,
    BALANCE_ACTIVITIES_TABLE,
    BALANCE_DESCRIPTION_ELLIPSIS
} from './classnames'

import CapDentalBalanceChangeLogEntity = CapDentalBalanceChangeLog.CapDentalBalanceChangeLogEntity

const {Panel} = Collapse

interface BalanceActivitiesProps {
    activities: CapDentalBalanceChangeLogEntity[]
    loading?: boolean
}

export const BalanceActivities: React.FC<BalanceActivitiesProps> = observer(({activities, loading}) => {
    const [isExpanded, setIsExpanded] = useState(true)

    const {t} = useTranslate()

    const columns: ColumnProps<CapDentalBalanceChangeLogEntity>[] = [
        {
            title: t('dental-product:dental-claim-balance-balance-transaction-date'),
            dataIndex: 'creationDate',
            key: 'creationDate',
            width: '10%',
            render: (text: string, record: CapDentalBalanceChangeLogEntity) => formatDate(record.creationDate as Date)
        },
        {
            title: t('dental-product:dental-claim-balance-balance-transaction-id'),
            dataIndex: 'transactionId',
            key: 'transactionId',
            width: '10%',
            render: (text: string, record: CapDentalBalanceChangeLogEntity) => {
                return (
                    <span>
                        {record.balanceChangeLogDetails?.transactionNumber ?? t('dental-product:not_available')}
                    </span>
                )
            }
        },
        {
            title: t('dental-product:dental-claim-balance-balance-transaction-type'),
            dataIndex: 'transactionType',
            key: 'transactionType',
            width: '10%',
            render: (text: string, record: CapDentalBalanceChangeLogEntity) => {
                return (
                    <span>
                        <LookupLabel
                            lookup='CapDNBalanceLogTransactionType'
                            code={record.balanceChangeLogDetails?.transactionTypeCd}
                            emptyLabel={t('dental-product:not_available')}
                        />
                    </span>
                )
            }
        },
        {
            title: t('dental-product:dental-claim-balance-balance-transaction-amount'),
            dataIndex: 'transactionAmount',
            key: 'transactionAmount',
            align: 'right',
            width: '20%',
            render: (text: string, record: CapDentalBalanceChangeLogEntity) => {
                const currentBalanceAmount = record.balanceChangeLogDetails?.totalBalanceAmount?.amount || 0
                const currentIndex = activities.findIndex(v => v._key.rootId === record._key.rootId)
                if (currentIndex === -1) {
                    return t('dental-product:not_available')
                }
                const previousIndex = currentIndex + 1
                const previousBalance = activities[previousIndex] || {}
                const previousBalanceAmount = previousBalance?.balanceChangeLogDetails?.totalBalanceAmount?.amount || 0
                const transactionAmount = currentBalanceAmount - previousBalanceAmount
                return transactionAmount === 0 ? (
                    0
                ) : (
                    <MoneyFormat value={currentBalanceAmount - previousBalanceAmount} />
                )
            }
        },
        {
            title: t('dental-product:dental-claim-balance-balance-transaction-description'),
            dataIndex: 'description',
            key: 'description',
            width: '10%',
            className: BALANCE_ACTIVITIES_COLUMN_DESC,
            render: (text: string, record: CapDentalBalanceChangeLogEntity) => {
                return (
                    <Tooltip title={record.balanceChangeLogDetails?.description}>
                        <div className={BALANCE_DESCRIPTION_ELLIPSIS}>
                            {record.balanceChangeLogDetails?.description}
                        </div>
                    </Tooltip>
                )
            }
        },
        {
            title: t('dental-product:dental-claim-balance-balance-transaction-total-balance'),
            dataIndex: 'totalBalance',
            key: 'totalBalance',
            width: '20%',
            align: 'right',
            render: (text: string, record: CapDentalBalanceChangeLogEntity) => {
                const currentBalanceAmount = record.balanceChangeLogDetails?.totalBalanceAmount?.amount || 0
                return currentBalanceAmount === 0 ? 0 : <MoneyFormat value={currentBalanceAmount} />
            }
        }
    ]

    const filterDataSource = activities.filter((item, index, arr) => {
        return index !== arr.length - 1
            ? arr[index].balanceChangeLogDetails?.totalBalanceAmount?.amount !==
                  arr[index + 1].balanceChangeLogDetails?.totalBalanceAmount?.amount
            : item.balanceChangeLogDetails?.totalBalanceAmount?.amount !== 0
    })

    return (
        <div className={BALANCE_ACTIVITIES}>
            <Collapse
                bordered={false}
                activeKey={isExpanded ? ['1'] : []}
                onChange={() => setIsExpanded(!isExpanded)}
                isWhite
            >
                <Panel header={t('dental-product:dental-claim-balance-balance-activities-title')} key='1'>
                    <Table
                        className={BALANCE_ACTIVITIES_TABLE}
                        columns={columns}
                        dataSource={filterDataSource ?? []}
                        pagination={{
                            size: 'small',
                            defaultPageSize: 5,
                            pageSizeOptions: ['5', '10', '15', '20', '25'],
                            showQuickJumper: true,
                            showSizeChanger: true,
                            total: filterDataSource.length
                        }}
                        size='small'
                        loading={loading}
                    />
                </Panel>
            </Collapse>
        </div>
    )
})
