$ tsc --project tsconfig.build.json  --emitDeclarationOnly
$ cross-env BABEL_ENV=build node ../../node_modules/@babel/cli/bin/babel.js src --out-dir target/dist/js/src --source-maps --extensions .js,.jsx,.ts,.tsx --no-comments --config-file @eisgroup/infra-scripts/config/models-babel.config
[BABEL] Note: The code generator has deoptimised the styling of D:\ProgramData\ms-claim-benefits-dental\ui\models\dental-claim-ms-api\src\generated\Search\api.ts as it exceeds the max of 500KB.
Successfully compiled 18 files with Babe<PERSON> (39861ms).
