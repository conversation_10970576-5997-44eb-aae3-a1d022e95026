/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {ProviderWithCustomerPartyRole} from '@eisgroup/claim-dental-customer-service-api'
import {DentalIndividualPolicy} from '@eisgroup/claim-dental-policy-service-api'

import {CUSTOMER, CustomerTypes, PROVIDER, ProviderTypes, UUID_V4_REGEX} from '../common/constants'

interface GerootUriParams {
    rootId: string
    modelName: string
    namespace: string
}

export function formGerootUri({rootId, modelName, namespace}: GerootUriParams): string {
    return `geroot://${namespace}/${modelName}//${rootId}`
}

export function formGerootUriIndividualCustomer(rootId: string): string {
    return formGerootUri({rootId, modelName: CustomerTypes.INDIVIDUALCUSTOMER, namespace: CUSTOMER})
}

export function formGerootUriIndividualProvider(rootId: string): string {
    return formGerootUri({rootId, modelName: ProviderTypes.INDIVIDUALCUSTOMER, namespace: PROVIDER})
}

export function formGerootUriOrganizationProvider(rootId: string): string {
    return formGerootUri({rootId, modelName: ProviderTypes.ORGANIZATIONCUSTOMER, namespace: PROVIDER})
}

export const formGerootUriProvider = (provider: ProviderWithCustomerPartyRole) => {
    const providerType = provider?.provider?._modelName
    const providerRootId = provider?.provider?._key?.rootId
    if (providerRootId && providerType === ProviderTypes.INDIVIDUALCUSTOMER) {
        return formGerootUriIndividualProvider(providerRootId)
    }
    if (providerRootId && providerType === ProviderTypes.ORGANIZATIONCUSTOMER) {
        return formGerootUriOrganizationProvider(providerRootId)
    }
    return ''
}

export function isUUID(str: string): boolean {
    return UUID_V4_REGEX.test(str)
}

export class EntityLink {
    static readonly linkRegexp =
        /(?<protocol>\w+):\/\/(?<baseType>\w+)\/(?<modelName>\w+)\/(?<variation>\w+)?\/(?<rootId>[-\w]+)\/?(?<revisionNo>\d+)?\/?(?<parentId>[-\w]+)?\/?(?<id>[-\w]+)?/

    protocol: string

    baseType: string

    modelName: string

    variation: string

    rootId: string

    revisionNo: string

    parentId: string

    id: string

    private constructor(
        protocol: string,
        baseType: string,
        modelName: string,
        variation: string,
        rootId: string,
        revisionNo: string,
        parentId: string,
        id: string
    ) {
        this.protocol = protocol
        this.baseType = baseType
        this.modelName = modelName
        this.variation = variation
        this.rootId = rootId
        this.revisionNo = revisionNo
        this.parentId = parentId
        this.id = id
    }

    /**
     * Parses EntityLink from string.
     * @param link link in format
     * {protocol}://{baseType}/{modelName}/{variation?}/{rootId}/{revisionNo?}/{parentId?}/{id?}
     * @returns EntityLink object
     * @throws error if link is empty or unresolvable
     */
    static from = (link: string): EntityLink => {
        if (!link) {
            throw new Error('link is required!')
        }
        const result = EntityLink.linkRegexp.exec(link)
        if (!result || !result.groups) {
            throw new Error('link format is not supported!')
        }
        const {groups} = result
        return new EntityLink(
            groups.protocol,
            groups.baseType,
            groups.modelName,
            groups.variation || '',
            groups.rootId,
            groups.revisionNo || '',
            groups.parentId || '',
            groups.id || ''
        )
    }

    static isValid = (link?: string): boolean => Boolean(link) && EntityLink.linkRegexp.test(link!)
}

export const getInsuredUrisFromPolicy = (policy: DentalIndividualPolicy): string[] => {
    return (
        policy.insureds
            ?.map(insured => {
                // for unverifypolicy
                if (!policy.parties && insured.registryTypeId) {
                    return insured.registryTypeId
                }
                return (
                    policy.parties?.find(person => person._key?.id === insured.insuredInfo?._ref)?.partyInfo?.[0]
                        ?.personBaseDetails?.registryTypeId || ''
                )
            })
            .filter(Boolean) || []
    )
}

export const getInsuredNamesFromPolicy = (policy?: DentalIndividualPolicy): string[] => {
    if (!policy) {
        return []
    }
    return (
        policy.insureds
            ?.map(insured => {
                const personBaseDetails = policy.parties?.find(person => person._key?.id === insured.insuredInfo?._ref)
                    ?.partyInfo?.[0]?.personBaseDetails
                if (!personBaseDetails) {
                    return ''
                }
                return `${personBaseDetails.firstName} ${personBaseDetails.lastName}`
            })
            .filter(Boolean) || []
    )
}
