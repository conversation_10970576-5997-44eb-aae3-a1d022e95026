/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {beforeEach, describe, expect, it, vi} from 'vitest'

import {
    activatePaymentSchedule,
    checkPaymentSchedule,
    getPayments,
    getPaymentSchedules
} from '../../../../../src/pages/dental-overview-page/components/financials-tab/paymentServiceUtils'

// Hoisted mock functions
const mockCallService = vi.hoisted(() => vi.fn())
const mockLoadPayments = vi.hoisted(() => vi.fn())
const mockLoadPaymentSechdule = vi.hoisted(() => vi.fn())
const mockActivatePaymentSchedule = vi.hoisted(() => vi.fn())
const mockVerifyPaymentScheduleActivation = vi.hoisted(() => vi.fn())
const mockCancelPaymentSchedule = vi.hoisted(() => vi.fn())

// Mock environment to prevent claimDentalMsPath errors
vi.mock('@eisgroup/environment', () => ({
    environment: {
        dxp: {
            claimDentalMsPath: 'http://localhost:8080/dental-ms',
            capDxpPath: 'http://localhost:8080/cap-dxp'
        }
    }
}))

// Mock dental financial service
vi.mock('@eisgroup/dental-product-services', () => ({
    dentalFinancialService: {
        loadPayments: mockLoadPayments,
        loadPaymentSechdule: mockLoadPaymentSechdule,
        activatePaymentSchedule: mockActivatePaymentSchedule,
        verifyPaymentScheduleActivation: mockVerifyPaymentScheduleActivation,
        cancelPaymentSchedule: mockCancelPaymentSchedule
    }
}))

// Mock BaseRootStoreImpl
vi.mock('../../../../../src/shared/stores', () => ({
    BaseRootStoreImpl: vi.fn(() => ({
        callService: mockCallService
    }))
}))

describe('paymentServiceUtils', () => {
    beforeEach(() => {
        vi.clearAllMocks()
    })

    describe('getPayments', () => {
        it('should call dentalFinancialService.loadPayments with correct search query', async () => {
            const mockResult = {result: [{id: 'payment1'}]}
            const mockServiceResult = Promise.resolve(mockResult)

            mockCallService.mockResolvedValue(mockResult)
            mockLoadPayments.mockReturnValue(mockServiceResult)

            const dentalLossSource = 'test-loss-source'
            const result = await getPayments(dentalLossSource)

            expect(mockLoadPayments).toHaveBeenCalledWith({
                originSource: {
                    matches: [dentalLossSource]
                }
            })
            expect(mockCallService).toHaveBeenCalledWith(mockServiceResult)
            expect(result).toBe(mockResult)
        })

        it('should handle errors from service call', async () => {
            const mockError = new Error('Service error')
            mockCallService.mockRejectedValue(mockError)
            mockLoadPayments.mockReturnValue(Promise.resolve())

            const dentalLossSource = 'test-loss-source'

            await expect(getPayments(dentalLossSource)).rejects.toThrow('Service error')
        })
    })

    describe('getPaymentSchedules', () => {
        it('should call dentalFinancialService.loadPaymentSechdule with correct search query', async () => {
            const mockResult = {result: [{id: 'schedule1'}]}
            const mockServiceResult = Promise.resolve(mockResult)

            mockCallService.mockResolvedValue(mockResult)
            mockLoadPaymentSechdule.mockReturnValue(mockServiceResult)

            const dentalLossSource = 'test-loss-source'
            const result = await getPaymentSchedules(dentalLossSource)

            expect(mockLoadPaymentSechdule).toHaveBeenCalledWith({
                originSource: {
                    matches: [dentalLossSource]
                }
            })
            expect(mockCallService).toHaveBeenCalledWith(mockServiceResult)
            expect(result).toBe(mockResult)
        })

        it('should handle errors from service call', async () => {
            const mockError = new Error('Service error')
            mockCallService.mockRejectedValue(mockError)
            mockLoadPaymentSechdule.mockReturnValue(Promise.resolve())

            const dentalLossSource = 'test-loss-source'

            await expect(getPaymentSchedules(dentalLossSource)).rejects.toThrow('Service error')
        })
    })

    describe('activatePaymentSchedule', () => {
        it('should call dentalFinancialService.activatePaymentSchedule with correct parameters', async () => {
            const mockResult = {success: true}
            const mockServiceResult = Promise.resolve(mockResult)

            mockCallService.mockResolvedValue(mockResult)
            mockActivatePaymentSchedule.mockReturnValue(mockServiceResult)

            const paymentScheduleKey = {
                rootId: 'test-payment-key',
                revisionNo: 1
            }
            const result = await activatePaymentSchedule(paymentScheduleKey)

            expect(mockActivatePaymentSchedule).toHaveBeenCalledWith({
                rootId: 'test-payment-key',
                revisionNo: 1
            })
            expect(mockCallService).toHaveBeenCalledWith(mockServiceResult)
            expect(result).toBe(mockResult)
        })

        it('should handle errors from service call', async () => {
            const mockError = new Error('Activation failed')
            mockCallService.mockRejectedValue(mockError)
            mockActivatePaymentSchedule.mockReturnValue(Promise.resolve())

            const paymentScheduleKey = {
                rootId: 'test-payment-key',
                revisionNo: 1
            }

            await expect(activatePaymentSchedule(paymentScheduleKey)).rejects.toThrow('Activation failed')
        })
    })

    describe('integration tests', () => {
        it('should use the same baseStore instance for all service calls', async () => {
            const mockResult = {result: 'test'}
            mockCallService.mockResolvedValue(mockResult)
            mockLoadPayments.mockReturnValue(Promise.resolve())

            await getPayments('test-source')

            expect(mockCallService).toHaveBeenCalledTimes(1)
        })
    })
})
