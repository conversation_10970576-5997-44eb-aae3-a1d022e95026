// tslint:disable
import { Localization } from '@eisgroup/i18n'

export const enMT: Localization.ResourceBundle = {
    locale : { country : 'MT' , language : 'en' } ,
    version : 1 ,
    ns : 'rules',
    resources : {
        'MandatoryPatientRoleCd': 'MT+Patient roleCd is required.',
        'AssertServiceOverideAllowed': 'MT+Service override isAllowed cannot be used and the same time with isDenied override.',
        'number-set-max-error': 'MT+Value must be {{0}} or smaller',
        'ReceivedDateCannotBeInFuture': 'MT+Received Date cannot be in the future.',
        'ProcedureQuantityMax': 'MT+Quantity must not be greater than 99.',
        'MandatoryClaimDiscountName': 'MT+discountName is mandatory.',
        'MandatoryClaimPayeeType': 'MT+Payee Type is mandatory.',
        'ConsideredCannotBeNegative': 'MT+cob.considered cannot be negative.',
        'AssertClaimOverideAllowed': 'MT+Claim override isAllowed cannot be used and the same time with isDenied override.',
        'CleanClaimDateCannotBeBeforeFutureDate': 'MT+cleanClaimDate cannot be in the future.',
        'rule-mandatory-empty-error': 'MT+Field must be empty',
        'MandatoryPatientRegistryId': 'MT+Patient is required.',
        'MandatoryClaimPatient': 'MT+Patient is mandatory.',
        'MandatoryHistoryClaimPatient': 'MT+patient is mandatory.',
        'ClaimOverridePreventiveWaitingPeriodHigherThanZero': 'MT+overridePreventiveWaitingPeriod has to be greater than 0.',
        'SuspendLossOnlyAvailableForOrtho': 'MT+suspendLoss command cannot be performed if transactionType is not OrthodonticServices.',
        'DateOfServiceCannotBeAfterReceivedDateWhenCleanClaimDateIsBlank': 'MT+Date of Service cannot be after the Received Date.',
        'ServiceOverrideCoveredAmountEqualOrHigherThanZero': 'MT+overrideCoveredAmount cannot be negative.',
        'ClaimOverrideOrthoWaitingPeriodHigherThanZero': 'MT+overrideOrthoWaitingPeriod has to be greater than 0.',
        'ProcedureQuantityMin': 'MT+Quantity has to be equal or more than 1.',
        'ServiceOverridePaymentInterestDaysEqualOrHigherThanZero': 'MT+Service overridePaymentInterestDays cannot be negative.',
        'MandatoryHistoryDOSDateIfActualServices': 'MT+DOSDate is mandatory when isPredet is not true.',
        'MandatoryClaimPolicyHolder': 'MT+Policy Holder is mandatory.',
        'ServiceOverrideReplacementLimitHigherThanZero': 'MT+overrideReplacementLimit has to be greater than 0.',
        'MandatoryClaimReceivedDate': 'MT+Received Date is mandatory.',
        'ServiceOverrideGracePeriodEqualOrHigherThanZero': 'MT+Service overrideGracePeriod cannot be negative.',
        'MandatoryClaimProviderFeeType': 'MT+providerFee.type is mandatory.',
        'CapDentalPaymentDetailsEntity-001': 'MT+payeeDetails are required.',
        'ServiceOverrideMaximumAmountEqualOrHigherThanZero': 'MT+overrideMaximumAmount cannot be negative.',
        'ServiceOverrideServiceWaitingPeriodHigherThanZero': 'MT+overrideServiceWaitingPeriod has be to greater than 0.',
        'rule-regexp-error': 'MT+Field must match regular expression pattern: {{0}}',
        'rule-length-error': 'MT+Text must not be longer than {{0}}',
        'ClaimOverridePaymentInterestDaysEqualOrHigherThanZero': 'MT+Claim overridePaymentInterestDays cannot be negative.',
        'MandatoryClaimURI': 'MT+A claimLossIdentification link to Claim is mandatory.',
        'MandatoryClaimDiscountPercentage': 'MT+discountPercentage is mandatory.',
        'ClaimOverridePaymentInterestAmountEqualOrHigherThanZero': 'MT+Claim overridePaymentInterestAmount cannot be negative.',
        'MandatoryClaimProvider': 'MT+Provider is mandatory when the isUnknownOrIntProvider flag is not true.',
        'MandatoryAlternatePayeeRegistryId': 'MT+AlternatePayee registryId is required.',
        'AssertClaimOverideDenied': 'MT+Claim isDenied cannot be used at the same time with isAllowed override.',
        'MandatoryClaimProviderFee': 'MT+providerFee.fee is mandatory.',
        'DiscountAmountCannotBeNegative': 'MT+discountAmount cannot be negative.',
        'MandatoryAuthorizationBy': 'MT+authorizationBy is mandatory.',
        'MandatorySubmittedFee': 'MT+Charges is required.',
        'MandatoryPolicyholderRegistryId': 'MT+Policyholder registryId is required.',
        'AssertServiceOverideDenied': 'MT+Service override isDenied cannot be used at the same time with isAllowed override.',
        'MandatoryClaimSource': 'MT+Source for the claim is mandatory.',
        'MandatoryOrthodonticFrequency': 'MT+Payment Frequency is required.',
        'MandatoryProcedureCode': 'MT+Procedure Code is required.',
        'MandatoryDiagnosisQualifier': 'MT+Diagnosis List Qualifier is required.',
        'SubmittedFeeCannotBeNegative': 'MT+Charges cannot be a negative value.',
        'ServiceOverrideCopayAmountEqualOrHigherThanZero': 'MT+overrideCopayAmount cannot be negative.',
        'MandatoryAlternatePayee': 'MT+alternatePayee is mandatory.',
        'rule-assertion-error': 'MT+Assertion failed',
        'number-set-min-max-step-error': 'MT+Value must be in interval between {{0}} and {{1}} inclusively with increment {{2}}',
        'MandatoryPolicyholderRoleCd': 'MT+Policyholder roleCd is required.',
        'ClaimOverrideMajorWaitingPeriodHigherThanZero': 'MT+overrideMajorWaitingPeriod has to be greater than 0.',
        'CapDentalBalanceChangeLogEntity-001': 'MT+originSource is required.',
        'PaidCannotBeNegative': 'MT+cob.paid cannot be negative.',
        'CapDentalBalanceChangeLogEntity-002': 'MT+payee is required.',
        'number-set-max-step-error': 'MT+Value must be {{0}} or smaller with decrement {{1}}',
        'DownPaymentCannotBeNegative': 'MT+Down Payment cannot be a negative value.',
        'MandatoryClaimDiscountType': 'MT+discountType is mandatory.',
        'ServiceOverrideCoinsurancePctEqualOrHigherThanZero': 'MT+overrideCoinsurancePct cannot be negative.',
        'ServiceOverrideConsideredAmountEqualOrHigherThanZero': 'MT+overrideConsideredAmount cannot be negative.',
        'ServiceOverrideServiceFrequencyLimitHigherThanZero': 'MT+overrideServiceFrequencyLimit has to be greater than 0.',
        'AuthorizationPeriodValidation': 'MT+authorizationPeriod.endDate must be later than authorizationPeriod.startDate.',
        'MandatoryClaimDiscountAmount': 'MT+discountAmount is mandatory.',
        'MandatoryProcedureDateOfService': 'MT+Date of Service is required.',
        'AllowedCannotBeNegative': 'MT+cob.allowed cannot be negative.',
        'MandatoryActualOrPredeterminationActualServicesForClaim': 'MT+Submitted Procedures Details are required.',
        'CapDentalPaymentPayeeDetailsEntity-001': 'MT+payee is required.',
        'CleanClaimDateCannotBeBeforeReceivedDate': 'MT+cleanClaimDate cannot be before the receivedDate.',
        'number-set-min-step-error': 'MT+Value must be {{0}} or larger with increment {{1}}',
        'ServiceOverridePatientResponsibilityEqualOrHigherThanZero': 'MT+overridePatientResponsibility cannot be negative.',
        'MandatoryClaimTransactionType': 'MT+Claim Type is mandatory.',
        'value-list-error': 'MT+Value must be one of: {{0}}',
        'rule-size-error': 'MT+Invalid collection size',
        'MandatoryOneOrthoServiceForClaim': 'MT+ortho details are required and only one Ortho service can be provided.',
        'rule-mandatory-error': 'MT+Field is mandatory',
        'rule-size-range-error': 'MT+Invalid collection size',
        'MandatoryHistoryCdtCoveredCd': 'MT+cdtCoveredCd is mandatory.',
        'number-set-min-max-error': 'MT+Value must be in interval between {{0}} and {{1}} inclusively',
        'ClaimOverrideLateEntrantWaitingPeriodHigherThanZero': 'MT+Claim overrideLateEntrantWaitingPeriod has to be greater than 0.',
        'CapDentalBalanceEntity-002': 'MT+payee is required.',
        'DateOfServiceCannotBeInFuture': 'MT+Date of Service cannot be future date.',
        'ServiceOverridePaymentInterestAmountEqualOrHigherThanZero': 'MT+Service overridePaymentInterestAmount cannot be negative.',
        'MandatoryProviderRegistryId': 'MT+Provider registryId is required.',
        'number-set-min-error': 'MT+Value must be {{0}} or larger',
        'MandatoryClaimPartyRoleCd': 'MT+Roles is required.',
        'ServiceOverrideLateEntrantWaitingPeriodHigherThanZero': 'MT+Service overrideLateEntrantWaitingPeriod has to be greater than 0.',
        'DateOfServiceCannotBeAfterCleanClaimDateWhenCleanClaimDateIsSet': 'MT+dateOfService cannot be after the cleanClaimDate.',
        'CapDentalBalanceEntity-001': 'MT+originSource is required.',
        'MandatoryProviderRoleCd': 'MT+Provider roleCd is required.',
        'MandatoryAlternatePayeeRoleCd': 'MT+AlternatePayee roleCd is required.',
        'MandatoryAuthorizationPeriod': 'MT+authorizationPeriod is mandatory.',
        'MandatoryProcedureQuantity': 'MT+Quantity is required.',
        'ServiceOverrideDeductibleEqualOrHigherThanZero': 'MT+overrideDeductible cannot be negative.',
        'ClaimOverrideBasicWaitingPeriodHigherThanZero': 'MT+overrideBasicWaitingPeriod has to be greater than 0.',
        'MandatoryClaimPolicyId': 'MT+Policy is mandatory.',
        'CapDentalPaymentEntity-001': 'MT+originSource is required.',
        'CapDentalPaymentEntity-002': 'MT+paymentNetAmount is required.',
        'ClaimOverrideGracePeriodEqualOrHigherThanZero': 'MT+Claim overrideGracePeriod cannot be negative.'
   }
}