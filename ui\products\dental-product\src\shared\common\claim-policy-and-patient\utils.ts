/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {DentalIndividualPolicy, DentalPolicySummary} from '@eisgroup/claim-dental-policy-service-api'
import {opt} from '@eisgroup/common-types'
import {ISuggestion} from '@eisgroup/dental-core'
import {CapDentalLoss} from '@eisgroup/dental-models'
import {DentalPolicyData} from '@eisgroup/dental-product-services'
import {t} from '@eisgroup/i18n'

import type {IndividualCustomer, OrganizationCustomer} from '@eisgroup/claim-dental-customer-service-api'

import {applyPhoneMask, formatDate, formCustomerAddressLine, formCustomerFullName} from '../../../utils/helpers'
import {EntityLink, formGerootUriIndividualCustomer, getInsuredNamesFromPolicy} from '../../../utils/helpers/entities'
import {CardData} from '../../components/cards'
import {PatientContactInformation} from '../../components/contact-cards/ContactCardPatient'

import CapDentalLossEntity = CapDentalLoss.CapDentalLossEntity

export function mapPolicyDetails(
    loss: CapDentalLossEntity,
    policy: DentalIndividualPolicy,
    insureds: IndividualCustomer[],
    groupSponsor: OrganizationCustomer
): CardData[] {
    const policyHolderLink = loss?.lossDetail?.claimData?.policyholderRole?.registryId
    const {rootId} = policyHolderLink ? EntityLink.from(policyHolderLink) : {rootId: ''}
    const policyholder = insureds.find(insured => insured._key?.rootId === rootId)
    if (!policy) {
        return []
    }
    return [
        {
            label: t('dental-product:dental-claim-policy-policy-policyholder'),
            value: opt(policyholder).map(formCustomerFullName).orElse('-')
        },
        {
            label: t('dental-product:dental-claim-policy-plan-name'),
            value: opt(policy.individualPackagingDetail?.plan?.planName || policy.planName).orElse('-')
        },
        {
            label: t('dental-product:dental-claim-policy-policy-group-name'),
            value: opt(groupSponsor)
                .map(v => v.details)
                .map(v => v.legalEntity)
                .map(v => v.legalName)
                .orElse('-')
        },
        {
            label: t('dental-product:dental-claim-policy-policy-group-id'),
            value: opt(groupSponsor?.customerNumber).orElse('-')
        }
    ]
}

export function getPatientOptions(insureds: IndividualCustomer[]): {displayValue: string; code: string}[] {
    return insureds
        .filter(insured => Boolean(insured._key?.rootId))
        .map(insured => ({
            displayValue: formCustomerFullName(insured),
            code: formGerootUriIndividualCustomer(insured._key?.rootId!)
        }))
}

export function mapPatientInfo(insureds: IndividualCustomer[], patientUri?: string): PatientContactInformation {
    if (!patientUri) {
        return {
            name: '',
            details: []
        } as PatientContactInformation
    }
    const {rootId} = EntityLink.from(patientUri)

    const insured = insureds.find(insured => insured._key?.rootId === rootId)

    return {
        name: opt(insured).map(formCustomerFullName).orElse('-'),
        details: [
            {
                label: t('dental-product:dental-claim-patient-contact-card-dob'),
                value: opt(formatDate(insured?.details.person.birthDate)).orElse('-')
            },
            {
                label: t('dental-product:dental-claim-patient-contact-card-phone'),
                value: opt(insured?.communicationInfo?.phones)
                    .map(phones => phones?.filter(phoneItem => phoneItem.preferred)?.[0] || phones?.[0])
                    .map(crmPhone => applyPhoneMask(crmPhone?.value))
                    .orElse('-')
            },
            {
                label: t('dental-product:dental-claim-patient-contact-card-customer-id'),
                value: opt(insured?.customerNumber).orElse('-')
            },
            {
                label: t('dental-product:dental-claim-patient-contact-card-address'),
                value: opt(insured).map(formCustomerAddressLine).orElse('-')
            }
        ]
    }
}

export const composeSuggestion = (policy: DentalPolicyData): ISuggestion => {
    const fullNameList = getInsuredNamesFromPolicy(policy?.policyData)
    return {
        key: opt(policy?.rootId).orElse(''),
        value: fullNameList.join(', '),
        hints: [
            {
                hint: `${t('dental-product:dental-claim-policy-policy-number')}: ${policy?.policyNumber}`
            }
        ]
    }
}

export const filterLatestPolicyList = (policyList: DentalPolicySummary[]): DentalPolicySummary[] => {
    const policyMap = new Map()
    policyList.forEach(s => {
        if (!policyMap.get(s.rootId)) {
            policyMap.set(s.rootId, s)
        } else {
            const oldPolicy = policyMap.get(s.rootId)
            const oldPolicyRevisionNo = EntityLink.from(oldPolicy._uri).revisionNo
            const newPolicyRevisionNo = EntityLink.from(s._uri).revisionNo
            policyMap.set(s.rootId, oldPolicyRevisionNo > newPolicyRevisionNo ? oldPolicy : s)
        }
    })
    return [...policyMap.values()]
}
