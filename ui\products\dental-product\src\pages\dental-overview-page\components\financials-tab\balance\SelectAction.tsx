import React from 'react'
import {observer} from 'mobx-react'

import {useTranslate} from '@eisgroup/i18n'
import {AntMenu, Dropdown} from '@eisgroup/ui-kit'

import {SELECT_ACTION} from './classnames'

export const SelectAction: React.FC = observer(() => {
    const {t} = useTranslate()

    const handleMenuClick = ({key}: {key: string}) => {
        // empty now
    }

    const menu = (
        <AntMenu onClick={handleMenuClick}>
            {/* <Menu.Item key="recalculate">
                {t('dental-product:balance-action-recalculate')}
            </Menu.Item>
            <Menu.Item key="adjust">
                {t('dental-product:balance-action-adjust')}
            </Menu.Item>
            <Menu.Item key="refund">
                {t('dental-product:balance-action-refund')}
            </Menu.Item> */}
        </AntMenu>
    )

    return (
        <div className={SELECT_ACTION}>
            <Dropdown
                overlay={menu}
                trigger={['click']}
                label={t('dental-product:dental-claim-balance-select-action')}
            />
        </div>
    )
})
