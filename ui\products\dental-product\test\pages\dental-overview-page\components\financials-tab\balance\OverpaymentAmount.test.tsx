import React from 'react'
import {render, screen} from '@testing-library/react'
import {describe, expect, it, vi} from 'vitest'

import {OverpaymentAmount} from '../../../../../../src/pages/dental-overview-page/components/financials-tab/balance/OverpaymentAmount'

vi.mock('@eisgroup/dental-core', async importActual => ({
    ...(await importActual()),
    MoneyFormat: ({value}: {value: number}) => <span data-testid='money-format'>{value}</span>
}))

vi.mock('@eisgroup/ui-kit', async importActual => ({
    ...(await importActual()),
    Tooltip: ({children}: {children: React.ReactNode}) => <div data-testid='tooltip'>{children}</div>
}))

describe('OverpaymentAmount', () => {
    it('should render not available when amount is undefined', () => {
        render(<OverpaymentAmount totalBalanceAmount={undefined} />)

        expect(screen.getByText('dental-product:not_available')).toBeInTheDocument()
        expect(screen.getByText('dental-product:balance_table_amount_box_0')).toBeInTheDocument()
    })

    it('should render zero amount correctly', () => {
        render(<OverpaymentAmount totalBalanceAmount={{amount: 0, currency: 'Mock'}} />)

        expect(screen.getByTestId('money-format')).toHaveTextContent('0')
        expect(screen.getByText('dental-product:balance_table_amount_box_0')).toBeInTheDocument()
    })

    it('should render positive amount with underpayment text', () => {
        render(<OverpaymentAmount totalBalanceAmount={{amount: 100, currency: 'Mock'}} />)

        expect(screen.getByTestId('money-format')).toHaveTextContent('100')
        expect(screen.getByText('dental-product:balance_table_amount_box_underpayment')).toBeInTheDocument()
    })

    it('should render negative amount with overpayment text and red color', () => {
        const {container} = render(<OverpaymentAmount totalBalanceAmount={{amount: -100, currency: 'Mock'}} />)

        expect(screen.getByTestId('money-format')).toHaveTextContent('-100')
        expect(screen.getByText('dental-product:balance_table_amount_box_overpayment')).toBeInTheDocument()

        // Check for red text class
        const moneyElement = container.querySelector('.text-balance-right-section-money-red')
        expect(moneyElement).toBeInTheDocument()
    })

    it('should apply correct CSS classes', () => {
        const {container} = render(<OverpaymentAmount totalBalanceAmount={{amount: 100, currency: 'Mock'}} />)

        expect(container.firstChild).toHaveClass('gen-balance-header-right-section')
        expect(container.querySelector('.gen-balance-header-amount-box')).toBeInTheDocument()
    })

    it('should render tooltip with correct amount', () => {
        render(<OverpaymentAmount totalBalanceAmount={{amount: 100, currency: 'Mock'}} />)

        const tooltip = screen.getByTestId('tooltip')
        expect(tooltip).toBeInTheDocument()
        expect(tooltip.querySelector('.text-balance-right-section-money')).toBeInTheDocument()
    })
})
