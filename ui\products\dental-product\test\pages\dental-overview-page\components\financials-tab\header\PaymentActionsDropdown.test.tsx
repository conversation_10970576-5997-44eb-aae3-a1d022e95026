/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {fireEvent, render, screen, waitFor} from '@testing-library/react'
import {beforeAll, beforeEach, describe, expect, it, vi} from 'vitest'

import {LocalizationUtils, resources, t} from '@eisgroup/i18n'

import {PaymentScheduledState} from '../../../../../../src/pages/dental-overview-page/components/financials-tab/contants'
import {PaymentActionsDropdown} from '../../../../../../src/pages/dental-overview-page/components/financials-tab/header/PaymentActionsDropdown'
import {Privileges} from '../../../../../../src/utils'
import {initLocalization, mockAuthenticationFacade, wrapInForm} from '../../../../../support/TestUtils'

// Hoisted mock functions
const mockActivatePaymentSchedule = vi.hoisted(() => vi.fn())
const mockCheckPaymentSchedule = vi.hoisted(() => vi.fn())
const mockHasAuthorities = vi.hoisted(() => vi.fn())
const mockGetCurrentUsername = vi.hoisted(() => vi.fn())

vi.mock('@eisgroup/dental-product-services', () => ({
    getConfiguration: vi.fn().mockReturnValue({basePath: ''})
}))

// Mock the payment service utils
vi.mock('../../../../../../src/pages/dental-overview-page/components/financials-tab/paymentServiceUtils', () => ({
    activatePaymentSchedule: mockActivatePaymentSchedule,
    checkPaymentSchedule: mockCheckPaymentSchedule
}))

// Mock the utils
vi.mock('../../../../../../src/utils', () => ({
    hasAuthorities: mockHasAuthorities,
    getCurrentUsername: mockGetCurrentUsername,
    Privileges: {
        FINANCIAL_ACTIVATE_PAYMENT_SCHEDULE: 'Financial: Activate Payment Schedule'
    },
    withPrefix: vi.fn()
}))

describe('FinancialActions Component', () => {
    beforeAll(async () => {
        await initLocalization
        LocalizationUtils.addResourceBundles(resources)
        mockAuthenticationFacade()
    })

    beforeEach(() => {
        vi.clearAllMocks()
        mockHasAuthorities.mockReturnValue(true)
        mockGetCurrentUsername.mockReturnValue('test_user')
        mockActivatePaymentSchedule.mockResolvedValue({success: true})
        mockCheckPaymentSchedule.mockResolvedValue({
            paymentScheduleActivationResult: {
                activationStatus: 'Activate'
            }
        })
    })

    const mockPaymentSchedule = {
        _modelType: 'CapPaymentSchedule',
        _key: 'test-payment-key',
        state: PaymentScheduledState.Open
    }

    const defaultFormValues = {
        paymentList: [mockPaymentSchedule],
        paymentSchedules: [mockPaymentSchedule]
    }

    it('should render the component and display the basic elements', () => {
        render(
            wrapInForm(<PaymentActionsDropdown />, {
                initialValues: defaultFormValues
            })
        )

        const button = screen.getByText(t('dental-product:dental-claim-financial-select-payments-action'))
        expect(button).toBeInTheDocument()
    })

    it('should display the activate button when the user has permission and the payment plan state is Open', async () => {
        mockHasAuthorities.mockReturnValue(true)

        render(
            wrapInForm(<PaymentActionsDropdown />, {
                initialValues: defaultFormValues
            })
        )

        const button = screen.getByText(t('dental-product:dental-claim-financial-select-payments-action'))
        fireEvent.click(button)

        await waitFor(() => {
            expect(
                screen.getByText(t('dental-product:dental-claim-financial-actions-cancel-payment-schedule-btn'))
            ).toBeInTheDocument()
        })
    })

    it('should display the activate button when there is no payment plan', async () => {
        render(
            wrapInForm(<PaymentActionsDropdown />, {
                initialValues: {paymentList: []}
            })
        )

        const button = screen.getByText(t('dental-product:dental-claim-financial-select-payments-action'))
        fireEvent.click(button)

        await waitFor(() => {
            expect(
                screen.queryByText(t('dental-product:dental-claim-financial-actions-cancel-payment-schedule-btn'))
            ).toBeInTheDocument()
        })
    })

    it('should display the activate button when the payment plan is not a CapPaymentSchedule type', async () => {
        const nonCapPaymentSchedule = {
            _modelType: 'OtherPaymentType',
            _key: 'test-payment-key',
            state: PaymentScheduledState.Open
        }

        render(
            wrapInForm(<PaymentActionsDropdown />, {
                initialValues: {paymentList: [nonCapPaymentSchedule]}
            })
        )

        const button = screen.getByText(t('dental-product:dental-claim-financial-select-payments-action'))
        fireEvent.click(button)

        await waitFor(() => {
            expect(
                screen.queryByText(t('dental-product:dental-claim-financial-actions-cancel-payment-schedule-btn'))
            ).toBeInTheDocument()
        })
    })

    it('should update the state when the dropdown menu visibility changes', async () => {
        render(
            wrapInForm(<PaymentActionsDropdown />, {
                initialValues: defaultFormValues
            })
        )

        const button = screen.getByText(t('dental-product:dental-claim-financial-select-payments-action'))

        fireEvent.click(button)
        fireEvent.click(document.body)

        expect(button).toBeInTheDocument()
    })
})
