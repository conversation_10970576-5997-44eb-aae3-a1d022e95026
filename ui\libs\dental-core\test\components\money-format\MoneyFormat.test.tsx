import React from 'react'
import {render} from '@testing-library/react'
import {vi} from 'vitest'

import {MoneyFormat} from '../../../src/components/money-format'

vi.mock('@eisgroup/i18n', () => ({
    LocalizationUtils: {
        moneyValue: (val: number) => ({
            toString: () => `$${val.toFixed(2)}`
        })
    },
    useTranslate: () => ({
        t: (key: string) => (key === 'dental-core:not_available' ? 'N/A' : key)
    })
}))

describe('MoneyFormat', () => {
    it('renders empty text when value is undefined', () => {
        const {getByText} = render(<MoneyFormat />)
        expect(getByText('N/A')).toBeInTheDocument()
    })

    it('renders 0 correctly', () => {
        const {getByText} = render(<MoneyFormat value={0} />)
        expect(getByText('$0.00')).toBeInTheDocument()
    })

    it('renders positive value correctly', () => {
        const {getByText} = render(<MoneyFormat value={1234.56} />)
        expect(getByText('$1234.56')).toBeInTheDocument()
    })

    it('renders negative value correctly', () => {
        const {getByText} = render(<MoneyFormat value={-1234.56} />)
        const element = getByText('($1234.56)')
        expect(element).toBeInTheDocument()
    })

    it('uses custom empty function', () => {
        const isEmpty = (value?: number) => value === 100
        const {getByText} = render(<MoneyFormat value={100} options={{isEmpty}} />)
        expect(getByText('N/A')).toBeInTheDocument()
    })

    it('uses custom empty text', () => {
        const {getByText} = render(<MoneyFormat value={undefined} options={{emptyText: 'Custom Empty'}} />)
        expect(getByText('Custom Empty')).toBeInTheDocument()
    })

    it('uses custom negative formatter', () => {
        const negetiveValueFormatter = (value: number) => `NEGATIVE ${value}`
        const {getByText} = render(<MoneyFormat value={-100} options={{negetiveValueFormatter}} />)
        expect(getByText('NEGATIVE -100')).toBeInTheDocument()
    })

    it('applies custom className', () => {
        const {container} = render(<MoneyFormat className='custom-class' value={100} />)
        expect(container.firstChild).toHaveClass('custom-class')
    })

    it('handles zero value correctly', () => {
        const {getByText} = render(<MoneyFormat value={0} />)
        expect(getByText('$0.00')).toBeInTheDocument()
    })

    it('handles large numbers correctly', () => {
        const {getByText} = render(<MoneyFormat value={1000000} />)
        expect(getByText('$1000000.00')).toBeInTheDocument()
    })

    it('handles decimal precision correctly', () => {
        const {getByText} = render(<MoneyFormat value={123.456} />)
        expect(getByText('$123.46')).toBeInTheDocument()
    })
})
