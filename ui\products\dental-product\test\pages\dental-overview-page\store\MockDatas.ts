/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

export const mockAccumulatorData: any[] = [
    {
        '_type': 'CapAccumulatorContainerEntity',
        '_modelName': 'CapAccumulatorContainer',
        '_modelType': 'CapAccumulatorContainer',
        '_modelVersion': '1',
        'accumulators': [
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '1382a766-091b-3b9e-829d-06c67eae936f',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '53a80a8a-de65-3a3b-9056-55189b0ca74b'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticMaximum_Lifetime_Cosmetic',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '38359c88-d1fb-300f-ac46-9b1e406bd3a4',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '802f574b-b9e3-31c9-8633-2e03190fea0d'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticMaximum_Lifetime_Cosmetic',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'ad2f0f11-af10-3503-9ccc-ed47b8ee5dfd',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '92e64f07-afb9-31f8-997b-f618919b2125'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticMaximum_Lifetime_Cosmetic',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'c0bfddf6-adee-397e-bfb5-2afe5f37fce1',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '099c4d1c-7525-32f4-8f49-2fbda2ab15a1'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticMaximum_Lifetime_Cosmetic',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'bfe07c50-66c5-3ecd-a092-230c263098da',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '663554fe-6d2a-36b4-8b2c-369a8a2f05c6'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticMaximum_Lifetime_Cosmetic',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '5f271e01-134e-339e-ad12-8d3facb60e76',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'fd718f78-665d-351c-8c54-aae897512183'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticMaximum_Lifetime_Cosmetic',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Implants'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '13bd8256-051f-38df-acbd-d3d0c93525fb',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'e13f5cd9-4010-3d47-ba22-6d7270289450'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Lifetime_Implants',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Implants'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'fd6a94ad-d7b5-341e-8248-66818708307a',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'dcd9dd47-9089-3b6f-ad8f-979775e9f916'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Lifetime_Implants',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Implants'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'bc243c46-46fb-3000-842b-a3aa805f731c',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'a85db68a-0e6e-3ec2-a676-d5f8bb242e6f'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Lifetime_Implants',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Implants'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '56783317-1694-30ba-a013-de0ef3c467b4',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'd2d8e136-ce33-31b5-981b-1513ba44a77f'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Lifetime_Implants',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Implants'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '6a894a94-33f1-3510-963b-890b7f7df7e1',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'f700d454-0625-3d33-8108-09bf9d6266c5'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Lifetime_Implants',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Implants'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'c11fe2cf-74e6-3e64-ba0d-1518bc065cc8',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '449772a2-afe2-334d-b40e-54ecf2401194'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Lifetime_Implants',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Basic', 'Major', 'Preventive'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'db26c2ed-60a2-3d27-a658-84ee8b5dea98',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '30adf361-36f2-36ae-9209-deca96b6bc13'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualDeductible_Annual_Dental',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 25,
                'limitAmount': 25,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Basic', 'Major', 'Preventive'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '1f50f051-330a-3083-acc0-78f793f981a3',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'b2c62daf-a038-3036-a6d0-84a7704048d3'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualDeductible_Annual_Dental',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 25,
                'limitAmount': 25,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Basic', 'Major', 'Preventive'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'd106371d-a98a-3828-abf3-d44718782492',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '1dbf8755-6b64-313a-ab35-61814bc077b9'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualDeductible_Annual_Dental',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 25,
                'limitAmount': 25,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Basic', 'Major', 'Preventive'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '73fefa11-37a4-31f4-a3a1-deff925c6e18',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '6bcae3a7-d9f3-3cb6-9c56-e8cdb08c8fd6'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualDeductible_Annual_Dental',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Basic', 'Major', 'Preventive'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '68a81751-2029-3a06-b642-70a3386c8c94',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'af01da05-1eca-348f-a7c2-cd36866f056a'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualDeductible_Annual_Dental',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Basic', 'Major', 'Preventive'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '54115fad-9642-372a-824d-87c1da5cb9a1',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '96c3db91-82f8-3486-91d8-d676ae177df1'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualDeductible_Annual_Dental',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': [],
                    'networkType': 'INN',
                    '_key': {
                        'id': '13fac71f-82fd-309a-bc5b-401e1a68859a',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '2572db33-25a3-3bda-b60a-7f6c5bce0b95'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualMaximum_INN_Annual_Major',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': [],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'abf4d911-a83c-3b76-b819-4652200d724e',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '23a49a6f-eb41-321b-a764-09351ff08273'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualMaximum_INN_Annual_Major',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': [],
                    'networkType': 'INN',
                    '_key': {
                        'id': '72ae8368-f756-3cd0-8d4d-36a46cfd6742',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '1becd45f-a42f-3ab1-a3e5-7aad3bb3d1fd'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualMaximum_INN_Annual_Major',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'fc3ed8fe-6c4b-3730-bc6c-a9ad84429e20',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': 'bbf7d152-1e5a-3ed4-bdda-a0a4e6ef63fc'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoDeductible_Annual_Orthodontics',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '57a087e4-117c-3775-9e45-5c4e727405df',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': '04119d01-6cdb-3fc9-bfc1-2c4456199595'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoDeductible_Annual_Orthodontics',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '80cdd792-c09b-3d0d-9aac-8ef9e5191c8b',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': '15d1b00a-a7da-3861-9035-0aad8853eb50'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoDeductible_Annual_Orthodontics',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '48dcd2bf-584d-3a91-bf17-5293ec3841fe',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': '33f9ef9e-4224-335f-b2b8-c6f7f8b11df2'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoDeductible_Annual_Orthodontics',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '0607c7f7-3cae-360b-916b-fdd49b46e199',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': '90808a15-17a0-3d1b-925c-72cd4d87bd9c'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoDeductible_Annual_Orthodontics',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '68a6a8b6-42ea-34b3-ab47-6f9d0af07bed',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': '10f68cdb-9de6-39cf-891a-4bc8eeb639fa'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoDeductible_Annual_Orthodontics',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': [],
                    'networkType': 'INN',
                    '_key': {
                        'id': '8e180693-da0c-364f-b592-7132a25dcd7f',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'ace861dc-f5f6-3114-b695-6d6d3535b549'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualMaximum_INN_Annual_Preventive',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': [],
                    'networkType': 'INN',
                    '_key': {
                        'id': '22a46ac9-7a81-30cb-839c-b33766c877e7',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '10de030c-3bfe-38e9-8c13-49e7765e0d3a'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualMaximum_INN_Annual_Preventive',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': [],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'e6fe704c-91a9-39f3-a499-f7dde1434a51',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '36b1a04c-60a9-31f7-bcc8-9cf086d98dd4'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualMaximum_INN_Annual_Preventive',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['TMJ'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '0c30edb8-d15a-3b3c-a800-e39cec249aea',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '8d7de96d-c62c-3eb8-8d68-df807de90a07'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Annual_TMJ',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['TMJ'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '7f42da69-fbb9-31e3-bf74-cef58fcec5ca',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '047ba3c4-adf9-3424-b2d4-e371e319fc0c'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Annual_TMJ',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['TMJ'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'f24c41b0-898e-3199-9cd2-59ec2ad121b9',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': 'cf5b984d-9990-3fd0-8913-ae4a3d7d54f6'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Annual_TMJ',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['TMJ'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '2a519872-d0ce-345d-a9e0-89a32c035d5f',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '5255ebf4-7193-3b79-b0da-9c251963eda6'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Annual_TMJ',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['TMJ'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '09d865fd-daed-32c9-a4f8-902953c4e84e',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': 'ab2d5cd3-7819-33e0-a8ad-1d476769cef9'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Annual_TMJ',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['TMJ'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '3fcd4dfe-bff1-37b6-b4e8-1831d4275566',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '2679622f-7a64-36be-a205-bc03ba46be8f'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Annual_TMJ',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '7f8c33de-8964-33fa-adef-e1e4f20bcfba',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '9f06fae7-7eb2-3daf-a989-ca679886cb57'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoMaximum_Lifetime_Orthodontics',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '351fb0cc-0976-3595-aa72-ea8c6307706e',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'a305c091-b332-3c91-ac9c-49e0a442a4e8'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoMaximum_Lifetime_Orthodontics',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'ef3f9757-05d8-3ee4-8ff5-4318dea6dfa3',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '8912f718-1557-3ecb-a74f-f312ffb097b1'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoMaximum_Lifetime_Orthodontics',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'f017fa14-af79-3b18-a71b-dc44fe8e6c21',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'e6df5239-a670-3ffd-8dc9-cac54b3fd916'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoMaximum_Lifetime_Orthodontics',
                'remainingAmount': 2250,
                'limitAmount': 2250,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '725fd315-e6fb-36d3-8118-090efaba63e8',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '8aac41fd-9de5-3c13-889d-571dcb6e5c81'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoMaximum_Lifetime_Orthodontics',
                'remainingAmount': 2250,
                'limitAmount': 2250,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['Orthodontics'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'b61ac2f6-5d3b-3c03-8d52-f844504603f3',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '5cc6f4c4-b925-3391-b99c-36c7811ed1be'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'OrthoMaximum_Lifetime_Orthodontics',
                'remainingAmount': 2250,
                'limitAmount': 2250,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': [],
                    'networkType': 'INN',
                    '_key': {
                        'id': '039bcac8-615e-3370-aa38-48fb7af732ca',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '44794197-6fc1-3485-ad74-9d33709794c1'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualMaximum_INN_Annual_Basic',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': [],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'ddeaa753-25ec-3365-b116-20412b27b721',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '85fe7f03-fd4e-3349-b6f1-8b5175bdff08'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualMaximum_INN_Annual_Basic',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': [],
                    'networkType': 'INN',
                    '_key': {
                        'id': '2344d232-cb8b-3de4-9059-467fbbd374b8',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '84a094fe-2a60-3c4f-97a3-9d81ac349b72'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'IndividualMaximum_INN_Annual_Basic',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'c580e0b2-e701-3ee5-8b34-5d5ea0581ffc',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': 'e45edf80-fd6d-3aa9-b039-3df3317ba869'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Annual_Cosmetic',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '8046b3a7-65d8-37f9-8359-bad764574be8',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '5e4fca77-b8e6-33e3-b4b2-f1372aba10ba'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Annual_Cosmetic',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '0b6d5de1-d145-3b37-b0d0-48e685d8b3f5',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': 'bdcb40c9-90c7-3d4f-bc04-002d41814ec5'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Annual_Cosmetic',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 50,
                'limitAmount': 50,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'b9ac6293-f142-3a23-87a6-e75bec645fb1',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '51a1991e-578b-3e6a-a845-26aeea8538b6'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Annual_Cosmetic',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '8883ff67-2e8b-3aaf-b24f-327663139ce7',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '3e086ba8-8882-3465-9ce9-db855e51c8de'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Annual_Cosmetic',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '37d9a39f-f3e7-3f05-9c6e-d24f089d2d29',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': 'bfcef990-70ea-37f9-b55d-4b067c727a3f'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Annual_Cosmetic',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Implants'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'fe314d6c-f5f5-36a8-93eb-0cd36b2b010f',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '468a2ef8-2ff9-3e59-a285-ebc1f545cae4'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Annual_Implants',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Implants'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '9f6608a6-fb06-3e93-a1c0-fcf00f52b0e1',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '5a02be50-7599-397d-8e13-1869b2917ed4'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Annual_Implants',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Implants'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'b9c38e5b-b66c-38ef-900a-34f1abd8b6be',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '1602329d-a361-37f8-b11a-5ff4fa338069'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Annual_Implants',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Implants'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'a26ee343-d849-393e-9614-57801d32b95f',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': 'a7c31bdf-3734-36e7-aa4c-38ca96d752dc'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Annual_Implants',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Implants'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'c55283e4-3e5d-367a-acb0-0a88e3bc4915',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '7980288d-f2dc-3b2e-bcb3-93e361c90721'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Annual_Implants',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'term': {
                        '_type': 'Term',
                        'effectiveDate': '2024-07-01T00:00:00.000Z',
                        'expirationDate': '2025-06-30T23:59:59.999Z'
                    },
                    'appliedToServices': ['Implants'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'fbefc83e-f2c6-3ee0-93a6-da7178b96a0f',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': 'e8ab957f-6a56-3e96-86b3-a14ec70b0f2d'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'ImplantsMaximum_Annual_Implants',
                'policyTermDetails': {
                    '_type': 'Term',
                    'effectiveDate': '2024-07-01T00:00:00.000Z',
                    'expirationDate': '2025-06-30T23:59:59.999Z'
                },
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '655c56c0-dec2-30af-8768-020018838473',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': 'aaee2d04-008a-334d-a6c2-c49767513e0c'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Lifetime_TMJ',
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '20f32fef-a718-3a20-9d8d-aff98d7c2ccb',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': '2284a934-6e23-357b-835d-8a114ccf9a54'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Lifetime_TMJ',
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'ac420b67-08fe-3e02-b187-e800f44fe21a',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': 'a7e42205-5412-3f60-9357-89bbbd24d8ff'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Lifetime_TMJ',
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '34c67857-e8aa-3b52-907a-840c7a3c0eef',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': '042985c7-0b73-3097-b0ee-90c8dcbbd4e6'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Lifetime_TMJ',
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '69962093-2979-333d-87b8-2a08e5d73406',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': '7401602e-6de2-32a9-8436-7618ed5ce204'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Lifetime_TMJ',
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'a30c9536-94c4-3a77-bc89-139bdb78ed42',
                        'rootId': '9a8a343c-5f35-4627-81af-2d0b7e347bb3',
                        'revisionNo': 1,
                        'parentId': 'd9206b46-a94f-300c-9938-201cdd46db46'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:58:22.407Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:58:22.407Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/3'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJDeductible_Lifetime_TMJ',
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '989954cb-cf7e-350a-a36a-7c3150237b97',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '7eaf0b50-c697-33d8-b775-d6bc50eb55ae'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Lifetime_Cosmetic',
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '3416e556-db86-3324-8932-c8f4617adf58',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '316a467c-43eb-3fda-adb3-8d45bfe1dc68'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Lifetime_Cosmetic',
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'db15bba4-5cbc-3e0d-91d3-49da86a81d07',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '249ba0d3-ecab-3032-a293-d12c174b66a5'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Lifetime_Cosmetic',
                'remainingAmount': 100,
                'limitAmount': 100,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '653b63ff-31af-3835-8057-5186df9eb7aa',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': 'ec8613c6-e8f1-35e5-91e5-2430d217a03b'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Lifetime_Cosmetic',
                'remainingAmount': 150,
                'limitAmount': 150,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '4347a0ba-5576-3b50-84ab-7b7f9be130b2',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': '96daf4ea-053b-361e-a7ac-a66049514f2e'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Lifetime_Cosmetic',
                'remainingAmount': 150,
                'limitAmount': 150,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['CosmeticServices'],
                    'networkType': 'OON',
                    '_key': {
                        'id': '2db80b2f-8c43-3de3-ac77-50777aef316e',
                        'rootId': '035c339d-1134-46b2-83d8-5cbfa33d95f2',
                        'revisionNo': 1,
                        'parentId': 'b35824b4-7d3a-32a6-a2a6-f68c29621db7'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:39:52.851Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:39:52.851Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/2'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'CosmeticDeductible_Lifetime_Cosmetic',
                'remainingAmount': 150,
                'limitAmount': 150,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'INN',
                    '_key': {
                        'id': 'e5d676da-410b-30de-981d-2f787c3d313b',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': '81076886-c044-3d68-a685-eed17e4e328c'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJMaximum_Lifetime_TMJ',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '62decaad-d0c1-33e6-96c2-6dc558ee1227',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'c6d31ff3-6347-3ce0-9049-f966f18accbd'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJMaximum_Lifetime_TMJ',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'INN',
                    '_key': {
                        'id': '5db8a28e-eddd-3f96-9a74-4e71c42a5dd0',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'f1eb4994-e394-347d-acc4-7213ffa111c8'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJMaximum_Lifetime_TMJ',
                'remainingAmount': 1000,
                'limitAmount': 1000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'c046635a-66da-3ce1-911c-0de2e2c56681',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'd99f6dc9-de58-3a9b-b636-caafd52b1aa9'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJMaximum_Lifetime_TMJ',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'cc57f6b2-5494-39a3-97a8-141d8721ca75',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'd7c8f5ad-b03f-37dc-ab55-4128da22012e'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJMaximum_Lifetime_TMJ',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//122508b1-7c4d-3480-9ea9-90a1c8297c7f'
                }
            },
            {
                '_type': 'CapAccumulator',
                'flags': [],
                'history': [],
                'transactions': [],
                'reservedAmount': 0,
                'extension': {
                    '_type': 'JsonType',
                    'appliedToServices': ['TMJ'],
                    'networkType': 'OON',
                    '_key': {
                        'id': 'b28a7528-503e-31df-800e-03ae4633e77d',
                        'rootId': 'cee31d4c-4abb-48c5-93a0-97ea744ec44e',
                        'revisionNo': 1,
                        'parentId': 'dfa942c7-6a32-333c-b763-4f63156e6094'
                    }
                },
                'accessTrackInfo': {
                    '_type': 'AccessTrackInfo',
                    'updatedOn': '2025-06-25T06:15:47.050Z',
                    '_key': {},
                    'createdOn': '2025-06-25T06:15:47.050Z'
                },
                'resource': {
                    '_uri': 'gentity://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d/1'
                },
                'usedAmount': 0,
                '_key': {},
                'type': 'TMJMaximum_Lifetime_TMJ',
                'remainingAmount': 2000,
                'limitAmount': 2000,
                'party': {
                    '_uri': 'geroot://Customer/INDIVIDUALCUSTOMER//248edca5-302b-3e35-af87-db5a5c6b9d3e'
                }
            }
        ],
        '_key': {},
        'customerURI': 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96',
        'policyURI': 'capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/9e05a548-beba-376e-9dca-d04abfe5800d'
    }
]

export const mockDentalLoss = {
    '_key': {
        'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
        'revisionNo': 1
    },
    '_type': 'CapDentalLossEntity',
    '_modelName': 'CapDentalLoss',
    '_modelVersion': '1',
    '_modelType': 'CapLoss',
    '_timestamp': '2025-08-06T07:41:38.653Z',
    'claimType': 'dental',
    'lossNumber': 'DN997',
    'policy': {
        'riskStateCd': 'NY',
        'isVerified': true,
        'capPolicyId':
            'capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/a293a858-565b-3999-b2cb-120a48b27658',
        'policyNumber': 'DN1748472742',
        'planName': 'High',
        'term': {
            'effectiveDate': '2024-06-01T00:00:00Z',
            '_type': 'Term',
            '_key': {
                'id': 'ceead71e-a3b7-3307-8dcc-092b543c4504',
                'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
                'revisionNo': 1,
                'parentId': 'ac4431fa-220d-352d-81d1-e2fc798fa76c'
            }
        },
        'policyStatus': 'issued',
        'txEffectiveDate': '2024-06-01T00:00:00Z',
        'capPolicyVersionId':
            'capPolicy://PAS/gentity://PolicySummary/DNIndividual/policy/a293a858-565b-3999-b2cb-120a48b27658/1',
        'plan': 'High',
        'productCd': 'DNIndividual',
        '_modelName': 'CapDentalLoss',
        '_modelVersion': '1',
        '_type': 'CapDentalLossPolicyEntity',
        '_key': {
            'id': 'ac4431fa-220d-352d-81d1-e2fc798fa76c',
            'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
            'revisionNo': 1,
            'parentId': '84264863-fde2-4fb6-b139-2f1a16e57c88'
        }
    },
    'state': 'Open',
    'policyId': 'capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/a293a858-565b-3999-b2cb-120a48b27658',
    'lossDetail': {
        '_key': {
            'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
            'revisionNo': 1,
            'parentId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
            'id': 'ac1e2878-7fe9-4152-95fd-d78ad8be1f28'
        },
        '_type': 'CapDentalDetailEntity',
        '_modelName': 'CapDentalLoss',
        '_modelVersion': '1',
        '_modelType': 'CapLoss',
        '_timestamp': '2025-08-06T07:41:38.651Z',
        'submittedProcedures': [
            {
                '_type': 'CapDentalProcedureEntity',
                'diagnosisCodes': [],
                'surfaces': [],
                'toothCodes': [],
                '_key': {
                    'id': '0092821b-8450-429b-a613-e3ab50cff048',
                    'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
                    'revisionNo': 1,
                    'parentId': 'ac1e2878-7fe9-4152-95fd-d78ad8be1f28'
                },
                'ortho': {
                    '_type': 'CapDentalOrthodonticEntity',
                    '_key': {
                        'id': '8ec9c70a-62f1-4006-a826-db849b8ddbea',
                        'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
                        'revisionNo': 1,
                        'parentId': '0092821b-8450-429b-a613-e3ab50cff048'
                    },
                    'orthoFrequencyCd': 'Monthly',
                    'orthoMonthQuantity': 1
                },
                'predetInd': false,
                'quantity': 1,
                'dateOfService': '2025-08-01',
                'procedureCode': 'D0120',
                'submittedFee': {
                    'currency': 'USD',
                    'amount': 122
                }
            }
        ],
        'claimData': {
            'initialDateOfService': '2025-08-01T00:00:00Z',
            '_type': 'CapDentalClaimDataEntity',
            'cob': [],
            'digitalImageNumbers': [],
            'missingTooths': [],
            'providerFees': [],
            '_key': {
                'id': 'e7e2c8c4-b551-4b0a-920d-b6b3ed858c9d',
                'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
                'revisionNo': 1,
                'parentId': 'ac1e2878-7fe9-4152-95fd-d78ad8be1f28'
            },
            'treatmentReason': {
                '_type': 'CapDentalTreatmentReasonEntity',
                '_key': {
                    'id': 'fcf7e4d4-5c01-48b3-846d-0be8f74ed12f',
                    'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
                    'revisionNo': 1,
                    'parentId': 'e7e2c8c4-b551-4b0a-920d-b6b3ed858c9d'
                }
            },
            'providerRole': {
                '_type': 'CapProviderRole',
                'roleCd': ['IndividualProvider'],
                '_key': {
                    'id': 'ffa48b71-4807-4504-80d9-5564810dea6b',
                    'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
                    'revisionNo': 1,
                    'parentId': 'e7e2c8c4-b551-4b0a-920d-b6b3ed858c9d'
                },
                'registryId': 'geroot://Customer/INDIVIDUALCUSTOMER//38bbb114-9151-3771-9731-465b824d06ca',
                'providerLink': 'geroot://Provider/IndividualProvider//d89f4b90-9259-33fc-8c10-ababee127b7a'
            },
            'source': 'NONEDI',
            'policyholderRole': {
                '_type': 'CapPolicyholderRole',
                'roleCd': ['Member'],
                'registryId': 'geroot://Customer/INDIVIDUALCUSTOMER//ddfb70cf-2bfc-3911-b92a-04345434c7cf',
                '_key': {
                    'id': '4e6eba2c-354f-323d-887c-499367ffb3ab',
                    'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
                    'revisionNo': 1,
                    'parentId': 'e7e2c8c4-b551-4b0a-920d-b6b3ed858c9d'
                }
            },
            'patientRole': {
                '_type': 'CapPatientRole',
                'roleCd': ['SubjectOfClaims'],
                'registryId': 'geroot://Customer/INDIVIDUALCUSTOMER//25cb2f3d-a3d9-4f15-8f5c-a18f3bcb1fe1',
                '_key': {
                    'id': '559eb20d-b934-341d-8d91-fa1d7e1c6886',
                    'rootId': '84264863-fde2-4fb6-b139-2f1a16e57c88',
                    'revisionNo': 1,
                    'parentId': 'e7e2c8c4-b551-4b0a-920d-b6b3ed858c9d'
                }
            },
            'receivedDate': '2025-08-06',
            'payeeType': 'Provider',
            'transactionType': 'ActualServices'
        }
    }
}

export const policyholderUri = 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
export const patientUri = 'geroot://Customer/INDIVIDUALCUSTOMER//8a328166-d293-34dc-9549-8475ab621d96'
