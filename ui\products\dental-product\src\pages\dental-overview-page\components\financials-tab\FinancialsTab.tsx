/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {useState} from 'react'
import {observer} from 'mobx-react'

import {useFormState} from '@eisgroup/form'

import {BalanceTab} from './balance'
import {DENTAL_FINANCIALS_SECTION, DENTAL_FINANCIALS_WRAPPER} from './classnames'
import {FinancialToggle} from './header/FinancialToggle'
import {PaymentsTable} from './payments/PaymentsTable'

export const FINANCIALS_TAB_TOGGLES = {
    PAYMENTS: 'payments',
    RECOVERIES: 'recoveries',
    BANLANCE: 'balance'
}

export const FinancialsTab = observer(() => {
    const {paymentList, parties} = useFormState().values

    const [activeToggle, setActiveToggle] = useState(FINANCIALS_TAB_TOGGLES.PAYMENTS)

    const handleChangeToggle = (value: string) => {
        setActiveToggle(value)
    }

    return (
        <div className={DENTAL_FINANCIALS_WRAPPER}>
            <div className={DENTAL_FINANCIALS_SECTION}>
                <FinancialToggle activeToggle={activeToggle} handleChangeToggle={handleChangeToggle} />
                {activeToggle === FINANCIALS_TAB_TOGGLES.PAYMENTS && (
                    <PaymentsTable paymentList={paymentList} parties={parties} />
                )}
                {activeToggle === FINANCIALS_TAB_TOGGLES.BANLANCE && <BalanceTab />}
            </div>
        </div>
    )
})
