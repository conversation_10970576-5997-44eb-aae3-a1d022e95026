.@{PREFIX}-balance-tab {
    .@{PREFIX}-balance-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 24px 0 24px 0;
        height: 50px;
    }

    .@{PREFIX}-select-action {
        button {
            margin: 0;
        }
    }

    .@{PREFIX}-balance-header-right-section-tooltip {
        .ant-tooltip-inner {
            text-align: center;
            background-color: @white !important;
            color: @gen-color-neutral-l-2;
        }

        .ant-typography {
            color: @eis-font-heading-h-2-color !important;
        }
    }

    .@{PREFIX}-balance-header-right-section {
        position: relative;
        top: -43px;

        .@{PREFIX}-balance-header-amount-box {
            margin: 0;
            border: 1.5px solid @eis-color-neutral-l-4;
            border-radius: 0.25rem;
            width: 13rem;
            height: 6rem;
            float: left;
            text-align: center;

            h2 {
                line-height: 3.5rem;
                padding: 0 0.5rem;
            }
        }

        .total-balance-right-section-money {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .text-balance-right-section-money-red {
            color: red;
        }
    }

    .@{PREFIX}-overpayment-amount {
        border: 1px solid #d9d9d9;
        width: 200px;
        height: 94px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
    }

    .@{PREFIX}-overpayment-amount-value {
        color: red;
        font-size: 24px;
        font-weight: bold;
        line-height: 1.2;
    }

    .@{PREFIX}-overpayment-amount-label {
        color: #666;
        font-size: 14px;
        margin-top: 4px;
    }

    .@{PREFIX}-balance-activities,
    .@{PREFIX}-recalculated-payments {
        border: 1px solid @eis-color-neutral-l-4;
        border-radius: 5px;
        padding: 5px;

        .ant-collapse {
            border: none;
        }

        .ant-collapse-item {
            border: none;
        }
    }

    .@{PREFIX}-balance-activities {
        margin-bottom: 24px;
    }

    .@{PREFIX}-balance-activities-column-desc {
        padding-left: clamp(0px, calc((100vw - 768px) / (1200 - 768) * 80), 80px) !important;
    }

    .@{PREFIX}-balance-description-ellipsis {
        max-width: 18rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        label span:last-child:not(.ant-radio-inner) {
            width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }

    .@{PREFIX}-recalculated-payments-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .@{PREFIX}-recalculated-payments-summary {
        display: flex;
        gap: 20px;
        padding-right: 0.5rem;

        &-card {
            display: flex;
            flex-direction: column;
            align-items: flex-end;

            &-title {
                font-size: 14px;
                color: #666666;
                font-weight: 400;
            }

            &-data {
                font-size: 18px;
                color: #444444;
                font-weight: 500;
            }
        }
    }

    .@{PREFIX}-recalculated-payments-allocation-table-expand {
        margin-left: 30%;
        padding-left: 25px;
    }

    .@{PREFIX}-recalculated-payments-allocation-table-expand-row {
        &:not(:last-of-type) {
            margin-bottom: 1rem;
        }
    }

    .@{PREFIX}-recalculated-payments-allocation-table-expand-payment-amount,
    .@{PREFIX}-recalculated-payments-allocation-table-expand-should-have-paid {
        display: flex;
        flex-direction: column;
    }

    .@{PREFIX}-recalculated-payments-allocation-table-expand-payment-amount {
        padding: 0 12px 0 12px;
    }

    .@{PREFIX}-recalculated-payments-allocation-table-expand-should-have-paid {
        padding: 0 0 0 12px;
    }

    .@{PREFIX}-recalculated-payments-allocation-table-expand-allocation {
        display: flex;
        justify-content: space-between;

        label {
            color: @gen-color-neutral-l-2;
        }
    }
}
