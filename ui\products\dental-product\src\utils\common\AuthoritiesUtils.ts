/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {authentication} from '@eisgroup/auth'
import {IoC} from '@eisgroup/ioc'

/**
 * Privileges/Authorities
 * command : expression
 */
export enum Privileges {
    UPDATE_LOSS = 'LossIntake: Update Loss',
    SUBMIT_LOSS = 'LossIntake: Submit Loss',
    SET_LOSS_SUBSTATUS = 'LossIntake: Set Loss Sub-Status',
    FINANCIAL_ACTIVATE_PAYMENT_SCHEDULE = 'Financial: Activate Payment Schedule',
    FINANCIAL_CANCEL_PAYMENT_SCHEDULE = 'Financial: Cancel Payment Schedule'
}

/**
 * Utility which checks whether user has defined privileges
 * @param privileges
 */
export const hasAuthorities = (privileges: string[]): boolean => {
    const authService = IoC.get<authentication.AuthenticationFacade>(
        authentication.TYPES.AuthenticationFacade
    ).getService()
    return authService.hasAuthorities(privileges)
}
