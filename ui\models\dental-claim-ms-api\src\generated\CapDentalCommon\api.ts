// tslint:disable
/**
 * common model API facade
 * API for common
 *
 * OpenAPI spec version: 1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


import * as url from "url";
import * as portableFetch from "portable-fetch";
import { Configuration } from "./configuration";

const BASE_PATH = "http://localhost".replace(/\/+$/, "");

/**
 *
 * @export
 */
export const COLLECTION_FORMATS = {
    csv: ",",
    ssv: " ",
    tsv: "\t",
    pipes: "|",
};

/**
 *
 * @export
 * @interface FetchAPI
 */
export interface FetchAPI {
    (url: string, init?: any): Promise<Response>;
}

/**
 *  
 * @export
 * @interface FetchArgs
 */
export interface FetchArgs {
    url: string;
    options: any;
}

/**
 * 
 * @export
 * @class BaseAPI
 */
export class BaseAPI {
    protected configuration: Configuration;

    constructor(configuration?: Configuration, protected basePath: string = BASE_PATH, protected fetch: FetchAPI = portableFetch) {
        if (configuration) {
            this.configuration = configuration;
            this.basePath = configuration.basePath || this.basePath;
        }
    }
};

/**
 * 
 * @export
 * @class RequiredError
 * @extends {Error}
 */
export class RequiredError extends Error {
    name: "RequiredError"
    constructor(public field: string, msg?: string) {
        super(msg);
    }
}

/**
 * Allocation acumulator details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    accumulatorAmount?: Money;
    /**
     * Accumulator type.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    accumulatorType?: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    appliesToProcedureCategories?: Array<string>;
    /**
     * Defines to which procedure category accumulator applies to.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    appliesToProcedureCategory?: string;
    /**
     * Network type for accumulator.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    networkType?: string;
    /**
     * Renewal type for accumulator.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    renewalType?: string;
    /**
     * 
     * @type {CapDentalPaymentScheduleTerm}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    term?: CapDentalPaymentScheduleTerm;
}

/**
 * Dental allocation details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity>}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
     */
    accumulatorDetails?: Array<CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity>;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
     */
    patient?: EntityLink;
    /**
     * Dental claim transaction type.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
     */
    transactionTypeCd?: string;
}

/**
 * An object which extends payment allocations details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentAllocationEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    _type: string;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationDentalDetails?: CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationGrossAmount?: Money;
    /**
     * Allocation Line Of Business code.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationLobCd?: string;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationLossInfo?: CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationNetAmount?: Money;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationPayableItem?: CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationSource?: EntityLink;
    /**
     * Allocation reserve type.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    reserveType?: string;
}

/**
 * Allocation claim details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity
     */
    lossSource?: EntityLink;
}

/**
 * Dental allocation payable item details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
     */
    claimSource?: EntityLink;
    /**
     * Month number for which allocation is paid.
     * @type {number}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
     */
    orthoMonth?: number;
    /**
     * Related Settlement Result entry's procedure ID.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
     */
    procedureID?: string;
}

/**
 * An object which extends payment details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
     */
    payeeDetails?: CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalPaymentAllocationEntity>}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
     */
    paymentAllocations?: Array<CapDentalPaymentScheduleCapDentalPaymentAllocationEntity>;
    /**
     * The payment post date.
     * @type {Date}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
     */
    paymentDate?: Date;
}

/**
 * An object which extends payment payee details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity
     */
    payee?: EntityLink;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentScheduleActivationResult
 */
export interface CapDentalPaymentScheduleCapDentalPaymentScheduleActivationResult {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleActivationResult
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleActivationResult
     */
    _type: string;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity>}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleActivationResult
     */
    activationMessages?: Array<CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity>;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleActivationResult
     */
    activationStatus?: string;
}

/**
 * The Root Entity of CAP Payment Schedule Domain.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntity {
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _archived?: boolean;
    /**
     * 
     * @type {RootEntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _key?: RootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _timestamp?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _version?: string;
    /**
     * A date when the schedule was created.
     * @type {Date}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    creationDate?: Date;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    originSource?: EntityLink;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentScheduleActivationResult}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    paymentScheduleActivationResult?: CapDentalPaymentScheduleCapDentalPaymentScheduleActivationResult;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    paymentTemplate?: EntityLink;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalScheduledPaymentEntity>}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    payments?: Array<CapDentalPaymentScheduleCapDentalScheduledPaymentEntity>;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity>}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    scheduleMessages?: Array<CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity>;
    /**
     * A unique ID, that is assigned to a new payment schedule.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    scheduleNumber?: string;
    /**
     * State of a payment schedule lifecycle.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    state?: string;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess
 */
export interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess {
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess
     */
    response?: string;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentScheduleEntity}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess
     */
    success?: CapDentalPaymentScheduleCapDentalPaymentScheduleEntity;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody
 */
export interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody {
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody
     */
    body?: CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody
     */
    requestId?: string;
}

/**
 * Stores Payment Schedule messages
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    _type: string;
    /**
     * Message code.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    code?: string;
    /**
     * Message text.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    message?: string;
    /**
     * Message severity.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    severity?: string;
    /**
     * Message source.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    source?: string;
}

/**
 * Defines payment transaction information.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
 */
export interface CapDentalPaymentScheduleCapDentalScheduledPaymentEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    _type: string;
    /**
     * A date when the payment was created.
     * @type {Date}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    creationDate?: Date;
    /**
     * Defines if payment is incoming or outgoing.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    direction?: string;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleMessageType>}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    messages?: Array<CapDentalPaymentScheduleMessageType>;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentDetailsEntity}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    paymentDetails?: CapDentalPaymentScheduleCapDentalPaymentDetailsEntity;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    paymentNetAmount?: Money;
    /**
     * A unique ID, that is assigned to a new payment.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    paymentNumber?: string;
}

/**
 * Defines a message that can be forwarded to the user on some exceptions.
 * @export
 * @interface CapDentalPaymentScheduleMessageType
 */
export interface CapDentalPaymentScheduleMessageType {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    _type: string;
    /**
     * Message code.
     * @type {string}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    code?: string;
    /**
     * Message text.
     * @type {string}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    message?: string;
    /**
     * Message severity.
     * @type {string}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    severity?: string;
    /**
     * Message source.
     * @type {string}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    source?: string;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleTerm
 */
export interface CapDentalPaymentScheduleTerm {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleTerm
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleTerm
     */
    _type: string;
    /**
     * 
     * @type {Date}
     * @memberof CapDentalPaymentScheduleTerm
     */
    effectiveDate?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapDentalPaymentScheduleTerm
     */
    expirationDate?: Date;
}

/**
 * 
 * @export
 * @interface DentalInternalCapUserCarryingEntity
 */
export interface DentalInternalCapUserCarryingEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof DentalInternalCapUserCarryingEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof DentalInternalCapUserCarryingEntity
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof DentalInternalCapUserCarryingEntity
     */
    userId?: string;
}

/**
 * 
 * @export
 * @interface EndpointFailure
 */
export interface EndpointFailure {
    /**
     * 
     * @type {ErrorHolder}
     * @memberof EndpointFailure
     */
    data?: ErrorHolder;
    /**
     * 
     * @type {boolean}
     * @memberof EndpointFailure
     */
    failure?: boolean;
    /**
     * 
     * @type {number}
     * @memberof EndpointFailure
     */
    httpCode?: number;
}

/**
 * 
 * @export
 * @interface EndpointFailureFailure
 */
export interface EndpointFailureFailure {
    /**
     * 
     * @type {EndpointFailure}
     * @memberof EndpointFailureFailure
     */
    failure?: EndpointFailure;
    /**
     * 
     * @type {string}
     * @memberof EndpointFailureFailure
     */
    response?: string;
}

/**
 * 
 * @export
 * @interface EndpointFailureFailureBody
 */
export interface EndpointFailureFailureBody {
    /**
     * 
     * @type {EndpointFailureFailure}
     * @memberof EndpointFailureFailureBody
     */
    body?: EndpointFailureFailure;
    /**
     * 
     * @type {boolean}
     * @memberof EndpointFailureFailureBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof EndpointFailureFailureBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface EntityKey
 */
export interface EntityKey {
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    parentId?: string;
    /**
     * 
     * @type {number}
     * @memberof EntityKey
     */
    revisionNo?: number;
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    rootId?: string;
}

/**
 * 
 * @export
 * @interface EntityLink
 */
export interface EntityLink {
    /**
     * 
     * @type {string}
     * @memberof EntityLink
     */
    _uri?: string;
}

/**
 * 
 * @export
 * @interface ErrorHolder
 */
export interface ErrorHolder {
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    code?: string;
    /**
     * 
     * @type {any}
     * @memberof ErrorHolder
     */
    details?: any;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    errorCode?: string;
    /**
     * 
     * @type {Array<ErrorHolder>}
     * @memberof ErrorHolder
     */
    errors?: Array<ErrorHolder>;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    field?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    logReference?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    message?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    path?: string;
}

/**
 * 
 * @export
 * @interface Money
 */
export interface Money {
    /**
     * 
     * @type {number}
     * @memberof Money
     */
    amount: number;
    /**
     * 
     * @type {string}
     * @memberof Money
     */
    currency: string;
}

/**
 * 
 * @export
 * @interface RootEntityKey
 */
export interface RootEntityKey {
    /**
     * 
     * @type {number}
     * @memberof RootEntityKey
     */
    revisionNo?: number;
    /**
     * 
     * @type {string}
     * @memberof RootEntityKey
     */
    rootId?: string;
}

/**
 * 
 * @export
 * @interface VerifyDentalPaymentScheduleActivationInputs
 */
export interface VerifyDentalPaymentScheduleActivationInputs {
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentScheduleEntity}
     * @memberof VerifyDentalPaymentScheduleActivationInputs
     */
    schedule?: CapDentalPaymentScheduleCapDentalPaymentScheduleEntity;
    /**
     * 
     * @type {DentalInternalCapUserCarryingEntity}
     * @memberof VerifyDentalPaymentScheduleActivationInputs
     */
    userRequest?: DentalInternalCapUserCarryingEntity;
}

/**
 * 
 * @export
 * @interface VerifyDentalPaymentScheduleActivationInputsBody
 */
export interface VerifyDentalPaymentScheduleActivationInputsBody {
    /**
     * 
     * @type {VerifyDentalPaymentScheduleActivationInputs}
     * @memberof VerifyDentalPaymentScheduleActivationInputsBody
     */
    body: VerifyDentalPaymentScheduleActivationInputs;
    /**
     * 
     * @type {boolean}
     * @memberof VerifyDentalPaymentScheduleActivationInputsBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof VerifyDentalPaymentScheduleActivationInputsBody
     */
    requestId?: string;
}


/**
 * DefaultApi - fetch parameter creator
 * @export
 */
export const DefaultApiFetchParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * No description provided
         * @param {VerifyDentalPaymentScheduleActivationInputsBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCommonCommonV1VerifyDentalPaymentScheduleActivationPost(params: { body?: VerifyDentalPaymentScheduleActivationInputsBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/common/common/v1/verifyDentalPaymentScheduleActivation`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"VerifyDentalPaymentScheduleActivationInputsBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function(configuration?: Configuration) {
    return {
        /**
         * No description provided
         * @param {VerifyDentalPaymentScheduleActivationInputsBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCommonCommonV1VerifyDentalPaymentScheduleActivationPost(params: { body?: VerifyDentalPaymentScheduleActivationInputsBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCommonCommonV1VerifyDentalPaymentScheduleActivationPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
    }
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (configuration?: Configuration, fetch?: FetchAPI, basePath?: string) {
    return {
        /**
         * No description provided
         * @param {VerifyDentalPaymentScheduleActivationInputsBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCommonCommonV1VerifyDentalPaymentScheduleActivationPost(params: { body?: VerifyDentalPaymentScheduleActivationInputsBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCommonCommonV1VerifyDentalPaymentScheduleActivationPost(params, options)(fetch, basePath);
        },
    };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
    /**
     * No description provided
     * @param {VerifyDentalPaymentScheduleActivationInputsBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCommonCommonV1VerifyDentalPaymentScheduleActivationPost(params: { body?: VerifyDentalPaymentScheduleActivationInputsBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCommonCommonV1VerifyDentalPaymentScheduleActivationPost(params, options)(this.fetch, this.basePath);
    }

}

