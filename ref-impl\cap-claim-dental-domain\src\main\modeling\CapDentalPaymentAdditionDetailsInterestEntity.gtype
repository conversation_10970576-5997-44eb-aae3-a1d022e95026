@Description("Interest addition details are described in this entity.")
BaseType CapDentalPaymentAdditionDetailsInterestEntity {

    @Description("Amount to change the interests calculation baseline.")
    Attr interestCalculateOnAmount: Money

    @Description("Number of days that has received.")
    Attr interestDaysFromReceivedNumber: Integer

    @Description("Defines the date up to when interest were paid .")
    Attr interestPaidUpToDate: Datetime

    @Description("Defines the rate based on which Interest was calculated.")
    Attr interestRate: Decimal

    @Description("Interest state according to which interest are caculated.")
    Attr interestState: String

    @Description("Interest amount threshold.")
    Attr interestThresholdAmount: Money
}