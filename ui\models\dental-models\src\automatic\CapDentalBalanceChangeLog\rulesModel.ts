// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_CAPDENTALBALANCECHANGELOG } from "./kraken_model_tree_CapDentalBalanceChangeLog"

let name = "CapDentalBalanceChangeLog"

let namespace = "CapDentalBalanceChangeLog"

export type CapDentalBalanceChangeLogEntryPointName = "CapDentalBalanceChangeLog:Init Balance Change Log Validation"

let entryPointNames = [
    "CapDentalBalanceChangeLog:Init Balance Change Log Validation"
] as CapDentalBalanceChangeLogEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_CAPDENTALBALANCECHANGELOG as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_CapDentalBalanceChangeLog = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree}, 'rules-model')));
