import React, {useState} from 'react'
import {sumBy, uniq} from 'lodash'
import {observer} from 'mobx-react'

import {MoneyFormat} from '@eisgroup/dental-core'
import {CapDentalBalance, CapDentalSettlement} from '@eisgroup/dental-models'
import {LocalizationUtils, t} from '@eisgroup/i18n'
import {LookupLabel} from '@eisgroup/react-components'
import {Collapse, ColumnProps, Table} from '@eisgroup/ui-kit'
import {ActionChevronRightMedium} from '@eisgroup/ui-kit-icons'

import {EntityLink} from '../../../../../utils/helpers/entities'
import {
    RECALCULATED_PAYMENTS,
    RECALCULATED_PAYMENTS_HEADER,
    RECALCULATED_PAYMENTS_SUMMARY,
    RECALCULATED_PAYMENTS_SUMMARY_CARD,
    RECALCULATED_PAYMENTS_SUMMARY_CARD_DATA,
    RECALCULATED_PAYMENTS_SUMMARY_CARD_TITLE,
    RECALCULATED_PAYMENTS_TABLE
} from './classnames'
import {RecalculatedPaymentExpand} from './RecalculatedPaymentExpand'

import CapDentalBalanceEntity = CapDentalBalance.CapDentalBalanceEntity
import CapDentalBalanceItemEntity = CapDentalBalance.CapDentalBalanceItemEntity
import dateValue = LocalizationUtils.dateValue
import CapDentalSettlementEntity = CapDentalSettlement.CapDentalSettlementEntity

const {Panel} = Collapse

interface RecalculatedPaymentsProps {
    loading?: boolean
    balance: CapDentalBalanceEntity | undefined
    settlements: CapDentalSettlementEntity[]
}

export const RecalculatedPayments: React.FC<RecalculatedPaymentsProps> = observer(({balance, loading, settlements}) => {
    const [isExpanded, setIsExpanded] = useState(true)
    const [expandedRows, setExpandedRows] = useState<string[]>(['1'])

    const paymentAmountTotal = sumBy(balance?.balanceItems, balanceItem =>
        balanceItem.actualNetAmount?.amount ? balanceItem.actualNetAmount.amount : 0
    )
    const shouldHavePaidTotal = sumBy(balance?.balanceItems, balanceItem =>
        balanceItem.scheduledNetAmount?.amount ? balanceItem.scheduledNetAmount.amount : 0
    )

    const columns: ColumnProps<CapDentalBalanceItemEntity>[] = [
        {
            title: t('dental-product:dental-claim-balance-balance-recalculated-payments-payment-date'),
            dataIndex: 'paymentDate',
            key: 'paymentDate',
            width: '15%',
            align: 'left',
            render: (text: string, record: CapDentalBalanceItemEntity) => {
                if (record.paymentDate) {
                    return dateValue(record.paymentDate).toString()
                }
                return ''
            }
        },
        {
            title: t('dental-product:dental-claim-balance-balance-recalculated-payments-id'),
            dataIndex: 'id',
            key: 'id',
            width: '15%',
            align: 'left',
            render: (text: string, record: CapDentalBalanceItemEntity) => {
                return <span>{record.paymentNumber ?? t('dental-product:not_available')}</span>
            }
        },
        {
            title: t('dental-product:dental-claim-balance-balance-recalculated-payments-entities'),
            dataIndex: 'entities',
            key: 'entities',
            align: 'left',
            render: (text: string, record: CapDentalBalanceItemEntity) => {
                const rootIds = record.actualAllocations
                    .flatMap(allocation => allocation?.allocationSource?._uri)
                    .filter(Boolean)
                    .map(item => EntityLink.from(item || '').rootId)
                const allClaimTypes = settlements
                    ?.map(settlement => {
                        if (rootIds.includes(settlement._key.rootId)) {
                            return settlement?.settlementLossInfo?.claimData?.transactionType
                        }
                        return undefined
                    })
                    .filter(Boolean)

                return (
                    <span>
                        {uniq(allClaimTypes).map(claimType => {
                            return (
                                <LookupLabel
                                    key={claimType}
                                    lookup='CapDNTransactionType'
                                    code={claimType}
                                    emptyLabel={t('dental-product:not_available')}
                                />
                            )
                        })}
                    </span>
                )
            }
        },
        {
            title: t('dental-product:dental-claim-balance-balance-recalculated-payments-amount'),
            dataIndex: 'paymentAmount',
            key: 'paymentAmount',
            align: 'right',
            render: (text: string, record: CapDentalBalanceItemEntity) => {
                return (
                    <span>
                        <MoneyFormat value={record?.actualNetAmount?.amount} />
                    </span>
                )
            }
        },
        {
            title: t('dental-product:dental-claim-balance-balance-recalculated-payments-should-have-paid'),
            dataIndex: 'shouldHavePaid',
            key: 'shouldHavePaid',
            align: 'right',
            render: (text: string, record: CapDentalBalanceItemEntity) => {
                return (
                    <span>
                        <MoneyFormat value={record?.scheduledNetAmount?.amount} />
                    </span>
                )
            }
        },
        {
            title: t('dental-product:dental-claim-balance-balance-recalculated-payments-balance-adjustment'),
            dataIndex: 'balanceAdjustment',
            key: 'balanceAdjustment',
            align: 'right',
            render: (text: string, record: CapDentalBalanceItemEntity) => {
                const {balancedNetAmount, actualNetAmount} = record
                const tempActualNetAmount = actualNetAmount ? actualNetAmount.amount : Number(0)
                const tempBalancedNetAmount = balancedNetAmount ? balancedNetAmount.amount : Number(0)
                const balanceAdjustment = tempBalancedNetAmount - tempActualNetAmount
                return (
                    <span>
                        <MoneyFormat value={balanceAdjustment} />
                    </span>
                )
            }
        },
        {
            title: t('dental-product:dental-claim-balance-balance-recalculated-payments-balance'),
            dataIndex: 'balance',
            key: 'balance',
            align: 'right',
            render: (text: string, record: CapDentalBalanceItemEntity) => {
                const {scheduledNetAmount, actualNetAmount} = record
                const tempScheduledNetAmount = scheduledNetAmount ? scheduledNetAmount.amount : Number(0)
                const tempActualNetAmount = actualNetAmount ? actualNetAmount.amount : Number(0)
                return <MoneyFormat value={tempScheduledNetAmount - tempActualNetAmount} />
            }
        }
    ]

    const headerContent = (
        <div className={RECALCULATED_PAYMENTS_HEADER}>
            <span>{t('dental-product:dental-claim-balance-balance-recalculated-payments-title')}</span>
            <div className={RECALCULATED_PAYMENTS_SUMMARY}>
                <div className={RECALCULATED_PAYMENTS_SUMMARY_CARD}>
                    <div className={RECALCULATED_PAYMENTS_SUMMARY_CARD_TITLE}>
                        {t('dental-product:dental-claim-balance-balance-recalculated-payments-amount')}
                    </div>
                    <div className={RECALCULATED_PAYMENTS_SUMMARY_CARD_DATA}>
                        {paymentAmountTotal !== undefined ? (
                            <MoneyFormat value={paymentAmountTotal} />
                        ) : (
                            t('dental-product:not_available')
                        )}
                    </div>
                </div>
                <div className={RECALCULATED_PAYMENTS_SUMMARY_CARD}>
                    <div className={RECALCULATED_PAYMENTS_SUMMARY_CARD_TITLE}>
                        {t('dental-product:dental-claim-balance-balance-recalculated-payments-should-have-paid')}
                    </div>
                    <div className={RECALCULATED_PAYMENTS_SUMMARY_CARD_DATA}>
                        {shouldHavePaidTotal !== undefined ? (
                            <MoneyFormat value={shouldHavePaidTotal} />
                        ) : (
                            t('dental-product:not_available')
                        )}
                    </div>
                </div>
            </div>
        </div>
    )

    return (
        <div className={RECALCULATED_PAYMENTS}>
            <Collapse
                bordered={false}
                activeKey={isExpanded ? ['1'] : []}
                onChange={() => setIsExpanded(!isExpanded)}
                isWhite
            >
                <Panel header={headerContent} key='1'>
                    <Table<CapDentalBalanceItemEntity>
                        className={RECALCULATED_PAYMENTS_TABLE}
                        columns={columns}
                        dataSource={balance?.balanceItems ?? []}
                        pagination={false}
                        size='small'
                        expandIcon={({expanded, record, onExpand}) => {
                            return (
                                <ActionChevronRightMedium onClick={() => onExpand(record)} rotate={expanded ? 90 : 0} />
                            )
                        }}
                        onExpand={(expand: boolean, record: CapDentalBalanceItemEntity) => {
                            if (expand) {
                                setExpandedRows(expandRows => [...expandRows, record._key.id ?? ''])
                            } else {
                                setExpandedRows(expandRows => expandRows.filter(key => record._key.id !== key))
                            }
                        }}
                        expandedRowRender={record => (
                            <RecalculatedPaymentExpand settlements={settlements} balanceItem={record} />
                        )}
                        expandedRowKeys={expandedRows}
                        onExpandedRowsChange={expandKeys => setExpandedRows(expandKeys as string[])}
                        // expandRowByClick
                        rowKey={row => row._key.id ?? ''}
                        loading={loading}
                    />
                </Panel>
            </Collapse>
        </div>
    )
})
