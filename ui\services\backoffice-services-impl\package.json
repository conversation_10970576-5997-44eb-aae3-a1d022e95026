{"name": "@eisgroup/dental-backoffice-services-impl", "version": "25.7.0-SNAPSHOT.202508080205", "description": "Claim side backoffice services implementation", "main": "target/dist/js/src/index.js", "typings": "target/dist/definitions/src/index.d.ts", "files": ["target/dist/"], "dependencies": {"@eisgroup/common-types": "100.0.0", "@eisgroup/dispatch": "100.0.0", "@eisgroup/environment": "100.0.0", "@eisgroup/ioc": "100.0.0", "@eisgroup/bam-common": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/bam-services": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/bam-gateway-client": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/data.either": "100.0.0", "@eisgroup/sidebar-service-api": "25.7.0-SNAPSHOT.202508080205", "@eisgroup/dental-product-services": "25.7.0-SNAPSHOT.202508080205", "@eisgroup/claim-dental-customer-service-api": "25.7.0-SNAPSHOT.202508080205", "@eisgroup/claim-dental-policy-service-api": "25.7.0-SNAPSHOT.202508080205", "@eisgroup/efolder-services": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/efolder-gateway-client": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/lookups": "100.0.0", "@eisgroup/common": "100.0.0", "@eisgroup/cem-gateway-client": "25.7.0-SNAPSHOT.202505051520", "@eisgroup/census-gateway-client": "25.7.0-SNAPSHOT.202505051520", "@eisgroup/notes-common": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/notes-gateway-client": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/notes-services": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/policy-gateway-client": "25.6.0-RC.4", "@eisgroup/work-common": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/workium-ui": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/work-services": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/work-gateway-client": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/i18n": "100.0.0"}, "peerDependencies": {"@eisgroup/common-types": "100.0.0", "@eisgroup/environment": "100.0.0", "@eisgroup/ioc": "100.0.0", "inversify": "^3.3.0", "mobx": "~4.15.7", "rxjs": "5.5.7"}, "scripts": {"build": "tsc --project tsconfig.build.json", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "lint": "yarn eslint \"src/**/*.ts{,x}\"", "test": "vitest run --passWithNoTests"}, "v20ModuleType": "reference implementation library"}