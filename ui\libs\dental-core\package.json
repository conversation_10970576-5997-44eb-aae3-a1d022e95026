{"name": "@eisgroup/dental-core", "version": "25.7.0-SNAPSHOT.202508120630", "description": "Dental Product Module", "license": "UNLICENSED", "publishConfig": {"registry": "https://genesis-npm-release.exigengroup.com/repository/genesis-npm-release/"}, "main": "target/dist/js/src/index.js", "typings": "target/dist/definitions/src/index.d.ts", "files": ["target/dist/"], "dependencies": {"proxy-memoize": "3.0.1", "styled-components": "^4.3.2", "@eisgroup/common": "100.0.0", "@eisgroup/builder": "25.8.0-SNAPSHOT.202507152214", "@eisgroup/builder-integration": "25.8.0-SNAPSHOT.202507152214", "@eisgroup/form": "100.0.0", "@eisgroup/form-core": "100.0.0", "@eisgroup/models-api": "100.0.0", "@eisgroup/kraken-form-api": "100.0.0", "@eisgroup/kraken-form-runtime": "100.0.0", "@eisgroup/dispatch": "100.0.0", "@eisgroup/cache": "100.0.0", "@eisgroup/data.either": "100.0.0", "@eisgroup/ui-temporals": "100.0.0", "@eisgroup/react-components": "100.0.0", "@eisgroup/form-kraken-core": "25.8.0-202506231223", "@eisgroup/kraken-core": "25.8.0-202506231223", "@eisgroup/i18n": "100.0.0", "@eisgroup/ioc": "100.0.0", "@eisgroup/ui-kit": "100.0.0", "@eisgroup/ui-kit-icons": "^100.0.0", "@eisgroup/lookups": "100.0.0", "@eisgroup/common-claim-shares": "25.10.0-SNAPSHOT.202507152214", "final-form-arrays": "3.0.2", "lodash": "~4.17.15", "@types/classnames": "2.2.3", "@types/react": "^16.8.24", "@types/react-dom": "^16.8.5", "inversify": "^3.3.0", "@types/react-router": "3.0.25", "mobx-react": "^6.1.8"}, "peerDependencies": {"@eisgroup/i18n": "100.0.0"}, "scripts": {"build": "tsc --project tsconfig.build.json", "postbuild": "cpx \"./src/**/*.{json,less,ico,html,gif,svg,png,ttf,woff,woff2,js,jsx}\" ./target/dist/js/src", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "lint": "infra-scripts lint --eslint", "test": "vitest run --passWithNoTests", "test:watch": "vitest"}}