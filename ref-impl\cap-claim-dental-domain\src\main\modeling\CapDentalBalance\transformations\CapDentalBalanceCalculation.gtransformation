Transformation CapDentalBalanceCalculation {
    Input {
         DentalInternal.CapDentalFinancialCalculateLossBalanceDataInput as input
    }
    Output {
        CapDentalBalance.CapDentalBalanceCalculationRulesOutput
    }

    Var request is createRequest(input)
    Var response is ExecuteRules("claim-dental-financial", "_api_balance_calculation", request)

    Attr balances is SafeInvoke(response.balances, addOriginSource(response.balances, input))

    Producer createRequest(input) {
        Attr actualPayments is getActualPayments(input.originSource).actualPayments
        Attr scheduledPayments is getLatestScheduledPayments(input.originSource).scheduledPayments
    }

    Producer getActualPayments(originSource) {
        Var allIndexedPayments is Load(createOriginKey(originSource), "CapDentalPaymentIndex", "CapDentalPaymentIdxEntity")
        Var resolvedPayments is resolvePayments(allIndexedPayments).resolvedPayment
        Attr actualPayments is SafeInvoke(resolvedPayments,
            convertToBalancePayment(resolvedPayments, "CapDentlaPaymentToBalancePayment", "CapDentalPaymentDefinition").balancePayment)
    }

    Producer getLatestScheduledPayments(originSource) {
        Var scheduleIndexes is Load(createOriginKey(originSource), "CapDentalPaymentScheduleIndex", "CapDentalPaymentScheduleIdxEntity")
        Var latestScheduleIndex is First(scheduleIndexes[activeSchedules]<timestampDesc>)
        Var paymentSchedule is ExtLink(SafeInvoke(latestScheduleIndex.paymentSchedule, AsExtLink(latestScheduleIndex.paymentSchedule)))
        Attr scheduledPayments is SafeInvoke(paymentSchedule.payments,
            convertToBalancePayment(paymentSchedule.payments, "CapDetanlScheduledPaymentToBalancePayment", "CapDentalPaymentSchedule").balancePayment)
    }

    Producer resolvePayments(indexedPayment) {
        Attr resolvedPayment is ExtLink(AsExtLink(indexedPayment.paymentId))
    }

    Producer convertToBalancePayment(payment, transformationName, modelName) {
        Attr balancePayment is Transform(transformationName, addModelName(payment, modelName))
    }

    Producer createOriginKey(originSource) {
        Attr originSource is originSource._uri
    }

    // Needed due to payments not having _modelName by default
    @Passthrough("payment")
    Producer addModelName(payment, modelName) {
        Attr _modelName is modelName
    }

    @Passthrough("balance")
    Producer addOriginSource(balance, input) {
        Attr originSource is input.originSource
        Attr balanceItems is Ternary(balance.balanceItems == Null(), FlatMap(Null()), balance.balanceItems)
    }

    Filter activeSchedules {
        state == "Open" || state == "Active" || state == "Completed" || state == "Suspended"
    }

    Sort timestampDesc {
        "_timestamp" -> "DESC"
    }
}