<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:design="http://flowable.org/design" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" design:palette="flowable-work-process-palette">
  <process id="generateDentalBalanceChangeLog" name="generateDentalBalanceChangeLog" isExecutable="true">
    <extensionElements>
      <design:stencilid><![CDATA[BPMNDiagram]]></design:stencilid>
      <design:creationdate><![CDATA[2025-07-28T06:42:41.645Z]]></design:creationdate>
      <design:modificationdate><![CDATA[2025-07-28T06:57:00.734Z]]></design:modificationdate>
    </extensionElements>
    <subProcess id="initBalances" name="Init Balances">
      <extensionElements>
        <design:stencilid><![CDATA[ExpandedSubProcess]]></design:stencilid>
      </extensionElements>
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${balanceChangeLogResponse.body.success.balanceChangeLogs}" flowable:elementVariable="balanceChangeLog">
        <extensionElements></extensionElements>
      </multiInstanceLoopCharacteristics>
      <serviceTask id="InitBalanceChangeLog" name="Init Balance Change Logs" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
        <extensionElements>
          <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
          <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
          <flowable:eventInParameter source="${requestId}" target="requestId"></flowable:eventInParameter>
          <flowable:eventInParameter source="{'originSource': ${balanceChangeLog.originSource}, 'payee': ${balanceChangeLog.payee}, 'entity': ${balanceChangeLog.balanceChangeLogDetails}}" target="payload"></flowable:eventInParameter>
          <flowable:eventInParameter source="1" target="_modelVersion"></flowable:eventInParameter>
          <flowable:eventInParameter source="CapDentalBalanceChangeLog" target="_modelName"></flowable:eventInParameter>
          <flowable:eventInParameter source="initBalanceChangeLog" target="commandName"></flowable:eventInParameter>
          <flowable:channelKey><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
          <flowable:triggerEventCorrelationParameter name="requestId" value="${requestId}"></flowable:triggerEventCorrelationParameter>
          <flowable:startEventCorrelationConfiguration><![CDATA[startNewInstance]]></flowable:startEventCorrelationConfiguration>
          <design:stencilid><![CDATA[SendAndReceiveEventTask]]></design:stencilid>
          <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
          <design:sendsynchronously><![CDATA[false]]></design:sendsynchronously>
        </extensionElements>
      </serviceTask>
      <startEvent id="startEvent2" flowable:formFieldValidation="true">
        <extensionElements>
          <flowable:work-form-field-validation><![CDATA[false]]></flowable:work-form-field-validation>
          <design:stencilid><![CDATA[StartNoneEvent]]></design:stencilid>
        </extensionElements>
      </startEvent>
      <endEvent id="endEvent2">
        <extensionElements>
          <design:stencilid><![CDATA[EndNoneEvent]]></design:stencilid>
        </extensionElements>
      </endEvent>
      <sequenceFlow id="flow6" sourceRef="InitBalanceChangeLog" targetRef="endEvent2">
        <extensionElements>
          <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
        </extensionElements>
      </sequenceFlow>
      <sequenceFlow id="flow5" sourceRef="startEvent2" targetRef="InitBalanceChangeLog">
        <extensionElements>
          <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
        </extensionElements>
      </sequenceFlow>
    </subProcess>
    <serviceTask id="generateBalanceApi" name="Rule Engine: Generate Balance Change Logs" flowable:parallelInSameTransaction="true" flowable:type="http">
      <extensionElements>
        <flowable:field name="requestMethod">
          <flowable:string><![CDATA[POST]]></flowable:string>
        </flowable:field>
        <flowable:field name="requestUrl">
          <flowable:expression><![CDATA[${HTTPHelper.serviceDiscoveryQuery(execution).serviceGroup('CapDentalBalanceChangeLog').serviceName('balanceChangeLogGeneration').query()}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestHeaders">
          <flowable:expression><![CDATA[Authorization: ${HTTPHelper.systemUserToken()}
Accept-Encoding: identity]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestBody">
          <flowable:expression><![CDATA[{body:{'activatePayload' : ${activatePayload}, "balancePayload" : ${balancePayload}, "balanceTransactionTypeCd": ${balanceTransactionTypeCd}, "_type": "CapDentalBalanceChangeLogGenerationInput"}}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="disallowRedirects">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="responseVariableName">
          <flowable:string><![CDATA[balanceChangeLogResponse]]></flowable:string>
        </flowable:field>
        <flowable:field name="ignoreException">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveRequestVariables">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseParameters">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseParametersTransient">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseVariableAsJson">
          <flowable:expression><![CDATA[${true}]]></flowable:expression>
        </flowable:field>
        <design:stencilid><![CDATA[HttpTask]]></design:stencilid>
        <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
      </extensionElements>
    </serviceTask>
    <exclusiveGateway id="exclusiveGateway" name="Are there any balance change logs?" default="flow4">
      <extensionElements>
        <design:stencilid><![CDATA[Exclusive_Databased_Gateway]]></design:stencilid>
        <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
      </extensionElements>
    </exclusiveGateway>
    <startEvent id="startEvent1" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:work-form-field-validation><![CDATA[false]]></flowable:work-form-field-validation>
        <design:stencilid><![CDATA[StartNoneEvent]]></design:stencilid>
      </extensionElements>
    </startEvent>
    <endEvent id="endEvent1">
      <extensionElements>
        <design:stencilid><![CDATA[EndNoneEvent]]></design:stencilid>
      </extensionElements>
    </endEvent>
    <sequenceFlow id="flow7" sourceRef="initBalances" targetRef="endEvent1">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="flow2" sourceRef="generateBalanceApi" targetRef="exclusiveGateway">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="flow1" sourceRef="startEvent1" targetRef="generateBalanceApi">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="flow3" name="There's at least one balance change log" sourceRef="exclusiveGateway" targetRef="initBalances">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
        <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${balanceChangeLogResponse.body.success.balanceChangeLogs != null && balanceChangeLogResponse.body.success.balanceChangeLogs.size() > 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow4" name="There's no balance change log" sourceRef="exclusiveGateway" targetRef="endEvent1">
      <extensionElements>
        <flowable:is-default-flow><![CDATA[true]]></flowable:is-default-flow>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
        <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
      </extensionElements>
    </sequenceFlow>
    <textAnnotation id="textAnnotation1">
      <extensionElements>
        <design:stencilid><![CDATA[TextAnnotation]]></design:stencilid>
        <design:text><![CDATA[Passed parameters:
        balancePayload
        balanceTransactionTypeCd
        activatePayload
        _uri]]></design:text>
      </extensionElements>
      <text>Passed parameters:
        balancePayload
        balanceTransactionTypeCd
        activatePayload
        _uri</text>
    </textAnnotation>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_generateDentalBalanceChangeLog">
    <bpmndi:BPMNPlane bpmnElement="generateDentalBalanceChangeLog" id="BPMNPlane_generateDentalBalanceChangeLog">
      <bpmndi:BPMNShape bpmnElement="initBalances" id="BPMNShape_initBalances">
        <omgdc:Bounds height="158.0" width="337.0" x="655.0" y="263.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="InitBalanceChangeLog" id="BPMNShape_InitBalanceChangeLog">
        <omgdc:Bounds height="80.0" width="100.0" x="773.5" y="301.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startEvent2" id="BPMNShape_startEvent2">
        <omgdc:Bounds height="30.0" width="30.0" x="677.0" y="326.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endEvent2" id="BPMNShape_endEvent2">
        <omgdc:Bounds height="28.0" width="28.0" x="920.0" y="327.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="textAnnotation1" id="BPMNShape_textAnnotation1">
        <omgdc:Bounds height="90.0" width="165.0" x="150.0" y="165.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="generateBalanceApi" id="BPMNShape_generateBalanceApi">
        <omgdc:Bounds height="80.0" width="100.0" x="280.0" y="302.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="exclusiveGateway" id="BPMNShape_exclusiveGateway">
        <omgdc:Bounds height="40.0" width="40.0" x="460.0" y="322.0"></omgdc:Bounds>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="195.0" x="389.8" y="288.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="140.0" y="327.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endEvent1" id="BPMNShape_endEvent1">
        <omgdc:Bounds height="28.0" width="28.0" x="1120.0" y="328.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow1" id="BPMNEdge_flow1" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="169.0" y="342.0"></omgdi:waypoint>
        <omgdi:waypoint x="279.0" y="342.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow2" id="BPMNEdge_flow2" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="380.0" y="342.0"></omgdi:waypoint>
        <omgdi:waypoint x="460.0" y="342.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow3" id="BPMNEdge_flow3" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="169.0" flowable:targetDockerY="79.0">
        <omgdi:waypoint x="500.0" y="342.0"></omgdi:waypoint>
        <omgdi:waypoint x="655.0" y="342.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="218.0" x="509.0" y="312.8"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow4" id="BPMNEdge_flow4" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="480.0" y="362.0"></omgdi:waypoint>
        <omgdi:waypoint x="480.0" y="515.0"></omgdi:waypoint>
        <omgdi:waypoint x="1134.0" y="515.0"></omgdi:waypoint>
        <omgdi:waypoint x="1134.0" y="356.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="169.0" x="490.0" y="371.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow5" id="BPMNEdge_flow5" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="706.0" y="341.0"></omgdi:waypoint>
        <omgdi:waypoint x="773.0" y="341.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow6" id="BPMNEdge_flow6" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="873.0" y="341.0"></omgdi:waypoint>
        <omgdi:waypoint x="920.0" y="341.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow7" id="BPMNEdge_flow7" flowable:sourceDockerX="169.0" flowable:sourceDockerY="79.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="991.0" y="342.0"></omgdi:waypoint>
        <omgdi:waypoint x="1120.0" y="342.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>