// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "ModelType"
  ],
  "moduleType":"CapBalanceChangeLog",
  "name":"CapDentalBalanceChangeLog",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapDentalBalanceChangeLogEntity"
  },
  "storeDeterminants":[
    "ModelName"
  ],
  "types":{
    "CapDentalBalanceChangeLogDetailsEntity":{
      "attributes":{
        "description":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Comments or reason user entered during transaction actions as creation cancellation etc"
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Comments or reason user entered during transaction actions as creation cancellation etc",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"description",
          "type":{
            "type":"STRING"
          }
        },
        "totalBalanceAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payee total balance amount after recalculation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payee total balance amount after recalculation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"totalBalanceAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "transactionNumber":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Transaction number if recalculation event is related to the payment or balance transaction."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Transaction number if recalculation event is related to the payment or balance transaction.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"transactionNumber",
          "type":{
            "type":"STRING"
          }
        },
        "transactionTypeCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Event that triggered balance recalculation."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.lookups.constraints.Lookup":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "CapDNBalanceLogTransactionType"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Event that triggered balance recalculation.",
              "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
            },
            "class com.eisgroup.genesis.lookups.constraints.Lookup":{
              "lookupName":"CapDNBalanceLogTransactionType"
            }
          },
          "name":"transactionTypeCd",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance log details."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance log details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"CapBalanceChangeLogDetails"
        }
      ],
      "baseTypes":[
        "CapBalanceChangeLogDetails"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Balance log details."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Balance log details.",
          "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceChangeLogDetailsEntity",
      "references":{
      }
    },
    "CapDentalBalanceChangeLogEntity":{
      "attributes":{
        "creationDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "The date when the entity was created."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"The date when the entity was created.",
              "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"creationDate",
          "type":{
            "type":"DATETIME"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "The Root Entity of Balance Change Log Domain."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      [
                        "CapBalanceChangeLog"
                      ]
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"The Root Entity of Balance Change Log Domain.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
                "CapBalanceChangeLog"
              ]
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "modelTypes":[
                  ]
                }
              },
              "parents":[
              ],
              "typeName":"RootEntity"
            }
          ],
          "typeName":"CapBalanceChangeLog"
        }
      ],
      "baseTypes":[
        "CapBalanceChangeLog"
      ],
      "extLinks":{
        "eventCaseLink":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the related case, used to search for the Balance Change Log."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the related case, used to search for the Balance Change Log.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"eventCaseLink",
          "targetType":"RootEntity"
        },
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the related claim."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the related claim.",
              "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        },
        "payee":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the CEM."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the CEM.",
              "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"payee",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "The Root Entity of Balance Change Log Domain."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"The Root Entity of Balance Change Log Domain.",
          "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceChangeLogEntity",
      "references":{
        "balanceChangeLogDetails":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance change log details."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.modeled.features.Embedded":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance change log details.",
              "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Embedded":{
            }
          },
          "name":"balanceChangeLogDetails",
          "type":"CapDentalBalanceChangeLogDetailsEntity"
        }
      }
    },
    "CapDentalBalanceChangeLogGenerationInput":{
      "attributes":{
        "activatePayload":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment or payment schedule entity"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment or payment schedule entity",
              "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
            }
          },
          "name":"activatePayload",
          "type":{
            "type":"JsonType"
          }
        },
        "balancePayload":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment or payment schedule entity"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment or payment schedule entity",
              "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
            }
          },
          "name":"balancePayload",
          "type":{
            "type":"JsonType"
          }
        },
        "balanceTransactionTypeCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Event that triggered balance recalculation."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Event that triggered balance recalculation.",
              "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
            }
          },
          "name":"balanceTransactionTypeCd",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceChangeLogGenerationInput",
      "references":{
      }
    },
    "CapDentalBalanceChangeLogGenerationRulesOutput":{
      "attributes":{
        "balanceChangeLogs":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of balance change logs that must be saves in the database."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of balance change logs that must be saves in the database.",
              "messageBundle":"domain-messages/CapDentalBalanceChangeLog/description"
            }
          },
          "name":"balanceChangeLogs",
          "type":{
            "type":"CapDentalBalanceChangeLogEntity"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceChangeLogGenerationRulesOutput",
      "references":{
      }
    },
    "JsonType":{
      "attributes":{
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"JsonType"
        }
      ],
      "baseTypes":[
        "JsonType"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"JsonType",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace CapDentalBalanceChangeLog {
    export type Variations = never


    export class CapDentalBalanceChangeLogDetailsEntity extends MAPI.BusinessEntity implements BusinessTypes.CapBalanceChangeLogDetails, MAPI.RootBusinessType {
    constructor() { super(CapDentalBalanceChangeLogDetailsEntity.name) }
        readonly _modelName: string = 'CapDentalBalanceChangeLog'
        readonly _modelType: string = 'CapBalanceChangeLog'
        readonly _modelVersion?: string = '1'
        readonly description?: string
        readonly totalBalanceAmount?: Money
        readonly transactionNumber?: string
        readonly transactionTypeCd?: string
    }

    export class CapDentalBalanceChangeLogEntity extends MAPI.BusinessEntity implements BusinessTypes.CapBalanceChangeLog, MAPI.RootBusinessType {
    constructor() { super(CapDentalBalanceChangeLogEntity.name) }
        readonly _modelName: string = 'CapDentalBalanceChangeLog'
        readonly _modelType: string = 'CapBalanceChangeLog'
        readonly _modelVersion?: string = '1'
        readonly balanceChangeLogDetails?: CapDentalBalanceChangeLogDetailsEntity
        readonly creationDate?: Date
        readonly eventCaseLink?: MAPI.ExternalLink
        readonly originSource?: MAPI.ExternalLink
        readonly payee?: MAPI.ExternalLink
    }

    export class CapDentalBalanceChangeLogGenerationInput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalBalanceChangeLogGenerationInput.name) }
        readonly activatePayload?: JsonType
        readonly balancePayload?: JsonType
        readonly balanceTransactionTypeCd?: string
    }

    export class CapDentalBalanceChangeLogGenerationRulesOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalBalanceChangeLogGenerationRulesOutput.name) }
        readonly balanceChangeLogs: CapDentalBalanceChangeLogEntity[] = []
    }

    export class JsonType extends MAPI.BusinessEntity implements BusinessTypes.JsonType {
    constructor() { super(JsonType.name) }
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapDentalBalanceChangeLogDetailsEntity, ()=> new CapDentalBalanceChangeLogDetailsEntity)
    factory.registerEntity(CapDentalBalanceChangeLogEntity, ()=> new CapDentalBalanceChangeLogEntity)
    factory.registerEntity(CapDentalBalanceChangeLogGenerationInput, ()=> new CapDentalBalanceChangeLogGenerationInput)
    factory.registerEntity(CapDentalBalanceChangeLogGenerationRulesOutput, ()=> new CapDentalBalanceChangeLogGenerationRulesOutput)
    factory.registerEntity(JsonType, ()=> new JsonType)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}