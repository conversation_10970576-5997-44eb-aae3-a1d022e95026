/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {observer} from 'mobx-react'

import {ClaimToggle} from '@eisgroup/dental-core'
import {useFormState} from '@eisgroup/form'
import {useTranslate} from '@eisgroup/i18n'

import {DENTAL_FINANCIAL_HEADER_WRAPPER} from '../classnames'
import {FINANCIALS_TAB_TOGGLES} from '../FinancialsTab'
import {PaymentActions} from './PaymentActions'

interface PaymentHeaderProps {
    activeToggle: string
    handleChangeToggle: (value: string) => void
}

export const FinancialToggle: React.FC<PaymentHeaderProps> = observer(({activeToggle, handleChangeToggle}) => {
    const {t} = useTranslate()
    const {paymentList} = useFormState().values

    return (
        <div className={DENTAL_FINANCIAL_HEADER_WRAPPER}>
            <ClaimToggle
                toggleGroups={[
                    {
                        value: FINANCIALS_TAB_TOGGLES.PAYMENTS,
                        label: t('dental-product:dental-claim-financial-toggle-payments'),
                        showCount: true,
                        count: paymentList.length
                    },
                    {
                        value: FINANCIALS_TAB_TOGGLES.RECOVERIES,
                        label: t('dental-product:dental-claim-financial-toggle-recoveries'),
                        showCount: true,
                        count: 0
                    },
                    {
                        value: FINANCIALS_TAB_TOGGLES.BANLANCE,
                        label: t('dental-product:dental-claim-financial-toggle-balance'),
                        showCount: false
                    }
                ]}
                activeToggle={activeToggle}
                handleChangeView={handleChangeToggle}
            />
            {activeToggle === FINANCIALS_TAB_TOGGLES.PAYMENTS ? <PaymentActions /> : null}
        </div>
    )
})
