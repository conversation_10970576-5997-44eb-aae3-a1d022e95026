import React from 'react'
import {fireEvent, render, screen} from '@testing-library/react'
import {beforeEach, describe, expect, it, vi} from 'vitest'

import {CapDentalBalance, CapDentalSettlement} from '@eisgroup/dental-models'
import {LocalizationUtils} from '@eisgroup/i18n'

import {RecalculatedPayments} from '../../../../../../src/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPayments'

import dateValue = LocalizationUtils.dateValue

vi.mock('@eisgroup/react-components', async importOriginal => ({
    ...(await importOriginal()),
    LookupLabel: ({code, emptyLabel}: {code: string; emptyLabel: string}) => (
        <span data-testid='lookup-label'>{code || emptyLabel}</span>
    )
}))
vi.mock('@eisgroup/ui-kit-icons', async importOriginal => ({
    ...(await importOriginal()),
    ActionChevronRightMedium: ({onClick, rotate}: {onClick: () => void; rotate: number}) => (
        <button onClick={onClick} data-testid='expand-icon'>
            expand
        </button>
    )
}))

vi.mock(
    '../../../../../../src/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPaymentExpand',
    () => {
        return {
            RecalculatedPaymentExpand: () => <div data-testid='payment-expand'>Expanded Content</div>
        }
    }
)

describe('RecalculatedPayments', () => {
    const mockBalance = {
        balanceItems: [
            {
                _key: {rootId: '1'},
                paymentDate: new Date('2025-08-07'),
                paymentNumber: 'PAY001',
                actualNetAmount: {amount: 100},
                scheduledNetAmount: {amount: 150},
                balancedNetAmount: {amount: 120},
                actualAllocations: [
                    {
                        allocationSource: {
                            _uri: 'gentity://CapSettlement/CapDentalSettlement//7461f7d7-fa85-423b-9adc-f0f89f823eb3/1'
                        }
                    }
                ]
            }
        ]
    } as CapDentalBalance.CapDentalBalanceEntity

    const mockSettlements = [
        {
            _key: {rootId: '7461f7d7-fa85-423b-9adc-f0f89f823eb3'},
            settlementLossInfo: {
                claimData: {
                    transactionType: 'CLAIM'
                }
            }
        }
    ] as CapDentalSettlement.CapDentalSettlementEntity[]

    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('should render header with correct totals', () => {
        const {container} = render(<RecalculatedPayments balance={mockBalance} settlements={mockSettlements} />)

        const cardData = container.querySelectorAll('.gen-recalculated-payments-summary-card-data')
        expect(cardData.length).toBe(2)
        expect(cardData?.[0]?.querySelector('span span')?.innerHTML).toBe('$100.00')
        expect(cardData?.[1]?.querySelector('span span')?.innerHTML).toBe('$150.00')
    })

    it('should render table with correct columns and data', () => {
        render(<RecalculatedPayments balance={mockBalance} settlements={mockSettlements} />)

        expect(screen.getByText(dateValue(new Date('2025-08-07')).toString())).toBeInTheDocument()
        expect(screen.getByText('PAY001')).toBeInTheDocument()
        expect(screen.getByTestId('lookup-label')).toHaveTextContent('CLAIM')
    })

    it('should handle row expansion', () => {
        render(<RecalculatedPayments balance={mockBalance} settlements={mockSettlements} />)

        const expandIcon = screen.getByTestId('expand-icon')
        fireEvent.click(expandIcon)

        expect(screen.getByTestId('payment-expand')).toBeInTheDocument()
    })

    it('should calculate balance adjustment correctly', () => {
        const {container} = render(<RecalculatedPayments balance={mockBalance} settlements={mockSettlements} />)

        const balanceAdjustment = container.querySelectorAll('td')?.[6]
        expect(balanceAdjustment.textContent).toBe('$20.00')
    })

    it('should calculate final balance correctly', () => {
        const {container} = render(<RecalculatedPayments balance={mockBalance} settlements={mockSettlements} />)

        const finalBalance = container.querySelectorAll('td')?.[7]
        expect(finalBalance.textContent).toBe('$50.00')
    })

    it('should handle empty data', () => {
        const emptyBalance = {balanceItems: []} as unknown as CapDentalBalance.CapDentalBalanceEntity
        render(<RecalculatedPayments balance={emptyBalance} settlements={[]} />)

        expect(screen.getByText('No Data')).toBeInTheDocument()
    })
})
