/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {useMemo} from 'react'
import {isEmpty, omit} from 'lodash'
import {observer} from 'mobx-react'

import {MultiSteps, VALIDATE_SCOPE} from '@eisgroup/dental-core'
import {Step, StepValidationConfig, useForm, useFormState} from '@eisgroup/form'
import {t} from '@eisgroup/i18n'
import {kraken} from '@eisgroup/kraken-form-api'
import {RoutingUtils} from '@eisgroup/react-routing'

import {useKrakenEvaluation} from '../../../../kraken'
import {ClaimInfo, ClaimPolicyAndPatient, ReviewClaimInfo} from '../../../../shared/common'
import {UploadDocuments} from '../../../../shared/common/upload-documents'
import {LOSS_SUBMIT_STATUS, PROVIDER_ROLE_PATH} from '../../../../utils/common/constants'
import {CLAIM_MODE, CLAIM_MODE_TYPE, EditWizardTabIDs} from '../../constants'
import {useContextRefStores} from '../../hooks'
import {DENTAL_CLAIM_INTAKE_WITHIN_FORM_ACTIONS, useIntakeRootStore, useWithinFormStore} from '../../store'
import {getIsNextButtonDisabled, getSteps} from './claimEditWizardUtils'
import {DENTAL_CLAIM_INTAKE_WIZARD_WRAPPER} from './classnames'

import EvaluationMode = kraken.form.EvaluationMode

export const ClaimEditWizard: React.FC = observer(() => {
    const {rulesEvaluationProps} = useKrakenEvaluation()
    const {mode} = useIntakeRootStore()
    const {uploadDocumentsStore} = useContextRefStores()
    const form = useForm()
    const formState = useFormState<{entity}>({
        subscription: {
            values: true
        }
    })
    const loss = formState.values.entity
    const {
        actionsStore,
        intakeWizardStore,
        intakeWizardRuleExecutionStore,
        updateDentalLoss,
        updateDentalLossDraft,
        initDentalLoss,
        submitDentalLoss,
        setLossSubmitStatus,
        updateLoss
    } = useWithinFormStore()
    const currentStep = intakeWizardStore.currentStep
    const currentStepKraken = intakeWizardRuleExecutionStore.evalutionInfo?.[currentStep]

    const isNextButtonDisabled = getIsNextButtonDisabled(loss, intakeWizardStore)

    const isNextButtonHasConfirmPopup =
        currentStep === EditWizardTabIDs.REVIEW_CLAIM_INFO_TAB && mode === CLAIM_MODE.ADJUST

    const steps = getSteps(mode, intakeWizardStore)
    const getStepsContent = (from: CLAIM_MODE_TYPE) => {
        const availableStepsContent = [
            <ClaimPolicyAndPatient key='claimPolicyAndPatient' />,
            <ClaimInfo key='claimInfo' />,
            <UploadDocuments key='uploadDocuments' />,
            <ReviewClaimInfo key='reviewClaimInfo' />
        ]
        if (from === CLAIM_MODE.ADJUST) {
            availableStepsContent.splice(2, 1)
        }
        return availableStepsContent
    }

    const stepsContent = getStepsContent(mode)

    const getNumberOfStepByName = (tabId: EditWizardTabIDs, currentSteps: Step[]) => {
        const index = currentSteps.findIndex(step => step.id === tabId)
        return index === -1 ? 0 : index
    }

    const activeStep = useMemo(
        () => ({
            index: getNumberOfStepByName(intakeWizardStore.currentStep, steps)
        }),
        [intakeWizardStore.currentStep, steps]
    )

    let nextButtonLabel = t('dental-product:next')
    if (!loss.lossNumber) {
        nextButtonLabel = t('dental-product:dental-claim-intake-create-claim-and-continue')
    }
    if (intakeWizardStore.currentStep === EditWizardTabIDs.REVIEW_CLAIM_INFO_TAB) {
        nextButtonLabel = t('dental-product:submit')
    }

    const stepValidation = useMemo<StepValidationConfig>(
        () => ({
            getEvaluationProps: () => [
                {
                    entryPointName: rulesEvaluationProps.payload.entryPointName,
                    krakenService: rulesEvaluationProps.krakenService,
                    evaluationMode: EvaluationMode.MODEL
                }
            ]
        }),
        [rulesEvaluationProps]
    )

    const goNextStepForAdjust = step => {
        const values = form.getState().values
        if (step.stepId === EditWizardTabIDs.CLAIM_INFO_TAB) {
            updateLoss(values.entity)
            intakeWizardStore.setStep(step.stepId)
            intakeWizardStore.setHasEnteredSteps(step.stepId)
        }
        if (step.stepId === EditWizardTabIDs.REVIEW_CLAIM_INFO_TAB) {
            updateLoss(values.entity)
            intakeWizardStore.setStep(step.stepId)
            intakeWizardStore.setHasEnteredSteps(step.stepId)
        }

        if (!step.stepId) {
            updateDentalLoss(values.entity).subscribe(either => {
                if (either.isRight) {
                    const updatedLoss = either.get()
                    updateLoss(updatedLoss)
                    RoutingUtils.goTo(`dental/${updatedLoss._key.rootId}/${updatedLoss._key.revisionNo}`)
                }
            })
        }
    }

    const goNextStep = step => {
        if (mode === CLAIM_MODE.ADJUST) {
            goNextStepForAdjust(step)
        } else {
            if (step.stepId === EditWizardTabIDs.CLAIM_INFO_TAB && currentStepKraken?.loading === false) {
                const values = form.getState().values

                if (values.entity.lossNumber) {
                    let updateDraft = values.entity
                    if (isEmpty(values.entity.lossDetail.claimData.providerRole?.providerLink)) {
                        updateDraft = omit(values.entity, PROVIDER_ROLE_PATH)
                    }
                    updateDentalLossDraft(updateDraft).subscribe(either => {
                        if (either.isRight) {
                            updateLoss(either.get())
                            intakeWizardStore.setStep(step.stepId)
                            intakeWizardStore.setHasEnteredSteps(step.stepId)
                        }
                    })
                } else {
                    const initData = {
                        ...values.entity,
                        policyId: values.entity.policyId
                    }
                    initDentalLoss(initData).subscribe(either => {
                        if (either.isRight) {
                            updateLoss(either.get())
                            intakeWizardStore.setStep(step.stepId)
                            intakeWizardStore.setHasEnteredSteps(step.stepId)
                        }
                    })
                }
            }
            if (step.stepId === EditWizardTabIDs.UPLOAD_DOCUMENTS_TAB && currentStepKraken?.loading === false) {
                const values = form.getState().values
                updateDentalLossDraft(values.entity).subscribe(either => {
                    if (either.isRight) {
                        updateLoss(either.get())
                        intakeWizardStore.setStep(step.stepId)
                        intakeWizardStore.setHasEnteredSteps(step.stepId)
                    }
                })
            }
            if (step.stepId === EditWizardTabIDs.REVIEW_CLAIM_INFO_TAB) {
                intakeWizardStore.setStep(step.stepId)
                intakeWizardStore.setHasEnteredSteps(step.stepId)
            }
            if (!step.stepId) {
                const values = form.getState().values
                submitDentalLoss(values.entity._key).subscribe(either => {
                    if (either.isRight) {
                        uploadDocumentsStore?.uploadClaimDocuments(form.getState().values.entity)
                        updateLoss(either.get())
                        setLossSubmitStatus(LOSS_SUBMIT_STATUS.SUCCESS)
                    } else {
                        setLossSubmitStatus(LOSS_SUBMIT_STATUS.FAILED)
                    }
                })
            }
        }
    }

    const goBackStepForAdjust = step => {
        const values = form.getState().values
        if (step.stepId === EditWizardTabIDs.POLICY_AND_PATIENT_TAB) {
            updateLoss(values.entity)
            intakeWizardStore.setStep(step.stepId)
        }
        if (step.stepId === EditWizardTabIDs.CLAIM_INFO_TAB) {
            intakeWizardStore.setStep(step.stepId)
        }
    }

    const goBackStep = step => {
        const values = form.getState().values
        if (mode === CLAIM_MODE.ADJUST) {
            goBackStepForAdjust(step)
        } else {
            let updateDraft = values.entity
            if (isEmpty(values.entity.lossDetail.claimData.providerRole.providerLink)) {
                updateDraft = omit(values.entity, PROVIDER_ROLE_PATH)
            }
            updateDentalLossDraft(updateDraft).subscribe(either => {
                if (either.isRight) {
                    updateLoss(either.get())
                    intakeWizardStore.setStep(step.stepId)
                }
            })
        }
    }

    const isStepLoading =
        actionsStore.isRunning(DENTAL_CLAIM_INTAKE_WITHIN_FORM_ACTIONS.INIT_DENTAL_LOSS_ENTITY) ||
        actionsStore.isRunning(DENTAL_CLAIM_INTAKE_WITHIN_FORM_ACTIONS.UPDATE_DENTAL_LOSS_DRAFT_ENTITY) ||
        actionsStore.isRunning(DENTAL_CLAIM_INTAKE_WITHIN_FORM_ACTIONS.UPDATE_DENTAL_LOSS_ENTITY) ||
        actionsStore.isRunning(DENTAL_CLAIM_INTAKE_WITHIN_FORM_ACTIONS.SUBMIT_DENTAL_LOSS_ENTITY)

    return (
        <div className={DENTAL_CLAIM_INTAKE_WIZARD_WRAPPER}>
            <MultiSteps
                activeStep={activeStep}
                type='navigation'
                steps={steps}
                backButtonTitle={t('dental-product:previous')}
                nextButtonTitle={nextButtonLabel}
                buttonsPosition='right'
                stepValidation={stepValidation}
                goNextStep={goNextStep}
                goBackStep={goBackStep}
                hasEnteredSteps={intakeWizardStore.hasEnteredSteps}
                validateScope={[VALIDATE_SCOPE.NEXT]}
                isNextButtonDisabled={isNextButtonDisabled}
                isStepLoading={isStepLoading}
                isNextButtonHasConfirmPopup={isNextButtonHasConfirmPopup}
                nextButtonConfirmInfo={t('dental-product:dental-claim-adjust-submit-msg')}
            >
                {stepsContent}
            </MultiSteps>
        </div>
    )
})
