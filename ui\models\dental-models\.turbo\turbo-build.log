$ tsc --project tsconfig.build.json
$ cross-env BABEL_ENV=build node ../../node_modules/@babel/cli/bin/babel.js target/dist/js --out-dir target/dist/js --source-maps --extensions .js,.jsx --no-comments --config-file @eisgroup/infra-scripts/config/babel.config
[BABEL] Note: The code generator has deoptimised the styling of D:\ProgramData\ms-claim-benefits-dental\ui\models\dental-models\target\dist\js\src\automatic\CapDentalSettlement\index.js as it exceeds the max of 500KB.
Successfully compiled 51 files with Babel (36488ms).
