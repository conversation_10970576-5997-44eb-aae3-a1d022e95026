import React from 'react'
import {observer} from 'mobx-react'

import {BalanceActivities} from './BalanceActivities'
import {BALANCE_HEADER, BALANCE_TAB} from './classnames'
import {OverpaymentAmount} from './OverpaymentAmount'
import {RecalculatedPayments} from './RecalculatedPayments'
import {SelectAction} from './SelectAction'
import {useBalanceLogService, useBalanceService} from './service'

export const BalanceTab: React.FC = observer(() => {
    const {balanceData, balanceLoading, settlements} = useBalanceService()
    const {balanceLogData, balanceLogLoading} = useBalanceLogService()

    return (
        <div className={BALANCE_TAB}>
            <div className={BALANCE_HEADER}>
                <SelectAction />
                <OverpaymentAmount totalBalanceAmount={balanceData?.totalBalanceAmount} />
            </div>
            <BalanceActivities activities={balanceLogData || []} loading={balanceLogLoading} />
            <RecalculatedPayments settlements={settlements} balance={balanceData} loading={balanceLoading} />
        </div>
    )
})
