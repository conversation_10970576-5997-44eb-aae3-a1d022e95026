/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {useState} from 'react'
import {observer} from 'mobx-react'

import {useForm, useFormState} from '@eisgroup/form'
import {useTranslate} from '@eisgroup/i18n'
import {AntDropdown, AntMenu, AntPopconfirm, Button, notification} from '@eisgroup/ui-kit'
import {ActionChevronDownMedium, ValidationMediumWarning} from '@eisgroup/ui-kit-icons'

import {hasAuthorities, Privileges} from '../../../../../utils'
import {DENTAL_FINANCIAL_DROPDOWN_ICON} from '../classnames'
import {PaymentScheduledState, PaymentSelectActions} from '../contants'
import {cancelPaymentSchedule, getPayments} from '../paymentServiceUtils'
import {formatLossSource, formatPaymentTableData} from '../utils'

export const PaymentActionsDropdown: React.FC = observer(() => {
    const {t} = useTranslate()
    const form = useForm()
    const {entity, paymentSchedules, paymentList} = useFormState().values
    const paymentSchedule = paymentSchedules?.length ? paymentSchedules?.[0] : null
    const paymentScheduleState = paymentSchedule?.state

    const [isPopconfirmVisible, setIsPopconfirmVisible] = useState<boolean>(false)
    const [actionMenuVisible, setActionMenuVisible] = useState<boolean>(false)

    const paymentsActions = {
        [PaymentSelectActions.CANCEL_PAYMENT_SCHEDULE]: {
            label: t('dental-product:dental-claim-financial-actions-cancel-payment-schedule-btn'),
            disabled: false,
            display:
                hasAuthorities([Privileges.FINANCIAL_CANCEL_PAYMENT_SCHEDULE]) &&
                [PaymentScheduledState.Open, PaymentScheduledState.Active, PaymentScheduledState.Suspended].includes(
                    paymentScheduleState
                ) &&
                paymentList.some(s =>
                    [
                        PaymentScheduledState.Open,
                        PaymentScheduledState.Active,
                        PaymentScheduledState.Suspended
                    ].includes(s.state)
                ),
            popConfirmHide: false
        }
    }

    const actionPaymentScheduleEither = async (result, actionType: string) => {
        const dentalLossSource = formatLossSource(entity)
        const payments = await getPayments(dentalLossSource)
        const filteredPaymentSchedules = result?.state !== PaymentScheduledState.Canceled ? [result] : []
        const paymentTableData = formatPaymentTableData(payments.result || [], filteredPaymentSchedules)
        form.change('paymentList', paymentTableData)
        form.change('paymentSchedules', filteredPaymentSchedules)
        notification.success({
            message: t(`dental-product:dental-claim-financial-payments-action-${actionType}-payments-successful-msg`)
        })
    }

    const onPopConfirmClick = async (actionType: string) => {
        switch (actionType) {
            case PaymentSelectActions.CANCEL_PAYMENT_SCHEDULE:
                setIsPopconfirmVisible(false)
                const cancelResult = await cancelPaymentSchedule(paymentSchedule?._key)
                actionPaymentScheduleEither(cancelResult, PaymentSelectActions.CANCEL_PAYMENT_SCHEDULE)
                break
            default:
                break
        }
    }

    const handlePopconfirmConfirm = (actionType: string) => {
        onPopConfirmClick(actionType)
        handleActionMenuVisibleChange(false)
    }

    const onMenuItemClick = async (key: string, e: React.MouseEvent) => {
        if (e) {
            e.stopPropagation()
        }
        switch (key) {
            case PaymentSelectActions.CANCEL_PAYMENT_SCHEDULE:
                setIsPopconfirmVisible(true)
                break
            default:
                break
        }
    }

    const handleActionMenuVisibleChange = (visible: boolean) => {
        setActionMenuVisible(visible)
    }

    const handlePopconfirmCancel = () => {
        handleActionMenuVisibleChange(false)
        setIsPopconfirmVisible(false)
    }

    const actionMenuItems = () => {
        return (
            <AntMenu onClick={e => e.domEvent.stopPropagation()}>
                {Object.keys(paymentsActions).map(v => (
                    <AntMenu.Item key={v} disabled={paymentsActions[v].disabled} hidden={!paymentsActions[v].display}>
                        <AntPopconfirm
                            trigger='click'
                            destroyTooltipOnHide
                            icon={<ValidationMediumWarning />}
                            visible={isPopconfirmVisible}
                            title={t(`dental-product:dental-claim-financial-actions-${v}-popconfirm-msg`)}
                            okText={t(`dental-product:dental-claim-financial-popconfirm-${v}`)}
                            cancelText={t('dental-product:cancel')}
                            disabled={paymentsActions[v].popConfirmHide}
                            onCancel={handlePopconfirmCancel}
                            onConfirm={() => handlePopconfirmConfirm(v)}
                        >
                            <div onClick={e => onMenuItemClick(v, e)}>{paymentsActions[v].label}</div>
                        </AntPopconfirm>
                    </AntMenu.Item>
                ))}
            </AntMenu>
        )
    }

    return (
        <AntDropdown
            trigger={['click']}
            overlay={actionMenuItems}
            placement='bottomLeft'
            onVisibleChange={visible => handleActionMenuVisibleChange(visible)}
            getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
            visible={actionMenuVisible}
        >
            <Button onClick={e => e.stopPropagation()}>
                {t('dental-product:dental-claim-financial-select-payments-action')}
                <ActionChevronDownMedium className={DENTAL_FINANCIAL_DROPDOWN_ICON} />
            </Button>
        </AntDropdown>
    )
})
