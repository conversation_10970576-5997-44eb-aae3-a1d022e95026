import {dentalFinancialService, EntityParams} from '@eisgroup/dental-product-services'

import {BaseRootStoreImpl} from '../../../../shared/stores'
import {getCurrentUsername} from '../../../../utils'

const baseStore = new BaseRootStoreImpl()

export const getPayments = async (dentalLossSource: string) => {
    const searchQuery = {
        originSource: {
            matches: [dentalLossSource]
        }
    }
    const result = await baseStore.callService(dentalFinancialService.loadPayments(searchQuery))
    return result
}
export const getPaymentSchedules = async (dentalLossSource: string) => {
    const searchQuery = {
        originSource: {
            matches: [dentalLossSource]
        }
    }
    const result = await baseStore.callService(dentalFinancialService.loadPaymentSechdule(searchQuery))
    return result
}

export const activatePaymentSchedule = async ({rootId, revisionNo}: EntityParams) => {
    const result = await baseStore.callService(dentalFinancialService.activatePaymentSchedule({rootId, revisionNo}))
    return result
}

export const checkPaymentSchedule = async paymentSchedule => {
    const userName = getCurrentUsername()
    const result = await baseStore.callService(
        dentalFinancialService.verifyPaymentScheduleActivation(paymentSchedule, userName)
    )
    return result
}

export const cancelPaymentSchedule = async ({rootId, revisionNo}: EntityParams) => {
    const result = await baseStore.callService(dentalFinancialService.cancelPaymentSchedule({rootId, revisionNo}))
    return result
}
