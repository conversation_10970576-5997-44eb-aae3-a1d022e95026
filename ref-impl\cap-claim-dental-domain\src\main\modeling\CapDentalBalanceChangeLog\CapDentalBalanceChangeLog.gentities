//GENESIS-354136
@Description("The Root Entity of Balance Change Log Domain.")
Entity CapDentalBalanceChangeLogEntity is CapBalanceChangeLog {

    @Description("Balance change log details.")
    @Embedded
    Ref balanceChangeLogDetails: CapDentalBalanceChangeLogDetailsEntity

    @Description("The date when the entity was created.")
    @Searchable
    Attr creationDate: Datetime

    @Description("Link to the related claim.")
    @Searchable
    ExtLink originSource: RootEntity

    @Description("Link to the CEM.")
    @Searchable
    ExtLink payee: RootEntity
}

@Description("Balance log details.")
Entity CapDentalBalanceChangeLogDetailsEntity is CapBalanceChangeLogDetails{

    @Lookup("CapDNBalanceLogTransactionType")
    @Description("Event that triggered balance recalculation.")
    Attr transactionTypeCd: String
}

Entity CapDentalBalanceChangeLogGenerationInput {

    @Description("Payment or payment schedule entity")
    Attr activatePayload: JsonType

    @Description("Payment or payment schedule entity")
    Attr balancePayload: JsonType

    @Description("Event that triggered balance recalculation.")
    Attr balanceTransactionTypeCd: String
}

Entity CapDentalBalanceChangeLogGenerationRulesOutput {

    @Description("List of balance change logs that must be saves in the database.")
    Attr balanceChangeLogs: *CapDentalBalanceChangeLogEntity
}