{"name": "@eisgroup/dental-models", "version": "25.7.0-SNAPSHOT.202508080205", "description": "Autogenerated Dental Models & Rules", "license": "UNLICENSED", "main": "target/dist/js/src/index.js", "typings": "target/dist/definitions/src/index.d.ts", "files": ["target/dist/"], "publishConfig": {"registry": "https://genesis-npm-release.exigengroup.com/repository/genesis-npm-release/"}, "dependencies": {"@eisgroup/kraken-integration-model": "~25.8.0-202506231223"}, "devDependencies": {"@eisgroup/skipper": "~25.8.0-202506231223"}, "peerDependencies": {"@eisgroup/i18n": "100.0.0", "@eisgroup/models-api": "100.0.0"}, "scripts": {"build": "tsc --project tsconfig.build.json", "postbuild": "cross-env BABEL_ENV=build node ../../node_modules/@babel/cli/bin/babel.js target/dist/js --out-dir target/dist/js --source-maps --extensions .js,.jsx --no-comments --config-file @eisgroup/infra-scripts/config/babel.config", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "generate-models": "skipper model"}}