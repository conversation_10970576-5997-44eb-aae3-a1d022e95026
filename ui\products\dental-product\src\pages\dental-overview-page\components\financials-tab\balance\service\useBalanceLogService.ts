import {useEffect, useState} from 'react'
import moment from 'moment'

import {createFormSelector} from '@eisgroup/dental-core'
import {CapDentalBalanceChangeLog, CapDentalLoss} from '@eisgroup/dental-models'
import {dentalBalanceService} from '@eisgroup/dental-product-services'

import {formatLossSource} from '../../utils'

import DentalLoss = CapDentalLoss.CapDentalLossEntity
import CapDentalBalanceChangeLogEntity = CapDentalBalanceChangeLog.CapDentalBalanceChangeLogEntity

const PAGE_SIZE = 100

export const loadBalanceLogFunction = (lossSource: string, offset: number) => {
    return dentalBalanceService
        .searchBalanceLog({
            limit: PAGE_SIZE,
            offset,
            query: {
                originSource: {
                    matches: [lossSource]
                }
            },
            sort: {creationDate: 'DESC'}
        })
        .toPromise()
}

export const loadAllBalanceLogs = async (lossSource: string) => {
    const firstPageBalanceLogs = (await loadBalanceLogFunction(lossSource, 0)).getOrElse({result: [], count: 0})
    const pages = Math.ceil((firstPageBalanceLogs?.count ?? 0) / PAGE_SIZE)
    if (pages <= 1) {
        return firstPageBalanceLogs.result
    }
    const pagesPromises = [...Array(pages).keys()]
        .filter(i => i !== 0)
        .map(i => {
            return loadBalanceLogFunction(lossSource, i * PAGE_SIZE)
        })
    const pagesResults = await Promise.all(pagesPromises)
    const results = pagesResults
        .map(result => result.getOrElse({result: [], count: 0}))
        .map(result => result.result)
        .filter(Boolean)
        .flat(2) as CapDentalBalanceChangeLogEntity[]
    const totalResults = [...(firstPageBalanceLogs?.result ?? []), ...results]
    totalResults.sort((a, b) => {
        return moment(a?.creationDate).isAfter(moment(b?.creationDate)) ? 1 : -1
    })
    return totalResults
}

export const useBalanceLogService = () => {
    const [balanceLogData, setBalanceLogData] = useState<CapDentalBalanceChangeLogEntity[]>()
    const [balanceLogLoading, setBalanceLogLoading] = useState<boolean>(false)

    const loss = createFormSelector<{entity: DentalLoss}, DentalLoss>(state => state.entity)()
    const lossSource = formatLossSource(loss)

    useEffect(() => {
        setBalanceLogLoading(true)
        loadAllBalanceLogs(lossSource).then(res => {
            setBalanceLogData(res)
            setBalanceLogLoading(false)
        })
    }, [lossSource])

    return {
        balanceLogData,
        balanceLogLoading
    }
}
