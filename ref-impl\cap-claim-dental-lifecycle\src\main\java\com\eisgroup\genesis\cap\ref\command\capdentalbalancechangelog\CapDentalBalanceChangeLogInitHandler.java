/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalancechangelog;

import com.eisgroup.genesis.cap.financial.command.balancechangelog.CapBalanceChangeLogInitHandler;
import com.eisgroup.genesis.cap.financial.command.balancechangelog.input.CapBalanceChangeLogInitInput;
import com.eisgroup.genesis.factory.model.capdentalbalancechangelog.CapDentalBalanceChangeLogEntity;

/**
 * Handler for dental balance change log initiation.
 *
 * <AUTHOR>
 * @since 25.11
 */
public class CapDentalBalanceChangeLogInitHandler extends CapBalanceChangeLogInitHandler<CapBalanceChangeLogInitInput, CapDentalBalanceChangeLogEntity> {
}
