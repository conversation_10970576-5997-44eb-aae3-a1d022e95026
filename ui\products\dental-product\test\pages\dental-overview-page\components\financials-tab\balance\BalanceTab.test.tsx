import React from 'react'
import {render, screen, waitFor} from '@testing-library/react'
import {Observable} from 'rxjs'
import {describe, expect, it, vi} from 'vitest'

import {Right} from '@eisgroup/data.either'

import {BalanceTab} from '../../../../../../src/pages/dental-overview-page/components/financials-tab/balance'
import {wrapInForm} from '../../../../../support/TestUtils'
import {mockDentalLoss} from '../../../store/MockDatas'

const {searchBalance, searchBalanceLog} = vi.hoisted(() => {
    return {
        searchBalance: vi.fn(),
        searchBalanceLog: vi.fn()
    }
})
vi.mock('@eisgroup/dental-product-services', () => ({
    dentalBalanceService: {
        searchBalance: searchBalance,
        searchBalanceLog: searchBalanceLog
    }
}))

vi.mock('../../../../../../src/pages/dental-overview-page/components/financials-tab/balance/OverpaymentAmount', () => {
    return {
        OverpaymentAmount: () => {
            return <div data-testid='mock-overpaymentamount'>0</div>
        }
    }
})

describe('BalanceTab', () => {
    it('should render components correctly', () => {
        searchBalance.mockImplementation(() => Observable.of(Right({result: [], count: 0})))
        searchBalanceLog.mockImplementation(() => Observable.of(Right({result: [], count: 0})))

        render(
            wrapInForm(<BalanceTab />, {
                initialValues: {
                    loss: mockDentalLoss,
                    settlementList: []
                }
            })
        )

        expect(screen.getByTestId('mock-overpaymentamount')).toBeInTheDocument()
    })
})
