{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Current Tests", // 新配置，用于调试文件
            "program": "${workspaceRoot}/node_modules/vitest/vitest.mjs", // 调用入口文件
            "args": ["${relativeFile}"], // 传递当前测试文件路径
            "console": "integratedTerminal",
            "autoAttachChildProcesses": true,
            "skipFiles": ["<node_internals>/**", "**/node_modules/**"],
            "smartStep": true
        }
    ]
}
