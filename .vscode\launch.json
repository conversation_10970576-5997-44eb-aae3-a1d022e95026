{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug Current Tests", "program": "${workspaceRoot}/ui/node_modules/vitest/vitest.mjs", "args": ["run", "${relativeFile}"], "cwd": "${workspaceRoot}", "console": "integratedTerminal", "autoAttachChildProcesses": true, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "smartStep": true, "env": {"NODE_ENV": "test", "TZ": "EET"}, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"type": "node", "request": "launch", "name": "Debug PaymentsTable Tests", "program": "${workspaceRoot}/ui/node_modules/vitest/vitest.mjs", "args": ["run", "ui/products/dental-product/test/pages/dental-overview-page/components/financials-tab/payments/PaymentsTable.test.tsx"], "cwd": "${workspaceRoot}", "console": "integratedTerminal", "autoAttachChildProcesses": true, "skipFiles": ["<node_internals>/**", "**/node_modules/**"], "smartStep": true, "env": {"NODE_ENV": "test", "TZ": "EET"}, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}]}