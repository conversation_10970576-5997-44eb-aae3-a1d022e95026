/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {Localization} from '@eisgroup/i18n'

export const enMT: Localization.ResourceBundle = {
    locale: {country: 'MT', language: 'en'},
    ns: 'dental-core',
    resources: {
        'date_format': 'DD--MM--YYYY',
        'create_new_dental-claim': 'MT+Dental',
        'not_available': 'MT+N/A'
    }
}
