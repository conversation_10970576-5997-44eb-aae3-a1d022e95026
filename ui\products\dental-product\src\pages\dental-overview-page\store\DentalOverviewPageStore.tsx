/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {action, computed, observable, runInAction} from 'mobx'
import moment from 'moment'

import {customerCommonService, providerService} from '@eisgroup/claim-dental-customer-service-api'
import {
    dentalClaimPolicyService,
    DentalIndividualPolicy,
    dentalPolicyService
} from '@eisgroup/claim-dental-policy-service-api'
import {CapDentalLoss, CapDentalSettlement} from '@eisgroup/dental-models'
import {
    dentalClaimService,
    SetLossSubStatusInputBody,
    unverifiedDentalPolicyService
} from '@eisgroup/dental-product-services'

import type {IndividualCustomer, ProviderWithCustomerPartyRole} from '@eisgroup/claim-dental-customer-service-api'
import type {EntityParams} from '@eisgroup/dental-product-services'

import {ActionsStoreImpl, BaseRootStoreImpl} from '../../../shared/stores'
import {POLICY_AND_PATIENT_STORE} from '../../../utils/common/constants'
import {storeBindingFactory} from '../../../utils/common/StoreHOCs'
import {FromQueryParams} from '../../../utils/common/urls'
import {EntityLink} from '../../../utils/helpers/entities'
import {AccumulatorTableData} from '../../../utils/types/types'
import {processAccumulatorData} from './accumulatorTypeUtils'
import {getPartiesFromLoss} from './utils'

import CapDentalLossEntity = CapDentalLoss.CapDentalLossEntity
import CapDentalSettlementEntity = CapDentalSettlement.CapDentalSettlementEntity

export enum DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS {
    INIT_STORE = 'INIT_STORE',
    LOAD_DENTAL_LOSS_ENTITY = 'LOAD_DENTAL_LOSS_ENTITY',
    LOAD_DENTAL_SETTLEMENT_ENTITY = 'LOAD_DENTAL_SETTLEMENT_ENTITY',
    LOAD_CUSTOMERS = 'LOAD_CUSTOMERS',
    LOAD_PROVIDER = 'LOAD_PROVIDER',
    CHANGE_CLAIM_SUBSTATUS = 'CHANGE_CLAIM_SUBSTATUS',
    RETRIEVE_POLICY_PRODUCT = 'RETRIEVE_POLICY_PRODUCT'
}

export interface DentalClaimParties {
    policyholder: IndividualCustomer | null
    patient: IndividualCustomer | null
    alternatePayee: IndividualCustomer | null
    provider: ProviderWithCustomerPartyRole | null
}

export interface StoreInitParams {
    dentalLossRootId: string | null
    dentalLossRevisionNo: number | null
    from: FromQueryParams | null
}

export interface DentalOverviewPageStore {
    loss: CapDentalLossEntity
    settlement: CapDentalSettlementEntity | null
    parties: DentalClaimParties
    dnIndividual: DentalIndividualPolicy
    productName: string
    isLoaded: boolean
    formatAccuData: AccumulatorTableData[]
    initStore: (storeParams: StoreInitParams) => Promise<void>
    changeClaimSubStatus: (body: SetLossSubStatusInputBody) => Promise<void>
    retrievePolicyProduct: (productCd: string) => Promise<void>
}

export class DentalOverviewPageStoreImpl extends BaseRootStoreImpl implements DentalOverviewPageStore {
    @observable loss: CapDentalLossEntity

    @observable formatAccuData: AccumulatorTableData[] = []

    @observable settlement: CapDentalSettlementEntity | null = null

    @observable dnIndividual: DentalIndividualPolicy

    @observable parties: DentalClaimParties = {
        policyholder: null,
        patient: null,
        alternatePayee: null,
        provider: null
    }

    @observable productName: string

    constructor() {
        super()
        this.actionsStore = new ActionsStoreImpl()
    }

    @computed
    get isLoaded(): boolean {
        return this.actionsStore.isCompleted(DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS.INIT_STORE)
    }

    @action
    initStore = async ({dentalLossRootId, dentalLossRevisionNo, from}: StoreInitParams): Promise<void> => {
        this.actionsStore.startAction(DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS.INIT_STORE)
        if (dentalLossRootId && dentalLossRevisionNo) {
            await this.loadDentalLoss({rootId: dentalLossRootId, revisionNo: dentalLossRevisionNo})

            let timestamp: number | undefined
            if (from === FromQueryParams.adjust && this.loss?.lossDetail?._timestamp) {
                timestamp = new Date(this.loss?.lossDetail?._timestamp).getTime()
            }

            this.loadDentalSettlement({rootId: dentalLossRootId, revisionNo: dentalLossRevisionNo}, timestamp).then(
                () => {
                    if (from) {
                        this.loadDentalLoss({rootId: dentalLossRootId, revisionNo: dentalLossRevisionNo})
                    }
                }
            )

            await this.loadParties()
            await this.loadPolicy()
        }

        this.actionsStore.completeAction(DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS.INIT_STORE)
    }

    @action
    changeClaimSubStatus = async (body: SetLossSubStatusInputBody) => {
        const result = await this.callService(
            dentalClaimService.setLossSubStatus(body),
            DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS.CHANGE_CLAIM_SUBSTATUS
        )

        runInAction(() => {
            this.loss = result
        })
    }

    @action
    async loadDentalLoss(lossParams: EntityParams): Promise<void> {
        const result = await this.callService(
            dentalClaimService.loadDentalLoss(lossParams),
            DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS.LOAD_DENTAL_LOSS_ENTITY
        )

        runInAction(() => {
            this.loss = result
        })
    }

    @action
    async loadDentalSettlement(lossParams: EntityParams, timestamp?: number): Promise<void> {
        const result = await this.callService(
            dentalClaimService.loadDentalSettlement(lossParams, timestamp),
            DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS.LOAD_DENTAL_SETTLEMENT_ENTITY
        )

        runInAction(() => {
            this.settlement = result
        })
    }

    @action
    async loadPolicy(): Promise<void> {
        if (!this.loss.policy?.capPolicyVersionId) {
            return
        }
        const urlParams = EntityLink.from(this.loss.policy?.capPolicyVersionId)

        const policyId = urlParams.rootId
        if (urlParams.baseType === 'UnverifiedPolicy') {
            const result = await unverifiedDentalPolicyService.loadUnverifiedDentalPolicy({
                rootId: policyId,
                revisionNo: Number(urlParams.revisionNo)
            })
            this.loadAccumulator()
            runInAction(() => {
                this.dnIndividual = result as any
            })
        } else {
            const result = await this.callService(
                dentalPolicyService().loadDentalPolicy({
                    rootId: policyId,
                    query: {onDate: moment(this.loss.policy?.txEffectiveDate).utc().toISOString() as unknown as Date}
                }),
                POLICY_AND_PATIENT_STORE.LOAD_POLICY
            )

            this.loadAccumulator()
            runInAction(() => {
                this.dnIndividual = result
            })
        }
    }

    @action
    async loadParties(): Promise<void> {
        const policyholderUri = this.loss.lossDetail?.claimData?.policyholderRole?.registryId
        const patientUri = this.loss.lossDetail?.claimData?.patientRole?.registryId
        const alternatePayeeUri = this.loss.lossDetail?.claimData?.alternatePayeeRole?.registryId
        const providerUri = this.loss.lossDetail?.claimData?.providerRole?.providerLink
        const providerRootId = providerUri
            ? EntityLink.from(this.loss.lossDetail?.claimData?.providerRole?.providerLink!).rootId
            : ''

        const partyUris = [policyholderUri, patientUri, alternatePayeeUri].filter(Boolean) as string[]

        const customers = partyUris.length ? await this.loadCustomers(partyUris) : []
        const provider = providerRootId ? await this.loadProvider(providerRootId) : null

        const {policyholder, patient, alternatePayee} = getPartiesFromLoss(customers, this.loss)

        runInAction(() => {
            this.parties = {
                ...this.parties,
                policyholder,
                patient,
                alternatePayee,
                provider
            }
        })
    }

    @action
    retrievePolicyProduct = async (productCd: string): Promise<void> => {
        const result = await this.callService(
            dentalClaimPolicyService().retrievePolicyProduct(productCd),
            DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS.RETRIEVE_POLICY_PRODUCT
        )

        runInAction(() => {
            this.productName = result?.policyProducts?.[0]?.productProperties.find(
                ({name}) => name === 'productName'
            ).values[0]
        })
    }

    async loadCustomers(customerUris: string[]): Promise<IndividualCustomer[]> {
        return this.callService(
            customerCommonService().searchCustomers(customerUris),
            DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS.LOAD_CUSTOMERS
        ) as any
    }

    async loadProvider(rootId: string): Promise<ProviderWithCustomerPartyRole> {
        return this.callService(
            providerService().loadProvider({rootId}),
            DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS.LOAD_PROVIDER
        ) as any
    }

    @action
    async loadAccumulator(): Promise<void> {
        const {policy, lossDetail} = this.loss ?? {}
        const policyholderUri = lossDetail?.claimData?.policyholderRole?.registryId ?? ''
        const patientUri = this.loss.lossDetail?.claimData?.patientRole?.registryId ?? ''
        if (!policy?.capPolicyId || !policyholderUri.length || !patientUri.length) {
            return
        }

        try {
            const result = await this.callService(
                dentalClaimService.loadAccumulator(policy.capPolicyId, policyholderUri),
                DENTAL_CLAIM_OVERVIEW_PAGE_ACTIONS.LOAD_DENTAL_LOSS_ENTITY
            )
            const processedData = processAccumulatorData(result, patientUri)
            runInAction(() => {
                this.formatAccuData = processedData
            })
        } catch (error) {
            console.error(error)
            runInAction(() => {
                this.formatAccuData = []
            })
        }
    }
}

export const {createViewLoader, useStore} = storeBindingFactory<DentalOverviewPageStore & BaseRootStoreImpl>(
    () => new DentalOverviewPageStoreImpl()
)
