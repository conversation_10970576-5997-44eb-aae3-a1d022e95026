/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {Localization} from '@eisgroup/i18n'

/* eslint-disable max-lines */
export const enUS: Localization.ResourceBundle = {
    locale: {country: 'US', language: 'en'},
    ns: 'dental-product',
    resources: {
        'error': 'Error',
        'na': 'n/a',
        'no-data': 'No Data',
        'no-data-msg': 'No data is found',
        'view-all': 'View all',
        'default_date_format': 'MM/DD/YYYY',

        // bam
        'bam-bench-drawer-title': 'Timeline',
        'bam-bench-activity-title': '{{type}} {{key}} by {{displayCreatedBy}}',
        'bam-bench-activity-displayCreatedBy': 'System',
        // header
        'dental-header-claim-home': 'Home',
        'dental-header-claim-number': 'Dental Claim {{lossNumber}}',
        'dental-header-claim-claim-actions': 'I want to',
        'dental-header-claim-action-adjust-claim': 'Adjust Claim',
        'dental-header-claim-action-change-claim-substatus': 'Change Claim Substatus',

        // Change claim substatus drawer
        'dental-header-claim-substatus-label': 'Substatus',
        'dental-header-claim-substatus-placeholder': 'Selected Substatus Name',

        // claim info card
        'dental-claim-info-card-status': 'Status',
        'dental-claim-info-card-policyholder': 'Policyholder',
        'dental-claim-info-card-policy': 'Policy',
        'dental-claim-info-card-patient': 'Patient',
        'dental-claim-info-card-product-plan': 'Policy Product/Plan',
        'dental-claim-info-card-received-date': 'Received Date',
        'dental-claim-info-card-date-of-service': 'Date of Service',
        'dental-claim-info-card-source': 'Source',
        'dental-claim-info-card-claim-type': 'Claim Type',
        'dental-claim-info-card-payee-type': 'Payee Type',
        'dental-claim-info-card-total-charges': 'Total Charges',
        'dental-claim-info-card-total-paid': 'Total Paid',
        'dental-claim-info-contact-information': 'CONTACT INFORMATION',
        'dental-claim-info-view-detail': 'View Detail',

        // upload documents tab
        'upload_documents_title': 'Upload Documents',
        'upload_documents_drop_model_hint': '',
        'upload_documents_drop_model_text': 'Drag your files here to upload',
        'upload_documents_drop_model_button': 'Select Files',
        'upload_documents_file_type_alert':
            'File extension is not supported. Please choose a file with the supported file extension.',
        'upload_documents_file_max_size_alert': 'File size is greater than allowable maximum of 20MB.',

        // adjudication tab
        'dental-adjudication-results-provider-details': 'Provider Details',
        'dental-adjudication-results-result-of-adjudication': 'Results of Adjudication',
        'dental-adjudication-results-coordination-of-benefits': 'Coordination of Benefits',
        'dental-adjudication-results-additional-claim-information': 'Additional Claim Information',
        'dental-adjudication-results-missing-teeth': 'Missing Teeth',
        'dental-adjudication-results-comments-and-remarks': 'Comments and Remarks',
        'dental-adjudication-results-place-of-treatment': 'Place of Treatment',
        'dental-adjudication-results-digital-image-no': 'Digital Image #',
        'dental-adjudication-results-secondary-missing-teeth': 'Secondary Missing Teeth',

        // adjudication results table
        'dental-adjudication-results-line': 'Line',
        'dental-adjudication-results-date-of-service': 'Date of Service',
        'dental-adjudication-results-pre-authorization': 'Preauthorization',
        'dental-adjudication-results-procedure-code': 'Procedure Code',
        'dental-adjudication-results-quantity': 'Quantity',
        'dental-adjudication-results-charges': 'Charges',
        'dental-adjudication-results-decision': 'Decision',
        'dental-adjudication-results-benefit-amount': 'Benefit Amount',
        'dental-adjudication-results-remark-codes': 'Remark Codes',
        'dental-adjudication-results-covered-cdt-code': 'Covered CDT Code',
        'dental-adjudication-results-category': 'Category',
        'dental-adjudication-results-deductible': 'Deductible',
        'dental-adjudication-results-EHB': 'EHB',
        'dental-adjudication-results-coinsurance-amount': 'Coinsurance Amount',
        'dental-adjudication-results-cob-applied': 'COB Applied',
        'dental-adjudication-results-patient-responsibility': 'Patient Responsibility',
        'dental-adjudication-results-patient-network': 'Network',
        'dental-adjudication-results-months-of-treatment': 'Months of Treatment',
        'dental-adjudication-results-payment-frequency': 'Payment Frequency',
        'dental-adjudication-results-allowed-amount': 'Allowed Amount',

        // intake claim wizard
        'dental-claim-intake-wizard-policy-and-patient': 'Policy and Patient',
        'dental-claim-intake-wizard-claim-info': 'Claim Info',
        'dental-claim-intake-wizard-upload-documents': 'Upload Documents',
        'dental-claim-intake-wizard-review-claim-info': 'Review Claim Info',
        'dental-claim-intake-withdraw-loss-msg': 'Are you sure you want to withdraw the claim and exit the form?',
        'dental-claim-intake-cancel-loss-msg': 'Are you sure you want to cancel and exit the form?',
        'dental-claim-intake-save-and-exit-loss-msg': 'Are you sure you want to save and exit this form?',

        // intake claim page
        'dental-claim-intake-header-title-create-new-claim': 'Create New Claim',
        'dental-claim-intake-header-title-create-claim': 'Create Claim',
        'dental-claim-intake-header-title-claim': 'Claim',
        'dental-claim-intake-create-claim-and-continue': 'Create Claim and Continue',
        'dental-claim-intake-wizard-select-policy': 'Select Policy and Patient',
        'dental-claim-intake-wizard-search-for-policy': 'Search for a Policy',
        'dental-claim-intake-wizard-claim-intake': 'Claim Intake',
        'dental-claim-intake-wizard-claim-details': 'Claim Details',
        'dental-claim-intake-wizard-claim-fees': 'Claim Fees and Concessions',
        'dental-claim-intake-wizard-enter-name-or-policy': 'Enter the policyholder name or policy#',
        'dental-claim-intake-wizard-select-policy-and-patient': 'Select Policy and Patient',
        'view-claim': 'View Claim',
        'claim-intake-success-label': 'You have successfully submitted the claim.',
        'claim-intake-view-claim-label': 'Click below button to view the claim.',
        'back-to-claim-intake': 'Back to Claim Intake',
        'claim-intake-fail-label': 'Claim submission has failed.',
        'claim-intake-back-to-intake-label': 'Click below button to go back to the claim intake to review.',

        // adjust claim page
        'dental-claim-adjust-cancel-loss-msg': 'Are you sure you want to cancel adjust claim process?',
        'dental-claim-adjust-submit-msg': 'Are you sure you want to submit the adjust claim?',
        'dental-claim-adjust-header-title': 'Adjust Claim {{lossNumber}}',
        'dental-claim-adjust-collapse-policy-and-patient': 'Policy and Patient',
        'dental-claim-adjust-collapse-provider': 'Provider',
        'dental-claim-adjust-collapse-claim-intake': 'Claim Intake',
        'dental-claim-adjust-collapse-claim-details': 'Claim Details',
        'dental-claim-adjust-collapse-claim-provider-discounts-and-fees': 'Provider Discounts and Fees',

        // shared components - claim action header
        'dental-claim-action-header-cancel': 'Cancel',
        'dental-claim-action-header-submit': 'Submit',

        // shared components - policy and patient
        'dental-claim-policy-policy-number': 'Policy #',
        'dental-claim-policy-policy-policyholder': 'Policyholder',
        'dental-claim-policy-plan-name': 'Plan Name',
        'dental-claim-policy-policy-group-name': 'Group Name',
        'dental-claim-policy-policy-group-id': 'Customer ID',

        // shared components - provider search
        'dental-claim-provider-select-a-provider': 'Select a Provider',
        'dental-claim-provider-search-for-a-provider': 'Search for a Provider',
        'dental-claim-provider-search-place-holder': 'Enter the provider name or NPI',
        'dental-claim-provider-search-option-npi': 'NPI: {{npi}}',
        'dental-claim-provider-card-title': 'Provider',
        'dental-claim-provider-search-provider': 'Search provider',
        'dental-claim-provider-search-cancel': 'Cancel',

        // shared components - claim intake
        'dental-claim-claim-intake-and-services': 'Claim Intake and Services',
        'dental-claim-claim-intake': 'CLAIM INTAKE',
        'dental-claim-claim-services': 'SERVICES',
        'dental-claim-claim-intake-received-date': 'Received Date',
        'dental-claim-claim-intake-source': 'Source',
        'dental-claim-claim-intake-claim-type': 'Claim Type',
        'dental-claim-claim-intake-payee-type': 'Payee Type',
        'dental-claim-claim-intake-alternate-payee': 'Alternate Payee',
        'dental-claim-claim-intake-alternate-payee-name': 'Alternate Payee Name',
        'dental-claim-claim-intake-alternate-search-customer': 'Search Customer',
        'select_placeholder': 'Select',

        // shared components - claim details
        'dental-claim-additional-information': 'Additional Information',
        'dental-claim-fee-and-concessions': 'Fees and Concessions',
        'dental-claim-claim-details-services': 'Services',
        'dental-claim-claim-details-services-add-new-service': 'Add New Service',
        'dental-claim-claim-details-services-line': 'Line',
        'dental-claim-claim-details-services-date-of-service': 'Date of Service',
        'dental-claim-claim-details-services-procedure-code': 'Procedure Code',
        'dental-claim-claim-details-services-quantity': 'Quantity',
        'dental-claim-claim-details-services-charges': 'Charges',
        'dental-claim-claim-details-services-months-of-treatment': 'Months of Treatment',
        'dental-claim-claim-details-services-payment-frequency': 'Payment Frequency',
        'dental-claim-claim-details-services-tooth-codes': 'Tooth Codes',
        'dental-claim-claim-details-services-tooth-area': 'Area of Oral Cavity',
        'dental-claim-claim-details-additional-claim-info-title': 'ADDITIONAL CLAIM INFORMATION',
        'dental-claim-claim-details-additional-claim-info-place-of-treatment': 'Place of Treatment',
        'dental-claim-claim-details-additional-claim-info-treatment-result-from': 'Treatment Resulting from',
        'dental-claim-claim-details-additional-claim-info-date-of-accident': 'Date of Accident',
        'dental-claim-claim-details-additional-claim-info-accident-state': 'Accident State',
        'dental-claim-claim-details-comments-and-remarks-comments-and-remarks': 'Comments/Remarks',
        'dental-claim-claim-details-comments-and-remarks': 'Comments and Remarks',
        'dental-claim-claim-details-comments-and-remarks-title': 'COMMENTS/REMARKS',
        'dental-claim-claim-details-select-missing-teeth-title': 'SELECT MISSING TEETH',
        'dental-claim-claim-details-add-missing-teeth-title': 'Add New Missing Teeth',
        'dental-claim-claim-details-edit-missing-teeth-title': 'Edit Missing Teeth',
        'dental-claim-claim-details-missing-teeth-lookup-label': 'Missing Teeth',
        'dental-claim-claim-details-missing-teeth-pic-label-primary': 'Primary',
        'dental-claim-claim-details-missing-teeth-pic-label-secondary': 'Secondary',
        'dental-claim-claim-details-services-add-button': 'Add New Service',
        'dental-claim-claim-details-services-edit-service-information': 'Edit Service Information',
        'dental-claim-claim-details-services-view-service-details': 'View Service Details',
        'procedure_remove_pop_message': 'Are you sure you want to delete this {{code}}?',
        'procedure-code-search-placeholder': 'Enter Procedure Code',
        'procedure-code-mandatory': 'Procedure Code is required',
        'months-of-treatment-validate':
            'Month of Treatments cannot be less than the number of payment(s) already issued.',

        // shared components - edit procedure details
        'dental-claim-edit-procedure-details-title': 'Service {{drawerIndex}}',
        'dental-claim-edit-procedure-details-preauthorization-number': 'Preauthorization number',
        'dental-claim-edit-procedure-details-tooth-surface': 'Tooth Surface',
        'dental-claim-edit-procedure-details-tooth-system': 'Tooth System',
        'dental-claim-edit-procedure-details-tooth-letters': 'Tooth Numbers/Letters',
        'dental-claim-edit-procedure-details-date-appliance-placed': 'Date Appliance Placed',
        'dental-claim-edit-procedure-details-down-payment-amount': 'Down Payment Amount',
        'dental-claim-edit-procedure-details-date-of-prosthesis-placement': 'Date of Prior Prosthesis Placement',
        'dental-claim-edit-procedure-details-diagnosis-qualifier': 'Diagnosis List Qualifier',
        'dental-claim-edit-procedure-details-diagnosis-code': 'Diagnosis Code',
        'dental-claim-edit-procedure-details-orthodontics-details-title': 'ORTHODONTICS DETAILS',
        'dental-claim-edit-procedure-details-prosthesis-details-title': 'PROSTHESIS DETAILS',
        'form_drawer_default_close_drawer_popconfirm_msg': 'Are you sure you want to close this form?',
        'diagnosis_remove_pop_message': 'Are you sure you want to delete this diagnosis code?',
        'dental-claim-edit-procedure-details-diagnosis-add-button': 'Add New Diagnosis Code',
        'dental-claim-edit-row-is-duplicate': 'Attempted to insert duplicate data',

        // shared components - provider discounts and fees
        'dental-claim-discounts-fees-table-fee-type': 'Fee Type',
        'dental-claim-discounts-fees-table-fee': 'Fee',
        'dental-claim-discounts-fees-table-add-new': 'Add New Fee',
        'dental-claim-discounts-concession-type': 'Concession Type',
        'dental-claim-discounts-concession-amount': 'Concession Amount',
        'dental-claim-discounts-amount': 'Amount',

        // shared components - total charges
        'dental-claim-total-charges': 'Total Charges',

        // shared components - cards
        'dental-claim-policy-card-policy': 'Policy',
        'dental-claim-policy-card-patient': 'Patient',
        'dental-claim-policy-card-select-patient': 'Select a Patient',
        'dental-claim-patient-contact-card-dob': 'DOB',
        'dental-claim-patient-contact-card-customer-id': 'Customer ID',
        'dental-claim-patient-contact-card-phone': 'Phone',
        'dental-claim-patient-contact-card-address': 'Address',

        // shared components - provider details
        'dental-claim-provider-card-provider-title': 'Provider',
        'dental-claim-provider-card-tin': 'TIN',
        'dental-claim-provider-card-npi': 'NPI',
        'dental-claim-provider-card-inout-network': 'In/Out Network',
        'dental-claim-provider-card-in-network': 'In network',
        'dental-claim-provider-card-out-network': 'Out of network',
        'dental-claim-provider-card-specialty': 'Specialty',
        'dental-claim-provider-card-alternative-payee': 'Alternative Payee',
        'dental-claim-provider-card-address': 'Address',
        'dental-claim-provider-card-phone': 'Phone',

        // shared components - payments
        'dental-claim-financial-actions-activate-payment-btn': 'Authority Approval',
        'dental-claim-financial-actions-do-not-have-authorize-activate-payment-popconfirm-msg':
            'You do not have the authority to approve the payments',
        'dental-claim-financial-actions-authorityApproval-popconfirm-msg':
            'Are you sure you want to authorize the payments?',
        'dental-claim-financial-payments-action-authorityApproval-payments-successful-msg':
            'You have successfully authorized the payments',
        'dental-claim-financial-popconfirm-authorityApproval': 'Authorize',
        'dental-claim-financial-select-payments-action': 'Select Payments Action',
        'dental-claim-financial-actions-cancel-payment-schedule-btn': 'Cancel Payment(s)',
        'dental-claim-financial-actions-cancelPaymentSchedule-popconfirm-msg':
            'Are you sure you want to cancel all pending payment(s)?',
        'dental-claim-financial-payments-action-cancelPaymentSchedule-payments-successful-msg':
            'You have successfully canceled the pending payment(s).',
        'dental-claim-financial-popconfirm-cancelPaymentSchedule': 'Cancel Payment(s)',
        'dental-claim-financial-toggle-payments': 'Payments',
        'dental-claim-financial-toggle-recoveries': 'Recoveries',
        'dental-claim-financial-toggle-balance': 'Balance',
        'dental-claim-payment-table-payment-number': 'Payment #',
        'dental-claim-payment-table-payment-date': 'Payment Date',
        'dental-claim-payment-table-issued-date': 'Issued Date',
        'dental-claim-payment-table-payee': 'Payee',
        'dental-claim-payment-table-payment-net-amount': 'Payment Amount',
        'dental-claim-payment-table-transaction-type': 'Transaction Type',
        'dental-claim-payment-table-status': 'Status',
        'dental_claim_payment_table_expand_total_allocation_label': 'Total Allocation',
        'dental_claim_payment_table_expand_interest': 'Interest',
        'dental_claim_payment_table_expand_total_payment_amount_label': 'Total Payment Amount',
        'dental-claim-payment-table-peyment-schedule-state-Open': 'Pending Approval',
        'dental-claim-payment-table-peyment-schedule-state-Active': 'Pending Post',
        'dental-claim-payment-table-peyment-schedule-state-Suspended': 'Suspended',
        'dental-claim-payment-table-peyment-schedule-state-Canceled': 'Canceled',
        'dental-claim-payment-table-peyment-schedule-state-Completed': 'Approved/Issued',

        'dental-claim-balance-select-action': 'Select Action',
        'dental-claim-balance-overpayment': 'Overpayment',
        'balance_table_amount_box_overpayment': 'Overpayment',
        'balance_table_amount_box_underpayment': 'Underpayment',
        'balance_table_amount_box_0': 'Total Balance per Claim',
        'dental-claim-balance-balance-activities-title': 'Balance Activities',
        'dental-claim-balance-balance-transaction-date': 'Transaction Date',
        'dental-claim-balance-balance-transaction-id': 'Transaction ID',
        'dental-claim-balance-balance-transaction-type': 'Transaction Type',
        'dental-claim-balance-balance-transaction-amount': 'Transaction Amount',
        'dental-claim-balance-balance-transaction-description': 'Description',
        'dental-claim-balance-balance-transaction-total-balance': 'Total Balance',
        'dental-claim-balance-balance-recalculated-payments-title': 'Recalculated Payments',
        'dental-claim-balance-balance-recalculated-payments-amount': 'Payment Amount',
        'dental-claim-balance-balance-recalculated-payments-should-have-paid': 'Should Have Paid',
        'dental-claim-balance-balance-recalculated-payments-payment-date': 'Payment Date',
        'dental-claim-balance-balance-recalculated-payments-id': 'ID',
        'dental-claim-balance-balance-recalculated-payments-entities': 'Entities',
        'dental-claim-balance-balance-recalculated-payments-balance-adjustment': 'Balance Adjustment',
        'dental-claim-balance-balance-recalculated-payments-balance': 'Balance',
        'dental-claim-balance-balance-recalculated-payments-total-allocation': 'Total Allocation',
        'dental-claim-balance-balance-recalculated-payments-total-interests': 'Interest',
        'dental-claim-balance-balance-recalculated-payments-additions': 'Additions',

        // button label
        'add': 'Add',
        'create': 'Create',
        'save': 'Save',
        'edit': 'Edit',
        'update': 'Update',
        'delete': 'Delete',
        'submit': 'Submit',
        'cancel': 'Cancel',
        'yes': 'Yes',
        'no': 'No',
        'next': 'Next',
        'previous': 'Previous',
        'close': 'Close',
        'withdraw': 'Withdraw',
        'save-and-exit': 'Save and Exit',

        // pop alert
        'editable_table_remove_popup_message': 'Are you sure you want to remove the row?',
        'list_action_remove_popconfirm_msg': 'Are you sure you want to remove the row?',

        // dental intake - validation message
        'field_mandatory_message': '{{label}} is required.',
        'date_of_service_cannot_be_future_date': 'Date of Service cannot be future date.',
        'date_of_service_cannot_be_after_received_date': 'Date of Service cannot be after the Received Date',
        'min_max_validation': 'Entered value must be between {{min}} and {{max}}.',
        'months_treatment_min': '{{label}} has to be equal or more than {{min}}.',
        'months_treatment_max': '{{label}} must not be greater than {{max}}.',
        'value_cannot_be_negative': '{{value}} cannot be a negative value.',

        // auto complete search
        'dental-claim-auto-complete-search-optgroup-label': 'Policyholders',

        // other
        'empty': '-',
        'not_available': 'N/A',

        // Adjudication
        'claim_info_tab_adjudication': 'Adjudication',

        // Financials
        'claim_info_tab_financials': 'Financials',

        // Accumulators Tab
        'claim_info_tab_accumulators': 'Accumulators',
        'accumulators_tab_title': 'View Accumulators',
        'accumulators_tab_policy_eff_date': 'Coverage Start Date',
        'accumulators_tab_table_col_value_family_deductible': 'Family Deductible',
        'accumulators_tab_table_col_value_individual_cosmetic_annual_deductible':
            'Individual Cosmetic Annual Deductible',
        'accumulators_tab_table_col_value_individual_cosmetic_annual_maximum': 'Individual Cosmetic Annual Maximum',
        'accumulators_tab_table_col_value_individual_cosmetic_lifetime_deductible':
            'Individual Cosmetic Lifetime Deductible',
        'accumulators_tab_table_col_value_individual_cosmetic_lifetime_maximum': 'Individual Cosmetic Lifetime Maximum',
        'accumulators_tab_table_col_value_individual_deductible': 'Individual Deductible',
        'accumulators_tab_table_col_value_individual_implant_annual_maximum': 'Individual Implant Annual Maximum',
        'accumulators_tab_table_col_value_individual_implant_lifetime_maximum': 'Individual Implant Lifetime Maximum',
        'accumulators_tab_table_col_value_individual_ortho_annual_deductible': 'Individual Ortho Annual Deductible',
        'accumulators_tab_table_col_value_individual_ortho_annual_maximum': 'Individual Ortho Annual Maximum',
        'accumulators_tab_table_col_value_individual_ortho_lifetime_deductible': 'Individual Ortho Lifetime Deductible',
        'accumulators_tab_table_col_value_individual_ortho_lifetime_maximum': 'Individual Ortho Lifetime Maximum',
        'accumulators_tab_table_col_value_individual_tmj_annual_maximum': 'Individual TMJ Annual Maximum',
        'accumulators_tab_table_col_value_individual_tmj_lifetime_maximum': 'Individual TMJ Lifetime Maximum',
        'accumulators_tab_table_col_value_individual_tmj_annual_deductible': 'Individual TMJ Annual Deductible',
        'accumulators_tab_table_col_value_individual_tmj_lifetime_deductible': 'Individual TMJ Lifetime Deductible',
        'accumulators_tab_table_col_value_individual_maximum': 'Individual Maximum',
        'accumulators_tab_table_col_name_accumulators': 'Accumulator',
        'accumulators_tab_table_col_name_inn_limit': 'INN Limit',
        'accumulators_tab_table_col_name_inn_satisfied': 'INN Satisfied',
        'accumulators_tab_table_col_name_inn_remaining': 'INN Remaining',
        'accumulators_tab_table_col_name_onn_limit': 'OON Limit',
        'accumulators_tab_table_col_name_onn_satisfied': 'OON Satisfied',
        'accumulators_tab_table_col_name_onn_remaining': 'OON Remaining'
    }
}
