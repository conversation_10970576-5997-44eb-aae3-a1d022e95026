{"name": "@eisgroup/dental-ui-app", "version": "25.7.0-SNAPSHOT.************", "private": true, "description": "Genesis Dental UI Application", "license": "UNLICENSED", "files": ["target/webroot/"], "dependencies": {"@eisgroup/dental-backoffice-services-impl": "25.7.0-SNAPSHOT.************", "@eisgroup/builder-components": "25.8.0-SNAPSHOT.202507152214", "@eisgroup/dental-product": "25.7.0-SNAPSHOT.************", "@eisgroup/lookups-gateway-client": "25.10.0-SNAPSHOT.************", "@eisgroup/sidebar-service-api": "25.7.0-SNAPSHOT.************", "@eisgroup/efolder-services": "25.10.0-SNAPSHOT.************", "@eisgroup/efolder-ui": "25.10.0-SNAPSHOT.************", "@eisgroup/work-views": "25.10.0-SNAPSHOT.************", "@eisgroup/common-shell": "100.0.0", "@eisgroup/common": "100.0.0", "@eisgroup/form": "100.0.0", "@eisgroup/lookups": "100.0.0", "@eisgroup/react-application": "100.0.0", "@eisgroup/react-components": "100.0.0", "@eisgroup/react-routing": "100.0.0", "@eisgroup/mfe-security": "100.0.0", "@eisgroup/ui-kit": "100.0.0", "@eisgroup/ui-kit-icons": "^100.0.0", "@eisgroup/ui-temporals": "100.0.0", "@eisgroup/cache": "100.0.0", "@eisgroup/common-security-provider": "100.0.0", "@eisgroup/common-types": "100.0.0", "@eisgroup/environment": "100.0.0", "@eisgroup/i18n": "100.0.0", "@eisgroup/ioc": "100.0.0", "@eisgroup/data.either": "100.0.0", "@eisgroup/oauth": "100.0.0", "data.either": "~1.3.0", "i18next": "~9.0.0", "inversify": "^3.3.0", "jquery": "~3.5.1", "nodelist-foreach-polyfill": "~1.2.0", "reflect-metadata": "~0.1.10", "rxjs": "5.5.7"}, "peerDependencies": {"@eisgroup/common-types": "100.0.0", "@eisgroup/environment": "100.0.0", "@eisgroup/i18n": "100.0.0", "@eisgroup/ioc": "100.0.0", "dotenv": "16.4.5", "mobx": "~4.15.7", "react": "~16.8.6", "react-dom": "~16.8.6"}, "scripts": {"start": "infra-scripts start", "build": "cross-env NODE_OPTIONS=--max-old-space-size=10240 APP_BASE_URL=/dental infra-scripts build-fast", "install-server": "node install-server.js", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "lint": "infra-scripts lint --eslint", "test": "vitest run --passWithNoTests"}}