/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.executor;

import static com.eisgroup.genesis.json.key.BaseKey.ATTRIBUTE_NAME;
import static com.eisgroup.genesis.json.key.BaseKey.ROOT_ID;

import java.io.IOException;
import java.util.Optional;

import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.http.entity.JsonRequestEntity;
import com.eisgroup.genesis.http.errors.HttpRequestFailedException;
import com.eisgroup.genesis.http.response.JsonResponseHandler;
import com.eisgroup.genesis.security.service2service.ServiceAccessRunner;
import com.eisgroup.genesis.transformation.context.TransformationContext;
import com.eisgroup.genesis.transformation.executors.OperationExecutor;
import com.eisgroup.genesis.transformation.model.operations.GenericOperation;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;

/**
 * Returns user root UUID by provided username
 *
 * <AUTHOR>
 * @since 25.100
 */
public class CapDentalLoadUserIdByUUIDOperatorExecutor implements OperationExecutor<GenericOperation, JsonElement> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CapDentalLoadUserIdByUUIDOperatorExecutor.class);
    private static final String USER_UUID_LINK = "api/userdomain/User/v1/load/%s";

    private final HttpClient httpClient;
    private final String securityUrl;
    private final ServiceAccessRunner serviceAccessRunner;

    public CapDentalLoadUserIdByUUIDOperatorExecutor(HttpClient httpClient, String url, ServiceAccessRunner serviceAccessRunner) {
        this.httpClient = httpClient;
        this.securityUrl = url;
        this.serviceAccessRunner = serviceAccessRunner;
    }

    @Override
    public JsonElement execute(GenericOperation operation, TransformationContext<JsonElement> context) {
        var userUUID = context.execute(operation.getInput(0));
        if (userUUID.isJsonNull()) {
            return JsonNull.INSTANCE;
        }
        if (!userUUID.isJsonPrimitive() || !userUUID.getAsJsonPrimitive().isString()) {
            new IllegalArgumentException("The LoadUserIdByUUID Operator parameter must be String");
        }
        return resolveUserId(userUUID.getAsString()).get();
    }

    private Lazy<JsonElement> resolveUserId(String userUUID) {
        HttpPost httpRequest = new HttpPost(createFullUrl(userUUID));
        httpRequest.setEntity(new JsonRequestEntity(new JsonObject()));
        return serviceAccessRunner.runLazy(() -> {
            try {
                JsonElement rootId = httpClient.execute(httpRequest, new JsonResponseHandler())
                        .getAsJsonObject()
                        .getAsJsonObject("body")
                        .getAsJsonObject("success")
                        .getAsJsonObject(ATTRIBUTE_NAME)
                        .get(ROOT_ID);
                return Lazy.of(rootId);
            } catch (IOException | HttpRequestFailedException e) {
                LOGGER.info("Exception caught:", e);
                return Lazy.of(JsonNull.INSTANCE);
            }
        });
    }

    private String createFullUrl(String userUUID) {
        return Optional.ofNullable(securityUrl)
                .map(url -> url.endsWith("/") ? url : url + "/")
                .map(url -> url + USER_UUID_LINK.formatted(userUUID))
                .get();
    }

    @Override
    public String getName() {
        return "LoadUserIdByUUID";
    }
}
