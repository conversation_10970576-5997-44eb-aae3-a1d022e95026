/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {observer} from 'mobx-react'

import {moneyByLocale} from '../../../../../utils/helpers'
import {
    DENTAL_PAYMENT_TABLE_EXPAND_AMOUNT,
    DENTAL_PAYMENT_TABLE_EXPAND_COL,
    DENTAL_PAYMENT_TABLE_EXPAND_ROW,
    DENTAL_PAYMENT_TABLE_EXPAND_TITLE
} from '../classnames'

export type PaymentAllocation = {
    allocationGrossAmount?: {amount?: number}
    allocationNetAmount?: {amount?: number}
    additionSplitResults?: Array<{additionNumber: number; appliedAmount?: {amount?: number}}>
}

export type PaymentDetails = {
    paymentAllocations?: Array<PaymentAllocation>
    paymentAdditions?: Array<{additionType: string; additionNumber: number}>
}

export interface PaymentsTableExpandRowProps {
    expandName: string
    amount: number
}

export const PaymentTableExpandRow: React.FC<PaymentsTableExpandRowProps> = observer((expandName, amount) => {
    return (
        <div className={DENTAL_PAYMENT_TABLE_EXPAND_ROW}>
            <div className={DENTAL_PAYMENT_TABLE_EXPAND_COL}>
                <label className={DENTAL_PAYMENT_TABLE_EXPAND_TITLE}>{expandName}</label>
            </div>
            <div className={DENTAL_PAYMENT_TABLE_EXPAND_COL}>
                <div className={DENTAL_PAYMENT_TABLE_EXPAND_AMOUNT}>{moneyByLocale(amount)}</div>
            </div>
        </div>
    )
})
