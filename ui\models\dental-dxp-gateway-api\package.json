{"name": "@eisgroup/dental-dxp-gateway-api", "version": "25.7.0-SNAPSHOT.202508080205", "description": "Autogenerated Dental DXP Services and Models", "main": "target/dist/js/src/index.js", "license": "UNLICENSED", "typings": "target/dist/definitions/src/index.d.ts", "files": ["target/dist/"], "publishConfig": {"registry": "https://genesis-npm-release.exigengroup.com/repository/genesis-npm-release/"}, "dependencies": {}, "scripts": {"build": "tsc --project tsconfig.build.json", "postbuild": "cpx \"./src/**/*.{json,less,ico,html,gif,svg,png,ttf,woff,woff2,js,jsx}\" ./target/dist/js/src", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "generate-models": "mvn clean && mvn install"}}