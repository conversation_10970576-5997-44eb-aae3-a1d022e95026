{"name": "@eisgroup/dental-product-services", "version": "25.7.0-SNAPSHOT.202508120630", "description": "Contains services that are reused in different View Modules of Dental Product.", "license": "UNLICENSED", "publishConfig": {"registry": "https://genesis-npm-release.exigengroup.com/repository/genesis-npm-release/"}, "main": "target/dist/js/src/index.js", "typings": "target/dist/definitions/src/index.d.ts", "files": ["target/dist/"], "dependencies": {"@eisgroup/cap-gateway-client": "25.10.0-SNAPSHOT.202507152214", "@eisgroup/cap-models": "25.10.0-SNAPSHOT.202507152214", "@eisgroup/claim-dental-customer-service-api": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/claim-dental-policy-service-api": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/dental-claim-ms-api": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/dental-models": "25.7.0-SNAPSHOT.202508120630", "@eisgroup/common": "100.0.0", "@eisgroup/common-types": "100.0.0", "@eisgroup/environment": "100.0.0", "@eisgroup/auth": "100.0.0", "@eisgroup/form-kraken-core": "25.8.0-202506231223", "@eisgroup/kraken-core": "25.8.0-202506231223", "@eisgroup/i18n": "100.0.0", "@eisgroup/ioc": "100.0.0", "@eisgroup/data.either": "100.0.0", "moment": "~2.29.4", "rxjs": "5.5.7"}, "peerDependencies": {"@eisgroup/auth": "100.0.0", "@eisgroup/common": "100.0.0", "@eisgroup/common-types": "100.0.0", "@eisgroup/ioc": "100.0.0", "@eisgroup/models-api": "100.0.0"}, "scripts": {"build": "tsc --project tsconfig.build.json", "postbuild": "cpx \"./src/**/*.{json,less,ico,html,gif,svg,png,ttf,woff,woff2,js,jsx}\" ./target/dist/js/src", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "lint": "infra-scripts lint --eslint", "test": "vitest run --passWithNoTests"}}