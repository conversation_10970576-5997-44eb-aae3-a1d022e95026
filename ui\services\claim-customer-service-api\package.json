{"name": "@eisgroup/claim-dental-customer-service-api", "version": "25.7.0-SNAPSHOT.202508080205", "description": "Claim customer dxp-api interfaces", "main": "target/dist/js/src/index.js", "typings": "target/dist/definitions/src/index.d.ts", "files": ["target/dist/"], "dependencies": {"@eisgroup/ioc": "100.0.0"}, "scripts": {"build": "tsc --project tsconfig.build.json", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "lint": "yarn eslint \"src/**/*.ts{,x}\""}, "publishConfig": {"registry": "https://genesis-npm-release.exigengroup.com/repository/genesis-npm-release/"}, "v20ModuleType": "reference implementation library"}