@CapEndpoint("getDentalFinancialData")
Transformation CapDentalSettlementToFinancialData {
    Input {
        CapDentalSettlement.CapDentalSettlementEntity as settlement
    }
    Output {
        *CapDentalPaymentTemplate.CapDentalPaymentAllocationTemplateEntity as allocations
    }

    Mapping allocations is settlement.settlementResult.entries {
        //Mapping to entry object to pass whole object to procedure and avoid collection of accumulators flattening
        Var entry is New() {
            Attr calculationResult is Super().calculationResult
            Attr reservedAccumulators is Super().reservedAccumulators
        }

        Attr allocationSource is ToExtLink(Super().settlement)
        Attr allocationLobCd is "Dental"
        Attr allocationLossInfo is produceAllocationLossInfo(Super().settlement)
        Attr allocationDentalDetails is produceAllocationDentalDetails(Super().settlement, entry)
        Attr allocationPayeeDetails is producePayeeDetails(Super().settlement)

        Producer produceAllocationLossInfo(settlement) {
            Attr lossSource is settlement.claimLossIdentification
        }

        Producer produceAllocationDentalDetails(settlement, entry) {
            Var orthoVar is First(FlatMap(settlement.settlementLossInfo.submittedProcedures.ortho))

            Attr grossBenefitAmount is entry.calculationResult.netBenefitAmount
            Attr transactionTypeCd is settlement.settlementLossInfo.claimData.transactionType
            //GENESIS-354142
            Attr receivedDate is settlement.settlementLossInfo.claimData.receivedDate
            Attr orthoFrequencyCd is orthoVar.orthoFrequencyCd
            Attr orthoMonthQuantity is orthoVar.orthoMonthQuantity
            Attr dateOfService is First(settlement.settlementLossInfo.submittedProcedures[filterById]).dateOfService
            //GENESIS-366362
            Attr patient is AsExtLink(settlement.settlementLossInfo.claimData.patientRole.registryId)
            Attr accumulatorDetails is SafeInvoke(entry.reservedAccumulators, produceAccumulatorDetails(entry.reservedAccumulators))
            Attr procedureID is entry.calculationResult.procedureID

            //GENESIS-354142
            Attr source is settlement.settlementLossInfo.claimData.source
            Attr riskStateCd is First(settlement.settlementLossInfo.submittedProcedures[filterById]).certPolicyInfo.riskStateCd
        }

        Producer producePayeeDetails(settlement) {
            Var payeeTypeVar is settlement.settlementLossInfo.claimData.payeeType

            Attr payeeTypeCd is payeeTypeVar
            //GENESIS-366362
            Attr payee is
                    Ternary(Equals(payeeTypeVar, "AlternatePayee"), AsExtLink(settlement.settlementLossInfo.claimData.alternatePayeeRole,registryId),
                    Ternary(Equals(payeeTypeVar, "Provider"), AsExtLink(settlement.settlementLossInfo.claimData.providerRole.providerLink),
                    Ternary(Equals(payeeTypeVar, "PrimaryInsured"), AsExtLink(settlement.settlementLossInfo.claimData.policyholderRole.registryId), Null())))
        }

        Producer produceAccumulatorDetails(accumulatorDetail) {
            Attr accumulatorType is accumulatorDetail.accumulatorType
            Attr networkType is accumulatorDetail.networkType
            Attr renewalType is accumulatorDetail.renewalType
            Attr appliesToProcedureCategories is accumulatorDetail.appliesToProcedureCategories
            Attr accumulatorAmount is accumulatorDetail.reservedAmount
            Attr term is accumulatorDetail.term
        }

        Filter filterById {
            _key.id == entry.calculationResult.procedureID
        }
    }

}
