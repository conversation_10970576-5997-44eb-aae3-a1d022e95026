import React from 'react'
import {observer} from 'mobx-react'

import {MoneyFormat} from '@eisgroup/dental-core'
import {LocalizationUtils, t} from '@eisgroup/i18n'
import {Money} from '@eisgroup/models-api'
import {Tooltip} from '@eisgroup/ui-kit'

import {
    BALANCE_HEADER_AMOUNT_BOX,
    BALANCE_HEADER_RIGHT_SECTION,
    BALANCE_HEADER_RIGHT_SECTION_TOOLTIP
} from './classnames'

import moneyValue = LocalizationUtils.moneyValue

interface OverpaymentAmountProps {
    totalBalanceAmount: Money | undefined
}

export const OverpaymentAmount: React.FC<OverpaymentAmountProps> = observer(({totalBalanceAmount}) => {
    const renderTotalAmount = (total?: Money): string => {
        let totalAmountView = t('dental-product:balance_table_amount_box_0')
        if (total && total?.amount > 0) {
            totalAmountView = t('dental-product:balance_table_amount_box_underpayment')
        } else if (total && total?.amount < 0) {
            totalAmountView = t('dental-product:balance_table_amount_box_overpayment')
        }
        return totalAmountView
    }

    const checkIfMoneyIsPositive = (value?: string): boolean => {
        return value ? value.includes('-') : false
    }
    const totalAmountView = renderTotalAmount(totalBalanceAmount)

    return (
        <div className={BALANCE_HEADER_RIGHT_SECTION}>
            <div className={BALANCE_HEADER_AMOUNT_BOX}>
                <Tooltip
                    overlayClassName={BALANCE_HEADER_RIGHT_SECTION_TOOLTIP}
                    title={totalBalanceAmount?.amount ? <MoneyFormat value={totalBalanceAmount?.amount} /> : ''}
                    placement='top'
                    trigger='hover'
                >
                    <h2
                        className={
                            totalBalanceAmount &&
                            checkIfMoneyIsPositive(moneyValue(totalBalanceAmount?.amount).toString())
                                ? 'total-balance-right-section-money text-balance-right-section-money-red'
                                : 'text-balance-right-section-money'
                        }
                    >
                        {totalBalanceAmount?.amount || totalBalanceAmount?.amount === 0 ? (
                            <MoneyFormat value={totalBalanceAmount?.amount} />
                        ) : (
                            t('dental-product:not_available')
                        )}
                    </h2>
                </Tooltip>
                <div>{totalAmountView}</div>
            </div>
        </div>
    )
})
