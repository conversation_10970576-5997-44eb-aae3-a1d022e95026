// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "ModelType"
  ],
  "moduleType":"CapPayment",
  "name":"CapDentalPaymentDefinition",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapDentalPaymentEntity"
  },
  "storeDeterminants":[
    "ModelName"
  ],
  "types":{
    "CapDentalPaymentAllocationAccumulatorDetailsEntity":{
      "attributes":{
        "accumulatorAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Accumulator amount used for the allocation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Accumulator amount used for the allocation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"accumulatorAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "accumulatorType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Accumulator type."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Accumulator type.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"accumulatorType",
          "type":{
            "type":"STRING"
          }
        },
        "appliesToProcedureCategories":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines to which procedure category(s) maximum/deductible applies to."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines to which procedure category(s) maximum/deductible applies to.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"appliesToProcedureCategories",
          "type":{
            "type":"STRING"
          }
        },
        "appliesToProcedureCategory":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines to which procedure category accumulator applies to."
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines to which procedure category accumulator applies to.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"appliesToProcedureCategory",
          "type":{
            "type":"STRING"
          }
        },
        "networkType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Network type for accumulator."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Network type for accumulator.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"networkType",
          "type":{
            "type":"STRING"
          }
        },
        "renewalType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Renewal type for accumulator."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Renewal type for accumulator.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"renewalType",
          "type":{
            "type":"STRING"
          }
        },
        "term":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Accumulator Term."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Accumulator Term.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"term",
          "type":{
            "type":"Term"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation acumulator details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation acumulator details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalPaymentAllocationAccumulatorDetailsEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentAllocationAccumulatorDetailsEntity"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Allocation acumulator details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Allocation acumulator details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationAccumulatorDetailsEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationDentalDetailsEntity":{
      "attributes":{
        "accumulatorDetails":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation acumulator details."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation acumulator details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"accumulatorDetails",
          "type":{
            "type":"CapDentalPaymentAllocationAccumulatorDetailsEntity"
          }
        },
        "transactionTypeCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental claim transaction type."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental claim transaction type.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"transactionTypeCd",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental allocation details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental allocation details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalPaymentAllocationDentalDetailsEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentAllocationDentalDetailsEntity"
      ],
      "extLinks":{
        "patient":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "URI to the patient."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"URI to the patient.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"patient",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Dental allocation details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Dental allocation details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationDentalDetailsEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationEntity":{
      "attributes":{
        "allocationDentalDetails":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental allocation details."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental allocation details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationDentalDetails",
          "type":{
            "type":"CapDentalPaymentAllocationDentalDetailsEntity"
          }
        },
        "allocationGrossAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Prorated Gross Amount."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Prorated Gross Amount.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationGrossAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationLobCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation Line Of Business code."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation Line Of Business code.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationLobCd",
          "type":{
            "type":"STRING"
          }
        },
        "allocationLossInfo":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation claim details."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation claim details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationLossInfo",
          "type":{
            "type":"CapDentalPaymentAllocationLossInfoEntity"
          }
        },
        "allocationNetAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Prorated NET Amount."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Prorated NET Amount.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationNetAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationPayableItem":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Details of what is paid for."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Details of what is paid for.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationPayableItem",
          "type":{
            "type":"CapDentalPaymentAllocationPayableItemEntity"
          }
        },
        "reserveType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation reserve type."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation reserve type.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"reserveType",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "An object which extends payment allocations details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"An object which extends payment allocations details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Entity for Payment/Recovery allocation information."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Entity for Payment/Recovery allocation information.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentAllocation"
            }
          ],
          "typeName":"CapDentalPaymentAllocationEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentAllocationEntity"
      ],
      "extLinks":{
        "allocationSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the Claim Loss settlement."
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the Claim Loss settlement.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "An object which extends payment allocations details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment allocation tax split results"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment allocation tax split results",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"taxSplitResults",
                      "type":{
                        "type":"CapPaymentAllocationTaxSplitResult"
                      }
                    },
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment allocation addition split results"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment allocation addition split results",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"additionSplitResults",
                      "type":{
                        "type":"CapPaymentAllocationAdditionSplitResult"
                      }
                    },
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment allocation reduction split results"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment allocation reduction split results",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"reductionSplitResults",
                      "type":{
                        "type":"CapPaymentAllocationReductionSplitResult"
                      }
                    }
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"An object which extends payment allocations details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationLossInfoEntity":{
      "attributes":{
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation claim details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation claim details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Defines payment allocation loss info details."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Defines payment allocation loss info details.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentAllocationLossInfo"
            }
          ],
          "typeName":"CapDentalPaymentAllocationLossInfoEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentAllocationLossInfoEntity"
      ],
      "extLinks":{
        "lossSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the Claim Loss."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the Claim Loss.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"lossSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Allocation claim details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Allocation claim details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationLossInfoEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationPayableItemEntity":{
      "attributes":{
        "orthoMonth":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Month number for which allocation is paid."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Month number for which allocation is paid.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"orthoMonth",
          "type":{
            "type":"NUMBER"
          }
        },
        "procedureID":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Related Settlement Result entry's procedure ID."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Related Settlement Result entry's procedure ID.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"procedureID",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental allocation payable item details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental allocation payable item details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Defines payment allocation payable item."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Defines payment allocation payable item.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentAllocationPayableItem"
            }
          ],
          "typeName":"CapDentalPaymentAllocationPayableItemEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentAllocationPayableItemEntity"
      ],
      "extLinks":{
        "claimSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to dental claim."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to dental claim.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"claimSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Dental allocation payable item details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Dental allocation payable item details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationPayableItemEntity",
      "references":{
      }
    },
    "CapDentalPaymentDetailsEntity":{
      "attributes":{
        "payeeDetails":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment payee details."
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.model.features.KrakenChildContext":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.model.features.KrakenField":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment payee details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            },
            "class com.eisgroup.genesis.model.features.KrakenChildContext":{
            },
            "class com.eisgroup.genesis.model.features.KrakenField":{
            }
          },
          "name":"payeeDetails",
          "type":{
            "type":"CapDentalPaymentPayeeDetailsEntity"
          }
        },
        "paymentAllocations":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment allocations details."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment allocations details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"paymentAllocations",
          "type":{
            "type":"CapDentalPaymentAllocationEntity"
          }
        },
        "paymentDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "The payment post date."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"The payment post date.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"paymentDate",
          "type":{
            "type":"DATETIME"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "An object which extends payment details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"An object which extends payment details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Subentity which extends to payment/recovery payee details."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Subentity which extends to payment/recovery payee details.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentDetails"
            }
          ],
          "typeName":"CapDentalPaymentDetailsEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentDetailsEntity"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "An object which extends payment details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment additions"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment additions",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"paymentAdditions",
                      "type":{
                        "type":"CapPaymentAddition"
                      }
                    },
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment reductions"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment reductions",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"paymentReductions",
                      "type":{
                        "type":"CapPaymentReduction"
                      }
                    },
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment taxes"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment taxes",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"paymentTaxes",
                      "type":{
                        "type":"CapPaymentTax"
                      }
                    }
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"An object which extends payment details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentDetailsEntity",
      "references":{
      }
    },
    "CapDentalPaymentEntity":{
      "attributes":{
        "cancelationDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Date when the payment was canceled or issue failed."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Date when the payment was canceled or issue failed.",
              "messageBundle":"domain-messages/CapDentalPaymentDefinition/description"
            }
          },
          "name":"cancelationDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "creationDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "A date when the payment was created."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"A date when the payment was created.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"creationDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "direction":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines if payment is incoming or outgoing."
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.lookups.constraints.Lookup":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "PaymentDirection"
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines if payment is incoming or outgoing.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            },
            "class com.eisgroup.genesis.lookups.constraints.Lookup":{
              "lookupName":"PaymentDirection"
            }
          },
          "name":"direction",
          "type":{
            "type":"STRING"
          }
        },
        "messages":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Entity that holds error and EOB messages details."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.constraints.ReadOnly":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Entity that holds error and EOB messages details.",
              "messageBundle":"domain-messages/CapDentalPaymentDefinition/description"
            },
            "class com.eisgroup.genesis.factory.model.constraints.ReadOnly":{
            }
          },
          "name":"messages",
          "type":{
            "type":"CapDentalPaymentMessageEntity"
          }
        },
        "paymentNetAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Final total NET amount of the payment."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Final total NET amount of the payment.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"paymentNetAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "paymentNumber":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Unique payment number."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Unique payment number.",
              "messageBundle":"domain-messages/CapDentalPaymentDefinition/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"paymentNumber",
          "type":{
            "type":"STRING"
          }
        },
        "state":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment state in the lifecycle."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment state in the lifecycle.",
              "messageBundle":"domain-messages/CapDentalPaymentDefinition/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"state",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines manual payment/recovery information. This is the Root Entity of CAP Payment Domain."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      [
                        "CapPayment"
                      ]
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.lifecycle.statemachine.features.StateMachine":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines manual payment/recovery information. This is the Root Entity of CAP Payment Domain.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
                "CapPayment"
              ]
            },
            "class com.eisgroup.genesis.lifecycle.statemachine.features.StateMachine":{
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Defines manual payment/recovery information."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Defines manual payment/recovery information.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentInfo"
            },
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "modelTypes":[
                  ]
                }
              },
              "parents":[
              ],
              "typeName":"RootEntity"
            }
          ],
          "typeName":"CapPayment"
        }
      ],
      "baseTypes":[
        "CapPayment"
      ],
      "extLinks":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental claim for which the payment is created."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental claim for which the payment is created.",
              "messageBundle":"domain-messages/CapDentalPaymentDefinition/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        },
        "paymentSchedule":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the schedule the payment was created from."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the schedule the payment was created from.",
              "messageBundle":"domain-messages/CapDentalPaymentDefinition/description"
            }
          },
          "name":"paymentSchedule",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Payment transaction information. The Root Entity of CAP Payment Domain."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.lifecycle.statemachine.features.StateMachine":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Payment transaction information. The Root Entity of CAP Payment Domain.",
          "messageBundle":"domain-messages/CapDentalPaymentDefinition/description"
        },
        "class com.eisgroup.genesis.lifecycle.statemachine.features.StateMachine":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentEntity",
      "references":{
        "paymentDetails":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental payment details."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.modeled.features.Embedded":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.model.features.KrakenChildContext":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.model.features.KrakenField":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental payment details.",
              "messageBundle":"domain-messages/CapDentalPaymentDefinition/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Embedded":{
            },
            "class com.eisgroup.genesis.model.features.KrakenChildContext":{
            },
            "class com.eisgroup.genesis.model.features.KrakenField":{
            }
          },
          "name":"paymentDetails",
          "type":"CapDentalPaymentDetailsEntity"
        }
      }
    },
    "CapDentalPaymentMessageEntity":{
      "attributes":{
        "code":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Message code."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Message code.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"code",
          "type":{
            "type":"STRING"
          }
        },
        "message":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Message text."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Message text.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"message",
          "type":{
            "type":"STRING"
          }
        },
        "severity":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Message severity."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Message severity.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"severity",
          "type":{
            "type":"STRING"
          }
        },
        "source":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Message source."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Message source.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"source",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines a message that can be forwarded to the user on some exceptions."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines a message that can be forwarded to the user on some exceptions.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"MessageType"
        }
      ],
      "baseTypes":[
        "MessageType"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Holds information of message type."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Holds information of message type.",
          "messageBundle":"domain-messages/CapDentalPaymentDefinition/description"
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentMessageEntity",
      "references":{
      }
    },
    "CapDentalPaymentPayeeDetailsEntity":{
      "attributes":{
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "An object which extends payment payee details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"An object which extends payment payee details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Defines payment payee details."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Defines payment payee details.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentPayeeDetails"
            }
          ],
          "typeName":"CapDentalPaymentPayeeDetailsEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentPayeeDetailsEntity"
      ],
      "extLinks":{
        "payee":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the CEM."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the CEM.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"payee",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "An object which extends payment payee details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                    {
                      "cardinality":"SINGLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                        },
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment method details"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment method details",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"paymentMethodDetails",
                      "type":{
                        "type":"CapPaymentMethodDetails"
                      }
                    }
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":true
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"An object which extends payment payee details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.features.Deprecated":{
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentPayeeDetailsEntity",
      "references":{
      }
    },
    "CapDentalProcessPaymentLifecycleOutput":{
      "attributes":{
        "paymentEvent":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"paymentEvent",
          "type":{
            "type":"STRING"
          }
        },
        "paymentMethodType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"paymentMethodType",
          "type":{
            "type":"STRING"
          }
        },
        "paymentUri":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"paymentUri",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Used to process Payment Hub lifecycle event in automated proccess."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Used to process Payment Hub lifecycle event in automated proccess.",
          "messageBundle":"domain-messages/CapDentalPaymentDefinition/description"
        }
      },
      "links":{
      },
      "name":"CapDentalProcessPaymentLifecycleOutput",
      "references":{
      }
    },
    "Term":{
      "attributes":{
        "effectiveDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"effectiveDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "expirationDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"expirationDate",
          "type":{
            "type":"DATETIME"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"Term"
        }
      ],
      "baseTypes":[
        "Term"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"Term",
      "references":{
      }
    }
  },
  "variations":[
    {
      "name":"overpaymentWaive"
    },
    {
      "name":"payment"
    },
    {
      "name":"recovery"
    },
    {
      "name":"underpayment"
    }
  ],
  "version":"1"
}
export namespace CapDentalPaymentDefinition {
    export type Variations = 'overpaymentWaive' | 'payment' | 'recovery' | 'underpayment'

    export class CapDentalPaymentAllocationAccumulatorDetailsEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentAllocationAccumulatorDetailsEntity {
    constructor() { super(CapDentalPaymentAllocationAccumulatorDetailsEntity.name) }
        readonly accumulatorAmount?: Money
        readonly accumulatorType?: string
        readonly appliesToProcedureCategories: string[] = []
        readonly appliesToProcedureCategory?: string
        readonly networkType?: string
        readonly renewalType?: string
        readonly term?: Term
    }

    export class CapDentalPaymentAllocationDentalDetailsEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentAllocationDentalDetailsEntity {
    constructor() { super(CapDentalPaymentAllocationDentalDetailsEntity.name) }
        readonly accumulatorDetails: CapDentalPaymentAllocationAccumulatorDetailsEntity[] = []
        readonly patient?: MAPI.ExternalLink
        readonly transactionTypeCd?: string
    }

    export class CapDentalPaymentAllocationEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentAllocationEntity {
    constructor() { super(CapDentalPaymentAllocationEntity.name) }
        readonly allocationDentalDetails?: CapDentalPaymentAllocationDentalDetailsEntity
        readonly allocationGrossAmount?: Money
        readonly allocationLobCd?: string
        readonly allocationLossInfo?: CapDentalPaymentAllocationLossInfoEntity
        readonly allocationNetAmount?: Money
        readonly allocationPayableItem?: CapDentalPaymentAllocationPayableItemEntity
        readonly allocationSource?: MAPI.ExternalLink
        readonly reserveType?: string
    }

    export class CapDentalPaymentAllocationLossInfoEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentAllocationLossInfoEntity {
    constructor() { super(CapDentalPaymentAllocationLossInfoEntity.name) }
        readonly lossSource?: MAPI.ExternalLink
    }

    export class CapDentalPaymentAllocationPayableItemEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentAllocationPayableItemEntity {
    constructor() { super(CapDentalPaymentAllocationPayableItemEntity.name) }
        readonly claimSource?: MAPI.ExternalLink
        readonly orthoMonth?: number
        readonly procedureID?: string
    }

    export class CapDentalPaymentDetailsEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentDetailsEntity, MAPI.RootBusinessType {
    constructor() { super(CapDentalPaymentDetailsEntity.name) }
        readonly _modelName: string = 'CapDentalPaymentDefinition'
        readonly _modelType: string = 'CapPayment'
        readonly _modelVersion?: string = '1'
        readonly _variation: Variations = 'payment'
        readonly payeeDetails?: CapDentalPaymentPayeeDetailsEntity
        readonly paymentAllocations: CapDentalPaymentAllocationEntity[] = []
        readonly paymentDate?: Date
    }

    export class CapDentalPaymentEntity extends MAPI.BusinessEntity implements BusinessTypes.CapPayment, MAPI.RootBusinessType {
    constructor() { super(CapDentalPaymentEntity.name) }
        readonly _modelName: string = 'CapDentalPaymentDefinition'
        readonly _modelType: string = 'CapPayment'
        readonly _modelVersion?: string = '1'
        readonly _variation: Variations = 'payment'
        readonly cancelationDate?: Date
        readonly creationDate?: Date
        readonly direction?: string
        readonly messages: CapDentalPaymentMessageEntity[] = []
        readonly originSource?: MAPI.ExternalLink
        readonly paymentDetails?: CapDentalPaymentDetailsEntity
        readonly paymentNetAmount?: Money
        readonly paymentNumber?: string
        readonly paymentSchedule?: MAPI.ExternalLink
        readonly state?: string
    }

    export class CapDentalPaymentMessageEntity extends MAPI.BusinessEntity implements BusinessTypes.MessageType {
    constructor() { super(CapDentalPaymentMessageEntity.name) }
        readonly code?: string
        readonly message?: string
        readonly severity?: string
        readonly source?: string
    }

    export class CapDentalPaymentPayeeDetailsEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentPayeeDetailsEntity {
    constructor() { super(CapDentalPaymentPayeeDetailsEntity.name) }
        readonly payee?: MAPI.ExternalLink
    }

    export class CapDentalProcessPaymentLifecycleOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalProcessPaymentLifecycleOutput.name) }
        readonly paymentEvent?: string
        readonly paymentMethodType?: string
        readonly paymentUri?: string
    }

    export class Term extends MAPI.BusinessEntity implements BusinessTypes.Term {
    constructor() { super(Term.name) }
        readonly effectiveDate?: Date
        readonly expirationDate?: Date
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapDentalPaymentAllocationAccumulatorDetailsEntity, ()=> new CapDentalPaymentAllocationAccumulatorDetailsEntity)
    factory.registerEntity(CapDentalPaymentAllocationDentalDetailsEntity, ()=> new CapDentalPaymentAllocationDentalDetailsEntity)
    factory.registerEntity(CapDentalPaymentAllocationEntity, ()=> new CapDentalPaymentAllocationEntity)
    factory.registerEntity(CapDentalPaymentAllocationLossInfoEntity, ()=> new CapDentalPaymentAllocationLossInfoEntity)
    factory.registerEntity(CapDentalPaymentAllocationPayableItemEntity, ()=> new CapDentalPaymentAllocationPayableItemEntity)
    factory.registerEntity(CapDentalPaymentDetailsEntity, ()=> new CapDentalPaymentDetailsEntity)
    factory.registerEntity(CapDentalPaymentEntity, ()=> new CapDentalPaymentEntity)
    factory.registerEntity(CapDentalPaymentMessageEntity, ()=> new CapDentalPaymentMessageEntity)
    factory.registerEntity(CapDentalPaymentPayeeDetailsEntity, ()=> new CapDentalPaymentPayeeDetailsEntity)
    factory.registerEntity(CapDentalProcessPaymentLifecycleOutput, ()=> new CapDentalProcessPaymentLifecycleOutput)
    factory.registerEntity(Term, ()=> new Term)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}