/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {action, computed, observable, runInAction} from 'mobx'

import {customerCommonService, organizationCustomerService} from '@eisgroup/claim-dental-customer-service-api'
import {dentalPolicyService} from '@eisgroup/claim-dental-policy-service-api'
import {
    dentalClaimService,
    DentalPolicyData,
    DentalPolicySummary,
    unverifiedDentalPolicyService
} from '@eisgroup/dental-product-services'
import {FormApi} from '@eisgroup/form-core'

import type {IndividualCustomer, OrganizationCustomer} from '@eisgroup/claim-dental-customer-service-api'
import type {DentalIndividualPolicy, DentalMasterPolicy} from '@eisgroup/claim-dental-policy-service-api'

import {POLICY_AND_PATIENT_STORE} from '../../../../utils/common/constants'
import {EntityLink, getInsuredUrisFromPolicy} from '../../../../utils/helpers/entities'
import {BaseRootStoreImpl} from '../../../stores'
import {INDIVIDUALCUSTOMER, ORGANIZATIONCUSTOMER} from '../constants'
import {filterLatestPolicyList} from '../utils'

export interface DentalPolicyAndPatientStore {
    policyWithInsured: DentalIndividualPolicy
    policy: DentalPolicyData | null
    dentalMasterPolicy: DentalMasterPolicy
    policyList: DentalPolicyData[]
    insureds: IndividualCustomer[]
    groupSponsor: OrganizationCustomer
    isLoading: boolean
    isEditMode: boolean
    policyEditable: boolean
    initStore: () => Promise<void>
    removePolicy: () => void
    addPolicy: (policy: DentalPolicyData) => Promise<void>
    setPolicyList: (policyList: DentalPolicyData[]) => void
    searchPolicy: (text: string, customerNumber?: string) => Promise<void>
    searchDentalMasterPolicyList: (text: string) => Promise<void>
    setCurrentPolicy: (policy: DentalPolicySummary) => void
    setEditMode: (mode: boolean) => void
    setPolicyEditable: (mode: boolean) => void
}

export class DentalPolicyAndPatientStoreImpl extends BaseRootStoreImpl implements DentalPolicyAndPatientStore {
    @observable policy: DentalPolicyData | null

    @observable dentalMasterPolicy: DentalMasterPolicy

    @observable policyWithInsured: DentalIndividualPolicy

    @observable policyList: DentalPolicyData[] = []

    @observable masterPolicyList: any[] = []

    @observable insureds: IndividualCustomer[] = []

    @observable groupSponsor: OrganizationCustomer

    @observable isEditMode = false

    @observable policyEditable = true

    form: FormApi

    @computed
    get isLoading(): boolean {
        return (
            !this.actionsStore.isCompleted(POLICY_AND_PATIENT_STORE.INIT_STORE) ||
            this.actionsStore.isRunning(POLICY_AND_PATIENT_STORE.LOAD_INSUREDS) ||
            this.actionsStore.isRunning(POLICY_AND_PATIENT_STORE.LOAD_DENTAL_MASTER_POLICY) ||
            this.actionsStore.isRunning(POLICY_AND_PATIENT_STORE.LOAD_MASTER_POLICY_LIST) ||
            this.actionsStore.isRunning(POLICY_AND_PATIENT_STORE.LOAD_GROUP_SPONSOR)
        )
    }

    constructor(form: FormApi) {
        super()
        this.form = form
    }

    @action
    initStore = async (): Promise<void> => {
        this.actionsStore.startAction(POLICY_AND_PATIENT_STORE.INIT_STORE)
        const policyId = this.form.getState().values.entity.policyId
        const rootId = policyId ? EntityLink.from(policyId)?.rootId : ''
        if (rootId) {
            await this.loadDentalPolicyWithInsureds(rootId)

            await this.searchDentalMasterPolicy()

            await this.searchGroupSponsor()

            await this.loadInsureds()

            this.setPolicyEditable(false)
        } else {
            this.setEditMode(true)
        }

        this.actionsStore.completeAction(POLICY_AND_PATIENT_STORE.INIT_STORE)
    }

    @action
    setEditMode = (mode: boolean) => {
        runInAction(() => {
            this.isEditMode = mode
        })
    }

    @action
    setPolicyEditable = (mode: boolean) => {
        runInAction(() => {
            this.policyEditable = mode
        })
    }

    @action
    removePolicy = () => {
        this.setEditMode(true)
        this.form.batch(() => {
            this.form.change('entity.policyId')
            this.form.change('entity.lossDetail.claimData.patientRole.registryId')
            this.form.change('entity.lossDetail.claimData.policyholderRole.registryId')
        })
        runInAction(() => {
            this.policy = null
            this.policyWithInsured = {} as DentalIndividualPolicy
            this.dentalMasterPolicy = {} as DentalMasterPolicy
            this.groupSponsor = {} as OrganizationCustomer
            this.insureds = []
        })
    }

    @action
    setCurrentPolicy = policy => {
        runInAction(() => {
            this.policy = policy
        })
    }

    @action
    setPolicyList = (policyList: DentalPolicyData[]) => {
        runInAction(() => {
            this.policyList = policyList
        })
    }

    @action
    addPolicy = async (policy: DentalPolicyData) => {
        if (!policy) {
            return
        }
        let policyRootId = policy?._key?.rootId
        if (policy?._uri) {
            policyRootId = EntityLink.from(policy?._uri)?.rootId
        }
        let policyholderRoleRegistryId = policy?.customer
        // for unverify policy
        if (!policyholderRoleRegistryId) {
            policyholderRoleRegistryId = policy.insureds?.find(v => v.isMain)?.registryTypeId as string
        }
        // for unverify policy
        let entityPolicyId = policy.capPolicyId
        if (!entityPolicyId) {
            entityPolicyId = `capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/${policyRootId}`
        }
        this.form.batch(() => {
            this.form.change('entity.lossDetail.claimData.policyholderRole.registryId', policyholderRoleRegistryId)
            this.form.change('entity.policyId', entityPolicyId)
        })

        if (this.policy?.policyData) {
            this.policyWithInsured = this.policy.policyData
        } else {
            await this.loadDentalPolicyWithInsureds(policy?.rootId)
        }

        await this.searchDentalMasterPolicy()

        await this.searchGroupSponsor()

        await this.loadInsureds()
    }

    @action
    searchPolicy = async (text: string, customerType?: string): Promise<void> => {
        const result = await this.callService(
            dentalPolicyService().searchDentalPolicy(text),
            POLICY_AND_PATIENT_STORE.LOAD_POLICY
        )
        let filteredPolicyList = result
        if (result.length > 0) {
            filteredPolicyList = filterLatestPolicyList(result)
            const policyList = await this.searchPolicyWithRootId(filteredPolicyList.map(n => n.rootId))

            const policyListWithAllData = filteredPolicyList.map(policy => {
                const filteredPolicy = policyList.find(a => {
                    return a._key?.rootId === policy.rootId
                })
                return {
                    ...policy,
                    policyData: filteredPolicy
                }
            })

            runInAction(() => {
                if (customerType === INDIVIDUALCUSTOMER && filteredPolicyList.length === 1) {
                    this.policy = result[0]
                    this.setPolicyEditable(false)
                    this.addPolicy(result[0])
                }

                if (customerType === ORGANIZATIONCUSTOMER) {
                    this.policyList = this.masterPolicyList?.length
                        ? policyListWithAllData.filter(policy => {
                              return this.masterPolicyList.some(masterPolicy => masterPolicy._uri === policy.source)
                          })
                        : []
                } else {
                    this.policyList = policyListWithAllData || []
                }
            })
        } else {
            // this service is search for qa testing UP
            const result2 = (await this.callService(
                dentalClaimService.commonSearchClaimPolicy(text),
                POLICY_AND_PATIENT_STORE.LOAD_POLICY
            )) as any
            filteredPolicyList = result2.result?.map(v => {
                return {
                    ...v,
                    policyData: v,
                    rootId: v._key?.rootId
                }
            })
            runInAction(() => {
                this.policyList = filteredPolicyList
            })
        }
    }

    @action
    searchDentalMasterPolicyList = async (text: string): Promise<void> => {
        const result = await this.callService(
            dentalPolicyService().searchDentalMasterPolicyList(text),
            POLICY_AND_PATIENT_STORE.LOAD_MASTER_POLICY_LIST
        )

        runInAction(() => {
            this.masterPolicyList = result || []
        })
    }

    @action
    searchCustomers = async (customerIds: string[]) => {
        const result = await customerCommonService().searchCustomers(customerIds)
        return result
    }

    @action
    searchPolicyWithRootId = async (policyNumbers: string[]) => {
        const result = await dentalPolicyService().searchPolicyWithRootId(policyNumbers)
        return result
    }

    @action
    searchDentalMasterPolicy = async (): Promise<void> => {
        if (!this.policyWithInsured?.masterLink?.source?._uri) {
            this.setEditMode(false)
            return
        }

        const {rootId} = EntityLink.from(this.policyWithInsured?.masterLink?.source?._uri)
        const result = await this.callService(
            dentalPolicyService().loadDentalMasterPolicy({rootId}),
            POLICY_AND_PATIENT_STORE.LOAD_DENTAL_MASTER_POLICY
        )

        runInAction(() => {
            this.dentalMasterPolicy = result as any
        })
    }

    @action
    searchGroupSponsor = async (): Promise<void> => {
        if (!this.dentalMasterPolicy?.customer?._uri) {
            return
        }
        const {rootId} = EntityLink.from(this.dentalMasterPolicy.customer?._uri)
        const result = await this.callService(
            organizationCustomerService().loadOrganizationCustomer(rootId),
            POLICY_AND_PATIENT_STORE.LOAD_GROUP_SPONSOR
        )

        runInAction(() => {
            this.groupSponsor = result
        })
    }

    @action
    loadDentalPolicyWithInsureds = async (rootId: string): Promise<void> => {
        if (!rootId) {
            return
        }
        const capPolicyVersionId = this.form.getState().values.entity.policy?.capPolicyVersionId
        const urlParams = capPolicyVersionId ? EntityLink.from(capPolicyVersionId) : undefined

        let result
        if (urlParams && urlParams.baseType === 'UnverifiedPolicy') {
            result = await this.callService(
                unverifiedDentalPolicyService.loadUnverifiedDentalPolicy({
                    rootId,
                    revisionNo: Number(urlParams.revisionNo)
                })
            )
        } else {
            result = await this.callService(
                dentalPolicyService().loadDentalPolicy({rootId}),
                POLICY_AND_PATIENT_STORE.LOAD_DENTAL_POLICY
            )
        }
        this.setEditMode(false)

        runInAction(() => {
            this.policyWithInsured = result
        })
    }

    @action
    loadInsureds = async (): Promise<void> => {
        if (this.policyWithInsured) {
            const insuredsUris = getInsuredUrisFromPolicy(this.policyWithInsured)
            const result = insuredsUris.length
                ? await this.callService(
                      customerCommonService().searchCustomers(insuredsUris),
                      POLICY_AND_PATIENT_STORE.LOAD_INSUREDS
                  )
                : []
            this.setEditMode(false)

            runInAction(() => {
                this.insureds = result as IndividualCustomer[]
            })
        }
    }
}
