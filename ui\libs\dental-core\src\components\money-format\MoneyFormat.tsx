import React from 'react'
import {observer} from 'mobx-react'

import {LocalizationUtils, useTranslate} from '@eisgroup/i18n'

import moneyValue = LocalizationUtils.moneyValue

export interface MoneyFormatProps {
    readonly className?: string
    readonly value?: number
    readonly options?: {
        isEmpty?: (value?: number) => boolean
        emptyText?: string
        negetiveValueFormatter?: (value: number) => React.ReactNode
    }
}

export const MoneyFormat: React.FC<MoneyFormatProps> = observer(props => {
    const {className, value, options} = props
    const {t} = useTranslate()

    const isEmptyFunction = options?.isEmpty ?? ((curr?: number) => !curr && curr !== 0)
    const emptyText = options?.emptyText ?? t('dental-core:not_available')
    const negetiveValueFormatter =
        options?.negetiveValueFormatter ?? ((curr: number) => `(${moneyValue(Math.abs(curr)).toString()})`)

    return (
        <span className={className}>
            {isEmptyFunction(value) ? <span>{emptyText}</span> : null}
            {value && value < 0 ? (
                <span className='negetive' style={{color: 'red'}}>
                    {negetiveValueFormatter(value)}
                </span>
            ) : null}
            {value === 0 || (value && value >= 0) ? <span>{moneyValue(value).toString()}</span> : null}
        </span>
    )
})
