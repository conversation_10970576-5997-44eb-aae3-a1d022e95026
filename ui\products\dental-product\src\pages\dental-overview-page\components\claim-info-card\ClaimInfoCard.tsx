/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {useEffect} from 'react'
import {observer} from 'mobx-react'

import {useFormState} from '@eisgroup/form'
import {useTranslate} from '@eisgroup/i18n'

import {InfoCard} from '../../../../shared/components'
import {useStore} from '../../store/DentalOverviewPageStore'
import {DENTAL_CLAIM_INFO_CARD} from './classnames'
import {mapInfoCardSectionsFromLoss} from './utils'

export const ClaimInfoCard: React.FC = observer(() => {
    const {loss, settlement, parties, dnIndividual, retrievePolicyProduct, productName} = useStore()
    const {paymentList = []} = useFormState().values
    const payments = paymentList.filter(item => item._type === 'CapDentalPaymentEntity')

    useTranslate()

    // todo remove dnIndividual
    useEffect(() => {
        if (!loss?.policy?.productCd) {
            return
        }
        retrievePolicyProduct(loss?.policy?.productCd)
    })
    const sections = mapInfoCardSectionsFromLoss(loss, settlement, payments, parties, dnIndividual, productName)

    return <InfoCard sections={sections} className={DENTAL_CLAIM_INFO_CARD} />
})
