{"name": "@eisgroup/dental-ui-monorepo", "version": "25.7.0-SNAPSHOT.202508120630", "workspaces": ["apps/*", "libs/*", "services/*", "products/*", "models/*"], "license": "MIT", "private": true, "description": "Dental UI Mono Repository", "publishConfig": {"registry": "https://genesis-npm-release.exigengroup.com/repository/genesis-npm-release/"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{tsx,json,ts,md,js,less}": ["prettier --write", "git add"]}, "engines": {"node": ">=18.18.0", "npm": ">=6.13.4", "yarn": ">=1.22.0"}, "dependencies": {"core-js": "^3.6.5", "react": "^16.8.6", "react-dom": "^16.8.6", "jscodeshift": "17"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.13.10", "@babel/preset-env": "^7.10.4", "@babel/preset-react": "^7.10.4", "@eisgroup/builder": "25.8.0-SNAPSHOT.************", "@eisgroup/builder-integration": "25.8.0-SNAPSHOT.************", "@eisgroup/builder-components": "25.8.0-SNAPSHOT.************", "@eisgroup/infra-scripts": "4.0.6", "@eisgroup/eslint-config-infra-scripts": "3.0.0", "@eisgroup/ui-desktop": "25.8.0-SNAPSHOT.************", "@eisgroup/ui-engine": "25.8.0-SNAPSHOT.************", "@eisgroup/cache": "100.0.0", "@eisgroup/common-security-provider": "100.0.0", "@eisgroup/data.either": "100.0.0", "@eisgroup/oauth": "100.0.0", "@eisgroup/ui-kit-icons": "^100.0.0", "@eisgroup/ui-platform-gateway-client": "100.0.0", "@eisgroup/ui-temporals": "100.0.0", "@eisgroup/common-policy-shares": "25.10.0-SNAPSHOT.************", "@eisgroup/ui-dependencies": "~25.0.4", "@ianvs/prettier-plugin-sort-imports": "^4.1.1", "@storybook/addons": "5.3.19", "@storybook/react": "5.3.19", "@testing-library/jest-dom": "^5.11.9", "@testing-library/user-event": "^12.6.3", "@types/classnames": "2.2.3", "@types/currency-formatter": "1.3.0", "@types/data.either": "100.0.0", "@types/downloadjs": "1.4.0", "@types/draft-js": "0.10.24", "enzyme": "3.10.0", "@types/enzyme": "^3.10.18", "@types/enzyme-adapter-react-16": "^1.0.9", "enzyme-adapter-react-16": "^1.14.0", "@types/es6-promise": "0.0.32", "@types/history": "3.2.3", "@types/i18next": "8.4.2", "@types/invariant": "2.2.29", "@types/jest": "26.0.23", "@types/jquery": "3.3.32", "@types/json-stringify-safe": "5.0.0", "@types/lodash": "4.14.201", "@types/lru-cache": "4.0.0", "@types/moment-timezone": "0.5.12", "@types/nprogress": "0.0.29", "@types/object-path": "0.11.0", "@types/react": "^16.8.24", "@types/react-dom": "^16.8.5", "@types/react-router": "3.0.25", "@types/seamless-immutable": "6.1.2", "@types/testing-library__jest-dom": "^5.9.5", "@types/underscore": "1.8.8", "@types/node": "20.10.6", "@vitejs/plugin-react-swc": "^3.7.2", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "copy-webpack-plugin": "11.0.0", "classnames": "~2.2.0", "cpx": "1.5.0", "husky": "3.1.0", "lint-staged": "10.2.11", "moment-timezone": "0.5.40", "rimraf": "3.0.0", "typescript": "4.9.5", "turbo": "^1.10.16", "vitest": "^2.1.8", "eslint-plugin-react-prefer-function-component": "3.4.0"}, "resolutions": {"react": "^16.8.6", "react-dom": "^16.8.6", "@eisgroup/builder": "25.8.0-SNAPSHOT.************", "@eisgroup/builder-integration": "25.8.0-SNAPSHOT.************", "@eisgroup/builder-components": "25.8.0-SNAPSHOT.************", "@eisgroup/common": "100.0.0", "@eisgroup/form": "100.0.0", "@eisgroup/form-core": "100.0.0", "@eisgroup/lookups": "100.0.0", "@eisgroup/state": "100.0.0", "@eisgroup/models-api": "100.0.0", "@eisgroup/react-application": "100.0.0", "@eisgroup/react-components": "100.0.0", "@eisgroup/react-connectors": "100.0.0", "@eisgroup/react-state-connectors": "100.0.0", "@eisgroup/react-routing": "100.0.0", "@eisgroup/ui-kit": "100.0.0", "@eisgroup/ui-desktop": "25.8.0-SNAPSHOT.************", "@eisgroup/ui-engine": "25.8.0-SNAPSHOT.************", "@eisgroup/form-kraken-core": "25.8.0-************", "@eisgroup/kraken-core": "25.8.0-************", "@eisgroup/bam-ui": "25.10.0-SNAPSHOT.************", "@eisgroup/work-views": "25.10.0-SNAPSHOT.************", "@eisgroup/workium-ui": "25.10.0-SNAPSHOT.************", "@eisgroup/notes-ui": "25.10.0-SNAPSHOT.************", "@eisgroup/alerts-ui": "25.10.0-SNAPSHOT.************", "@eisgroup/efolder-ui": "25.10.0-SNAPSHOT.************", "@eisgroup/work-common": "25.10.0-SNAPSHOT.************", "@eisgroup/efolder-common": "25.10.0-SNAPSHOT.************", "@eisgroup/notes-common": "25.10.0-SNAPSHOT.************", "@eisgroup/alerts-common": "25.10.0-SNAPSHOT.************", "@eisgroup/bam-common": "25.10.0-SNAPSHOT.************", "@eisgroup/bam-services": "25.10.0-SNAPSHOT.************", "@eisgroup/work-services": "25.10.0-SNAPSHOT.************", "@eisgroup/efolder-services": "25.10.0-SNAPSHOT.************", "@eisgroup/notes-services": "25.10.0-SNAPSHOT.************", "@eisgroup/alerts-services": "25.10.0-SNAPSHOT.************", "draft-js": "~0.10.5", "immutable": "~3.8.1", "moment-timezone/moment": "2.18.1", "less": "^3.11.1", "react-idle-timer": "5.5.3", "express": "^4.21.2", "@types/react": "^16.8.24", "@types/react-dom": "^16.8.5", "@types/react-autosuggest": "8.0.2", "@types/react-dropzone": "4.2.0", "@types/react-scroll": "1.5.4", "@types/react-transition-group": "4.2.2"}, "scripts": {"start": "cross-env HOST=*************** npm --prefix ./apps/dental-ui-app start --", "test": "turbo run test --token=EIS1234567890 --concurrency=1  --no-daemon --output-logs=errors-only", "test-coverage": "yarn test -- -- --coverage --coverage.reportOnFailure=true --dangerouslyIgnoreUnhandledErrors=true --no-color || true", "clean:node-modules": "rm -rf node_modules", "clean-all": "turbo run clean:all && yarn clean:node-modules", "validate-version": "yarn infra-scripts version validate", "version": "yarn infra-scripts version update", "build": "yarn validate-version && yarn build-only && yarn lint && yarn test", "build-only": "turbo run build", "lint": "turbo run lint", "lint:fix": "infra-scripts lint --eslint --fix --no-error-on-unmatched-pattern", "release": "infra-scripts release", "safe-publish": "infra-scripts safe-publish", "update-models": "infra-scripts models update", "generate-models": "turbo run generate-models", "generate-i18n-test-bundles": "yarn infra-scripts i18n generate-test-bundles", "remove-i18n-test-bundles": "yarn infra-scripts i18n remove-test-bundles", "build-generator": "yarn --cwd ui-generator install && yarn --cwd ui-generator build", "clean-generator": "yarn --cwd ui-generator clean-all"}, "packageManager": "yarn@1.22.5"}