$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use <PERSON>ite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/products/dental-product[39m

(node:86096) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:63420) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:50352) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:82988) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:127420) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:138380) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:149340) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:100716) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:59524) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:70104) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:4968) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:66572) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:92408) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:104320) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:128676) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/utils.test.ts [2m([22m[2m17 tests[22m[2m)[22m[90m 20[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/store/accumulatorTypeUtils.test.tsx [2m([22m[2m14 tests[22m[2m)[22m[90m 24[2mms[22m[39m
(node:150228) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:91360) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/sub-stores/EditWizardStore.test.tsx [2m([22m[2m21 tests[22m[2m)[22m[90m 28[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/DentalEditPageRootStore.test.tsx [2m([22m[2m17 tests[22m[2m)[22m[90m 38[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/WithinFormEditWrapperStore.test.tsx [2m([22m[2m21 tests[22m[2m)[22m[90m 38[2mms[22m[39m
(node:136536) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:38900) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:56848) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/claim-policy-and-patient/store/PolicyAndPatientStore.test.ts [2m([22m[2m11 tests[22m[2m)[22m[33m 2338[2mms[22m[39m
(node:100056) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-entry-page/components/dental-claims-table/DentalClaimsTable.test.tsx [2m([22m[2m13 tests[22m[2m)[22m[90m 49[2mms[22m[39m
(node:35664) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-entry-page/DentalEntryPage.test.tsx [2m([22m[2m20 tests[22m[2m)[22m[33m 1279[2mms[22m[39m
   [33m[2m✓[22m[39m DentalEntryPage[2m > [22mrendering[2m > [22mshould render the page title [33m357[2mms[22m[39m
(node:94420) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/claim-provider-search/store/ProviderStore.test.ts [2m([22m[2m6 tests[22m[2m)[22m[90m 37[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/DentalEditPage.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[90m 122[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/header/PaymentActionsDropdown.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[33m 743[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/adjudication-tab/adjudication-results-table/AdjudicationResultsTable.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 703[2mms[22m[39m
   [33m[2m✓[22m[39m AdjudicationResultsTable[2m > [22mRender AdjudicationResultsTable Correctly [33m698[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/store/DentalOverviewPageStore.test.tsx [2m([22m[2m22 tests[22m[2m)[22m[90m 96[2mms[22m[39m
(node:32868) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-adjust-page-deprecated/store/DentalAdjustPageStore.test.tsx [2m([22m[2m20 tests[22m[2m)[22m[90m 74[2mms[22m[39m
 [32m✓[39m test/pages/dental-entry-page/store/DentalEntryPageStore.test.tsx [2m([22m[2m15 tests[22m[2m)[22m[90m 61[2mms[22m[39m
(node:65184) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-overview-page/components/claim-info-tab/ClaimInfoTab.test.tsx [2m([22m[2m9 tests[22m[2m)[22m[33m 1167[2mms[22m[39m
   [33m[2m✓[22m[39m ClaimInfoTab[2m > [22mshould switch between multiple tabs correctly [33m325[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/paymentServiceUtils.test.ts [2m([22m[2m7 tests[22m[2m)[22m[90m 24[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/service/useBalanceLogService.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 34[2mms[22m[39m
(node:116776) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:95652) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [31m❯[39m test/pages/dental-overview-page/components/financials-tab/payments/PaymentsTable.test.tsx [2m([22m[2m7 tests[22m[2m | [22m[31m1 failed[39m[2m)[22m[33m 1541[2mms[22m[39m
   [33m[2m✓[22m[39m PaymentsTable[2m > [22mRender payment table correctly [33m769[2mms[22m[39m
[31m   [31m×[31m PaymentsTable[2m > [22mRender expanded row amounts correctly[90m 299[2mms[22m[31m[39m
[31m     → Objects are not valid as a React child (found: object with keys {expandName, amount}). If you meant to render a collection of children, use an array instead.
    in label (at PaymentTableExpandRow.tsx:38)
    in div (at PaymentTableExpandRow.tsx:37)
    in div (at PaymentTableExpandRow.tsx:36)
    in wrappedComponent (at PaymentsTable.tsx:159)
    in div (at PaymentsTable.tsx:150)
    in td (created by TableCell)
    in TableCell (created by TableRow)
    in tr (created by TableRow)
    in TableRow (created by Connect(TableRow))
    in Connect(TableRow) (created by BaseTable)
    in tbody (created by BaseTable)
    in table (created by BaseTable)
    in BaseTable (created by Connect(BaseTable))
    in Connect(BaseTable) (created by BodyTable)
    in div (created by BodyTable)
    in BodyTable (created by ExpandableTable)
    in div (created by ExpandableTable)
    in div (created by ExpandableTable)
    in div (created by ExpandableTable)
    in ExpandableTable (created by Connect(ExpandableTable))
    in Connect(ExpandableTable) (created by Table)
    in Provider (created by Table)
    in Table (created by LocaleReceiver)
    in LocaleReceiver (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Table (created by ForwardRef(TableComponent))
    in ForwardRef(TableComponent) (at PaymentsTable.tsx:169)
    in div (at PaymentsTable.tsx:168)
    in wrappedComponent (at PaymentsTable.test.tsx:281)[39m
 [32m✓[39m test/shared/common/claim-procedures/utils.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[90m 135[2mms[22m[39m
(node:62284) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:56708) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:103432) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:65892) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:118940) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:76012) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:32040) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:34792) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPayments.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[33m 756[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/utils.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 18[2mms[22m[39m
(node:101860) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/utils/common/ValidationUtils.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 8[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/sub-stores/EditWizardRulesExecutionStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 10[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/ClaimPolicyAndPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 276[2mms[22m[39m
(node:87764) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:129052) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:53524) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:133620) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-edit-page/utils/Utils.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[90m 8[2mms[22m[39m
(node:126176) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPaymentExpand.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[90m 116[2mms[22m[39m
(node:64060) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-edit-page/store/UploadDocumentsStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 21[2mms[22m[39m
(node:59856) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-overview-page/components/accumulator-tab/AccumutatorTab.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 507[2mms[22m[39m
   [33m[2m✓[22m[39m AccumulatorTab[2m > [22mBasic Rendering[2m > [22mshould render card with correct title and policy date field [33m504[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/header/AuthorityApproval.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[90m 196[2mms[22m[39m
(node:115080) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:124120) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/upload-document/utils.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 3[2mms[22m[39m
(node:62260) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/review-claim-info/PolicyAndPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 170[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-provider-search/ClaimProviderSearch.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 153[2mms[22m[39m
(node:92340) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:144968) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/upload-document/UploadDocuments.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 174[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/OverpaymentAmount.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 154[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-procedures/claim-procedures-view-items/ClaimProceduresViewItems.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 728[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/AdditionalInformation.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 152[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-provider-search/claim-provider-search-with-field/ClaimProviderSearchWithField.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 972[2mms[22m[39m
   [33m[2m✓[22m[39m ClaimProviderSearchWithField[2m > [22mshould render and update input value [33m405[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/BalanceActivities.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 491[2mms[22m[39m
(node:22240) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:69464) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:106516) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:111220) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:73588) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/review-claim-info/ProviderDetail.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 126[2mms[22m[39m
(node:147628) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/SelectMissingTeethSelect.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 474[2mms[22m[39m
(node:97688) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/review-claim-info/IntakeAndServices.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 355[2mms[22m[39m
   [33m[2m✓[22m[39m IntakeAndServices Component[2m > [22mshould display the correct content [33m352[2mms[22m[39m
(node:34316) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/upload-document/UploaderFileItem.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 127[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/balance/BalanceTab.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 327[2mms[22m[39m
   [33m[2m✓[22m[39m BalanceTab[2m > [22mshould render components correctly [33m325[2mms[22m[39m
 [32m✓[39m test/shared/common/components/procedure-code-search/ProcedureCodeSearch.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 148[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-intake/claim-intake-section/ClaimIntakeSectionSlot.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 23176[2mms[22m[39m
   [33m[2m✓[22m[39m ClaimIntakeSectionSlot[2m > [22mshould export the component correctly [33m23174[2mms[22m[39m
 [32m✓[39m test/shared/common/components/select-patient-card/SelectPatientCard.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 88[2mms[22m[39m
 [32m✓[39m test/shared/common/components/contact-cards/ContactCardPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 65[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/adjudication-tab/adjudication-results-table/ExpandedAdjudicationRow.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 61[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/comments-and-remarks/CommentsAndRemarks.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 91[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/additional-claim-info/AdditionalClaimInfo.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 238[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/AddMissingTeeth.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 260[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/CardRow.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 34[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/SelectMissingTeethInfo.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 43[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/Card.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 54[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/CardWrapper.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 21[2mms[22m[39m

[31m⎯⎯⎯⎯⎯⎯⎯[1m[7m Failed Tests 1 [27m[22m⎯⎯⎯⎯⎯⎯⎯[39m

[31m[1m[7m FAIL [27m[22m[39m test/pages/dental-overview-page/components/financials-tab/payments/PaymentsTable.test.tsx[2m > [22mPaymentsTable[2m > [22mRender expanded row amounts correctly
[31m[1mError[22m: Objects are not valid as a React child (found: object with keys {expandName, amount}). If you meant to render a collection of children, use an array instead.
    in label (at PaymentTableExpandRow.tsx:38)
    in div (at PaymentTableExpandRow.tsx:37)
    in div (at PaymentTableExpandRow.tsx:36)
    in wrappedComponent (at PaymentsTable.tsx:159)
    in div (at PaymentsTable.tsx:150)
    in td (created by TableCell)
    in TableCell (created by TableRow)
    in tr (created by TableRow)
    in TableRow (created by Connect(TableRow))
    in Connect(TableRow) (created by BaseTable)
    in tbody (created by BaseTable)
    in table (created by BaseTable)
    in BaseTable (created by Connect(BaseTable))
    in Connect(BaseTable) (created by BodyTable)
    in div (created by BodyTable)
    in BodyTable (created by ExpandableTable)
    in div (created by ExpandableTable)
    in div (created by ExpandableTable)
    in div (created by ExpandableTable)
    in ExpandableTable (created by Connect(ExpandableTable))
    in Connect(ExpandableTable) (created by Table)
    in Provider (created by Table)
    in Table (created by LocaleReceiver)
    in LocaleReceiver (created by Context.Consumer)
    in div (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Spin (created by Context.Consumer)
    in div (created by Context.Consumer)
    in Table (created by ForwardRef(TableComponent))
    in ForwardRef(TableComponent) (at PaymentsTable.tsx:169)
    in div (at PaymentsTable.tsx:168)
    in wrappedComponent (at PaymentsTable.test.tsx:281)[39m
[90m [2m❯[22m throwOnInvalidObjectType ../../node_modules/react-dom/cjs/react-dom.development.js:[2m13413:15[22m[39m
[90m [2m❯[22m reconcileChildFibers ../../node_modules/react-dom/cjs/react-dom.development.js:[2m14313:7[22m[39m
[90m [2m❯[22m reconcileChildren ../../node_modules/react-dom/cjs/react-dom.development.js:[2m16762:28[22m[39m
[90m [2m❯[22m updateHostComponent ../../node_modules/react-dom/cjs/react-dom.development.js:[2m17302:3[22m[39m
[90m [2m❯[22m beginWork ../../node_modules/react-dom/cjs/react-dom.development.js:[2m18627:14[22m[39m
[90m [2m❯[22m HTMLUnknownElement.callCallback ../../node_modules/react-dom/cjs/react-dom.development.js:[2m188:14[22m[39m
[90m [2m❯[22m HTMLUnknownElement.callTheUserObjectsOperation ../../node_modules/jsdom/lib/jsdom/living/generated/EventListener.js:[2m26:30[22m[39m
[90m [2m❯[22m innerInvokeEventListeners ../../node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:[2m338:25[22m[39m
[90m [2m❯[22m invokeEventListeners ../../node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:[2m274:3[22m[39m
[90m [2m❯[22m HTMLUnknownElementImpl._dispatch ../../node_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js:[2m221:9[22m[39m

[31m[2m⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/1]⎯[22m[39m

[2m Test Files [22m [1m[31m1 failed[39m[22m[2m | [22m[1m[32m55 passed[39m[22m[90m (56)[39m
[2m      Tests [22m [1m[31m1 failed[39m[22m[2m | [22m[1m[32m332 passed[39m[22m[90m (333)[39m
[2m   Start at [22m 16:06:51
[2m   Duration [22m 172.99s[2m (transform 17.07s, setup 194.15s, collect 2086.10s, tests 39.15s, environment 86.55s, prepare 11.73s)[22m

error Command failed with exit code 1.
info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.
