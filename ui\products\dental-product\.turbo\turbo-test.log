$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use <PERSON>ite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/products/dental-product[39m

(node:59928) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:63716) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:21976) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:66864) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:55976) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:61880) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:42604) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:85468) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:55088) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:860) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:33736) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:76564) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:28824) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:55024) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:18672) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/utils.test.ts [2m([22m[2m17 tests[22m[2m)[22m[90m 24[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/sub-stores/EditWizardStore.test.tsx [2m([22m[2m21 tests[22m[2m)[22m[90m 12[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/store/accumulatorTypeUtils.test.tsx [2m([22m[2m14 tests[22m[2m)[22m[90m 25[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/DentalEditPageRootStore.test.tsx [2m([22m[2m17 tests[22m[2m)[22m[90m 44[2mms[22m[39m
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/WithinFormEditWrapperStore.test.tsx [2m([22m[2m21 tests[22m[2m)[22m[90m 78[2mms[22m[39m
 [32m✓[39m test/pages/dental-entry-page/components/dental-claims-table/DentalClaimsTable.test.tsx [2m([22m[2m13 tests[22m[2m)[22m[90m 44[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/store/PolicyAndPatientStore.test.ts [2m([22m[2m11 tests[22m[2m)[22m[90m 32[2mms[22m[39m
(node:88352) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:59736) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:28508) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:66760) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:87612) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:31324) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:49396) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-entry-page/DentalEntryPage.test.tsx [2m([22m[2m20 tests[22m[2m)[22m[33m 535[2mms[22m[39m
(node:30572) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-edit-page/DentalEditPage.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[90m 56[2mms[22m[39m
 [32m✓[39m test/pages/dental-adjust-page-deprecated/store/DentalAdjustPageStore.test.tsx [2m([22m[2m20 tests[22m[2m)[22m[90m 40[2mms[22m[39m
 [32m✓[39m test/pages/dental-entry-page/store/DentalEntryPageStore.test.tsx [2m([22m[2m15 tests[22m[2m)[22m[90m 34[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/store/DentalOverviewPageStore.test.tsx [2m([22m[2m22 tests[22m[2m)[22m[90m 54[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/payments/PaymentsTable.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[33m 465[2mms[22m[39m
(node:39304) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/paymentServiceUtils.test.ts [2m([22m[2m7 tests[22m[2m)[22m[90m 19[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-procedures/utils.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[90m 110[2mms[22m[39m
(node:26172) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:66544) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:18568) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:30836) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:85020) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:59740) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-edit-page/store/UploadDocumentsStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 14[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-provider-search/store/ProviderStore.test.ts [2m([22m[2m6 tests[22m[2m)[22m[90m 26[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/header/PaymentActionsDropdown.test.tsx [2m([22m[2m5 tests[22m[2m)[22m[33m 544[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/adjudication-tab/adjudication-results-table/AdjudicationResultsTable.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 226[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/utils.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 16[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/claim-info-tab/ClaimInfoTab.test.tsx [2m([22m[2m9 tests[22m[2m)[22m[33m 921[2mms[22m[39m
 [32m✓[39m test/utils/common/ValidationUtils.test.ts [2m([22m[2m5 tests[22m[2m)[22m[90m 8[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/accumulator-tab/AccumutatorTab.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 383[2mms[22m[39m
   [33m[2m✓[22m[39m AccumulatorTab[2m > [22mBasic Rendering[2m > [22mshould render card with correct title and policy date field [33m381[2mms[22m[39m
(node:34508) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:35664) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:48472) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:82168) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-edit-page/store/within-form-wrapper-store/sub-stores/EditWizardRulesExecutionStore.test.tsx [2m([22m[2m6 tests[22m[2m)[22m[90m 10[2mms[22m[39m
(node:59388) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:26256) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:69420) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:63888) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:86856) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-edit-page/utils/Utils.test.tsx [2m([22m[2m7 tests[22m[2m)[22m[90m 8[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-policy-and-patient/ClaimPolicyAndPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 174[2mms[22m[39m
 [32m✓[39m test/shared/common/upload-document/utils.test.ts [2m([22m[2m4 tests[22m[2m)[22m[90m 6[2mms[22m[39m
(node:69484) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:79760) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:33180) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/pages/dental-overview-page/components/financials-tab/header/AuthorityApproval.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[90m 62[2mms[22m[39m
(node:69048) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/review-claim-info/PolicyAndPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 69[2mms[22m[39m
(node:59756) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/claim-provider-search/ClaimProviderSearch.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 74[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-procedures/claim-procedures-view-items/ClaimProceduresViewItems.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 375[2mms[22m[39m
(node:76576) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:70236) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/claim-provider-search/claim-provider-search-with-field/ClaimProviderSearchWithField.test.tsx [2m([22m[2m4 tests[22m[2m)[22m[33m 480[2mms[22m[39m
(node:59076) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/upload-document/UploadDocuments.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 158[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/AdditionalInformation.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 113[2mms[22m[39m
 [32m✓[39m test/shared/common/upload-document/UploaderFileItem.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 95[2mms[22m[39m
(node:64160) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/review-claim-info/ProviderDetail.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 98[2mms[22m[39m
(node:67888) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:65780) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/SelectMissingTeethSelect.test.tsx [2m([22m[2m3 tests[22m[2m)[22m[33m 312[2mms[22m[39m
 [32m✓[39m test/shared/common/components/procedure-code-search/ProcedureCodeSearch.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 85[2mms[22m[39m
 [32m✓[39m test/shared/common/review-claim-info/IntakeAndServices.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 189[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-intake/claim-intake-section/ClaimIntakeSectionSlot.test.tsx [2m([22m[2m1 test[22m[2m)[22m[33m 24494[2mms[22m[39m
   [33m[2m✓[22m[39m ClaimIntakeSectionSlot[2m > [22mshould export the component correctly [33m24492[2mms[22m[39m
 [32m✓[39m test/shared/common/components/select-patient-card/SelectPatientCard.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 60[2mms[22m[39m
 [32m✓[39m test/shared/common/components/contact-cards/ContactCardPatient.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 42[2mms[22m[39m
 [32m✓[39m test/pages/dental-overview-page/components/adjudication-tab/adjudication-results-table/ExpandedAdjudicationRow.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 35[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/AddMissingTeeth.test.tsx [2m([22m[2m2 tests[22m[2m)[22m[90m 149[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/additional-claim-info/AdditionalClaimInfo.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 131[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/CardRow.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 27[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/comments-and-remarks/CommentsAndRemarks.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 41[2mms[22m[39m
 [32m✓[39m test/shared/common/claim-claim-details/missing-teeth/SelectMissingTeethInfo.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 21[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/Card.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 23[2mms[22m[39m
 [32m✓[39m test/shared/common/components/cards/CardWrapper.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 14[2mms[22m[39m

[2m Test Files [22m [1m[32m50 passed[39m[22m[90m (50)[39m
[2m      Tests [22m [1m[32m307 passed[39m[22m[90m (307)[39m
[2m   Start at [22m 09:32:12
[2m   Duration [22m 80.26s[2m (transform 13.12s, setup 170.59s, collect 796.84s, tests 31.05s, environment 75.77s, prepare 10.36s)[22m

