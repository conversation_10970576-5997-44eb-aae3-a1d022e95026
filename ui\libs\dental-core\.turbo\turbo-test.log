$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use Vite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/libs/dental-core[39m

(node:119092) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:9284) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:42032) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/money-format/MoneyFormat.test.tsx [2m([22m[2m11 tests[22m[2m)[22m[33m 690[2mms[22m[39m
   [33m[2m✓[22m[39m MoneyFormat[2m > [22mrenders 0 correctly [33m539[2mms[22m[39m
 [32m✓[39m test/components/claim-toggle/ClaimToggle.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 95[2mms[22m[39m
 [32m✓[39m test/components/multi-steps/index.test.tsx [2m([22m[2m10 tests[22m[2m)[22m[33m 650[2mms[22m[39m

[2m Test Files [22m [1m[32m3 passed[39m[22m[90m (3)[39m
[2m      Tests [22m [1m[32m22 passed[39m[22m[90m (22)[39m
[2m   Start at [22m 10:25:09
[2m   Duration [22m 125.46s[2m (transform 3.51s, setup 191.52s, collect 86.12s, tests 1.43s, environment 48.29s, prepare 2.03s)[22m

