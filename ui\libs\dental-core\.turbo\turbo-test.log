$ vitest run --passWithNoTests
[33m[7m[33m Vitest [33m[27m "cache.dir" is deprecated, use Vite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"[39m

[1m[7m[36m RUN [39m[27m[22m [36mv2.1.9 [39m[90mD:/ProgramData/ms-claim-benefits-dental/ui/libs/dental-core[39m

(node:84020) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:32304) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
 [32m✓[39m test/components/claim-toggle/ClaimToggle.test.tsx [2m([22m[2m1 test[22m[2m)[22m[90m 34[2mms[22m[39m
 [32m✓[39m test/components/multi-steps/index.test.tsx [2m([22m[2m10 tests[22m[2m)[22m[90m 281[2mms[22m[39m

[2m Test Files [22m [1m[32m2 passed[39m[22m[90m (2)[39m
[2m      Tests [22m [1m[32m11 passed[39m[22m[90m (11)[39m
[2m   Start at [22m 09:31:47
[2m   Duration [22m 23.66s[2m (transform 547ms, setup 13.03s, collect 24.39s, tests 315ms, environment 7.30s, prepare 410ms)[22m

