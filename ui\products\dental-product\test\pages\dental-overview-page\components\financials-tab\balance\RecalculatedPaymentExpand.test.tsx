import React from 'react'
import {render, screen} from '@testing-library/react'
import {describe, expect, it, vi} from 'vitest'

import {CapDentalBalance, CapDentalSettlement} from '@eisgroup/dental-models'

import {RecalculatedPaymentExpand} from '../../../../../../src/pages/dental-overview-page/components/financials-tab/balance/RecalculatedPaymentExpand'

vi.mock('@eisgroup/form', async importOriginal => ({
    ...(await importOriginal()),
    useLookupValues: () => ({
        values: [{code: 'D1234', displayValue: 'Test Procedure'}]
    })
}))

describe('RecalculatedPaymentExpand', () => {
    const mockBalanceItem = {
        actualAllocations: [
            {
                _key: {rootId: '1'},
                allocationSource: {
                    _uri: 'gentity://CapSettlement/CapDentalSettlement//7461f7d7-fa85-423b-9adc-f0f89f823eb3/1'
                },
                allocationPayableItem: {
                    procedureID: 'PROC1'
                },
                allocationGrossAmount: {
                    amount: 100
                },
                scheduledAllocations: [
                    {
                        allocationGrossAmount: {
                            amount: 50
                        }
                    }
                ]
            }
        ]
    } as CapDentalBalance.CapDentalBalanceItemEntity

    const mockSettlements = [
        {
            _key: {rootId: '7461f7d7-fa85-423b-9adc-f0f89f823eb3', revisionNo: '1'},
            settlementLossInfo: {
                submittedProcedures: [
                    {
                        _key: {id: 'PROC1'},
                        dateOfService: new Date('2025-08-07')
                    }
                ]
            },
            settlementResult: {
                entries: [
                    {
                        serviceSource: 'PROC1',
                        status: {
                            submittedCode: 'D1234'
                        }
                    }
                ]
            }
        }
    ] as unknown as CapDentalSettlement.CapDentalSettlementEntity[]

    it('should render service detail with correct format', () => {
        render(<RecalculatedPaymentExpand balanceItem={mockBalanceItem} settlements={mockSettlements} />)

        expect(screen.getByText('D1234-Test Procedure(08/07/2025)')).toBeInTheDocument()
    })

    it('should display correct payment and allocation amounts', () => {
        render(<RecalculatedPaymentExpand balanceItem={mockBalanceItem} settlements={mockSettlements} />)

        expect(screen.getByText('$100.00')).toBeInTheDocument()
    })

    it('should render all required labels', () => {
        render(<RecalculatedPaymentExpand balanceItem={mockBalanceItem} settlements={mockSettlements} />)

        const allocationLabels = screen.getAllByText(
            'dental-product:dental-claim-balance-balance-recalculated-payments-total-allocation'
        )
        expect(allocationLabels).toHaveLength(2)

        const interestLabels = screen.getAllByText(
            'dental-product:dental-claim-balance-balance-recalculated-payments-total-interests'
        )
        expect(interestLabels).toHaveLength(2)
    })

    it('should handle missing procedure code or description', () => {
        const invalidBalanceItem = {
            actualAllocations: [
                {
                    _key: {rootId: '1'},
                    allocationSource: {
                        _uri: 'gentity://CapSettlement/CapDentalSettlement//999/1'
                    },
                    allocationPayableItem: {
                        procedureID: 'INVALID_PROC'
                    },
                    allocationGrossAmount: {
                        amount: 100
                    },
                    scheduledAllocations: []
                }
            ]
        } as unknown as CapDentalBalance.CapDentalBalanceItemEntity

        render(<RecalculatedPaymentExpand balanceItem={invalidBalanceItem} settlements={mockSettlements} />)

        expect(screen.getByText('-()')).toBeInTheDocument()
    })
})
