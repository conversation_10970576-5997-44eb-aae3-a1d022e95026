/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.common.command.config.CapBaseCommandConfig;
import com.eisgroup.genesis.cap.ref.command.capdentalbalancechangelog.CapDentalBalanceChangeLogInitHandler;
import com.eisgroup.genesis.cap.transformation.command.config.CapTransformationCommandsConfig;
import com.eisgroup.genesis.lifecycle.CommandHandler;
import com.eisgroup.genesis.lifecycle.LifecycleModule;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;

/**
 * Lifecycle configuration for {@link com.eisgroup.genesis.factory.modeling.types.CapBalanceChangeLog}
 */
public class CapDentalBalanceChangeLogLifecycleModule implements LifecycleModule {

    @Nonnull
    @Override
    public Collection<CommandHandler<?, ?>> getCommands() {
        return List.of(
                new CapDentalBalanceChangeLogInitHandler()
        );
    }

    @Override
    public String getModelType() {
        return "CapBalanceChangeLog";
    }

    @Override
    public String getModelName() {
        return "CapDentalBalanceChangeLog";
    }

    @Override
    public Object[] getConfigResources() {
        return new Object[]{
                CapTransformationCommandsConfig.class,
                CapBaseCommandConfig.class
        };
    }
}
