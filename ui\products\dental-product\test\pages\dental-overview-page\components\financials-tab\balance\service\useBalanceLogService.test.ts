import {Observable} from 'rxjs'
import {beforeEach, describe, expect, it, vi} from 'vitest'

import {of, Right} from '@eisgroup/data.either'
import {DentalBalanceChangeLogEntitySearchResult, dentalBalanceService} from '@eisgroup/dental-product-services'

import {
    loadAllBalanceLogs,
    loadBalanceLogFunction
} from '../../../../../../../src/pages/dental-overview-page/components/financials-tab/balance/service'

const {searchBalanceLog} = vi.hoisted(() => {
    return {
        searchBalanceLog: vi.fn()
    }
})
vi.mock('@eisgroup/dental-product-services', () => ({
    dentalBalanceService: {
        searchBalanceLog: searchBalanceLog
    }
}))

describe('Balance Log Service', () => {
    const mockLossSource = 'TEST_SOURCE'

    beforeEach(() => {
        vi.clearAllMocks()
    })

    describe('loadBalanceLogFunction', () => {
        it('should call searchBalanceLog with correct parameters', async () => {
            const mockOffset = 0
            const currentResult = {
                result: [],
                count: 0
            }
            searchBalanceLog.mockImplementation(() => Observable.of(Right(currentResult)))

            await loadBalanceLogFunction(mockLossSource, mockOffset)

            expect(dentalBalanceService.searchBalanceLog).toHaveBeenCalledWith({
                limit: 100,
                offset: mockOffset,
                query: {
                    originSource: {
                        matches: [mockLossSource]
                    }
                },
                sort: {creationDate: 'DESC'}
            })
        })
    })

    describe('loadAllBalanceLogs', () => {
        it('should return single page results when total count is less than page size', async () => {
            const mockResult = [
                {
                    id: '1',
                    creationDate: '2025-08-06'
                }
            ]

            searchBalanceLog.mockImplementation(() =>
                Observable.of(
                    Right({result: mockResult, count: 1} as unknown as DentalBalanceChangeLogEntitySearchResult)
                )
            )

            const result = await loadAllBalanceLogs(mockLossSource)

            expect(result).toEqual(mockResult)
            expect(dentalBalanceService.searchBalanceLog).toHaveBeenCalledTimes(1)
        })

        it('should handle multiple pages and sort results by creation date', async () => {
            const mockResult1 = [
                {
                    id: '1',
                    creationDate: '2025-08-06'
                }
            ]
            const mockResult2 = [
                {
                    id: '2',
                    creationDate: '2025-08-05'
                }
            ]

            searchBalanceLog
                .mockImplementationOnce(() =>
                    Observable.of(
                        Right({result: mockResult1, count: 150} as unknown as DentalBalanceChangeLogEntitySearchResult)
                    )
                )
                .mockImplementationOnce(() =>
                    Observable.of(
                        Right({result: mockResult2, count: 150} as unknown as DentalBalanceChangeLogEntitySearchResult)
                    )
                )

            const result = await loadAllBalanceLogs(mockLossSource)

            expect(result).toHaveLength(2)
            expect(result?.[0]).toEqual(mockResult2[0])
            expect(result?.[1]).toEqual(mockResult1[0])
            expect(dentalBalanceService.searchBalanceLog).toHaveBeenCalledTimes(2)
        })

        it('should handle empty results', async () => {
            searchBalanceLog.mockImplementationOnce(() => Observable.of(Right({result: [], count: 0})))

            const result = await loadAllBalanceLogs(mockLossSource)

            expect(result).toEqual([])
            expect(dentalBalanceService.searchBalanceLog).toHaveBeenCalledTimes(1)
        })

        it('should handle pagination correctly', async () => {
            const page1 = [{id: '1', creationDate: '2025-08-06'}]
            const page2 = [{id: '2', creationDate: '2025-08-05'}]
            const page3 = [{id: '3', creationDate: '2025-08-04'}]

            searchBalanceLog
                .mockImplementationOnce(() =>
                    Observable.of(
                        Right({result: page1, count: 250} as unknown as DentalBalanceChangeLogEntitySearchResult)
                    )
                )
                .mockImplementationOnce(() =>
                    Observable.of(
                        Right({result: page2, count: 250} as unknown as DentalBalanceChangeLogEntitySearchResult)
                    )
                )
                .mockImplementationOnce(() =>
                    Observable.of(
                        Right({result: page3, count: 250} as unknown as DentalBalanceChangeLogEntitySearchResult)
                    )
                )

            const result = await loadAllBalanceLogs(mockLossSource)

            expect(result).toHaveLength(3)
            expect(dentalBalanceService.searchBalanceLog).toHaveBeenCalledTimes(3)
            // Verify sorting
            expect(result?.[0]).toEqual(page3[0])
            expect(result?.[1]).toEqual(page2[0])
            expect(result?.[2]).toEqual(page1[0])
        })
    })
})
