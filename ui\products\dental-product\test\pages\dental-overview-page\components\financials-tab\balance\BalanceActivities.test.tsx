import React from 'react'
import {fireEvent, render, screen} from '@testing-library/react'
import {describe, expect, it, vi} from 'vitest'

import {CapDentalBalanceChangeLog} from '@eisgroup/dental-models'

import {BalanceActivities} from '../../../../../../src/pages/dental-overview-page/components/financials-tab/balance/BalanceActivities'

vi.mock('@eisgroup/react-components', () => ({
    LookupLabel: ({code, emptyLabel}: {code: string; emptyLabel: string}) => (
        <span data-testid='lookup-label'>{code || emptyLabel}</span>
    )
}))

describe('BalanceActivities', () => {
    const mockActivities = [
        {
            _key: {rootId: '1'},
            creationDate: new Date('2025-08-06'),
            balanceChangeLogDetails: {
                transactionNumber: 'TX001',
                transactionTypeCd: 'PAYMENT',
                totalBalanceAmount: {amount: 1000},
                description: 'First transaction'
            }
        },
        {
            _key: {rootId: '2'},
            creationDate: new Date('2025-08-05'),
            balanceChangeLogDetails: {
                transactionNumber: 'TX002',
                transactionTypeCd: 'ADJUSTMENT',
                totalBalanceAmount: {amount: 500},
                description: 'Second transaction'
            }
        }
    ] as CapDentalBalanceChangeLog.CapDentalBalanceChangeLogEntity[]

    it('should render collapse panel with correct title', () => {
        render(<BalanceActivities activities={[]} />)

        expect(screen.getByText('dental-product:dental-claim-balance-balance-activities-title')).toBeInTheDocument()
    })

    it('should render table with correct columns', () => {
        render(<BalanceActivities activities={mockActivities} />)

        expect(screen.getByText('dental-product:dental-claim-balance-balance-transaction-date')).toBeInTheDocument()
        expect(screen.getByText('dental-product:dental-claim-balance-balance-transaction-id')).toBeInTheDocument()
        expect(screen.getByText('dental-product:dental-claim-balance-balance-transaction-type')).toBeInTheDocument()
        expect(screen.getByText('dental-product:dental-claim-balance-balance-transaction-amount')).toBeInTheDocument()
        expect(
            screen.getByText('dental-product:dental-claim-balance-balance-transaction-description')
        ).toBeInTheDocument()
        expect(
            screen.getByText('dental-product:dental-claim-balance-balance-transaction-total-balance')
        ).toBeInTheDocument()
    })

    it('should filter data source correctly', () => {
        const {container} = render(<BalanceActivities activities={mockActivities} />)

        const rows = container.querySelectorAll('.ant-table-row')
        expect(rows).toHaveLength(2)
    })
})
