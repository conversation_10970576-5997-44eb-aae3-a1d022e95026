@CapEndpoint("balanceChangeLogGeneration")
@CapRawRequest
Transformation CapDentalBalanceChangeLogGeneration {
    Input {
        CapDentalBalanceChangeLog.CapDentalBalanceChangeLogGenerationInput as input
    }
    Output {
      CapDentalBalanceChangeLog.CapDentalBalanceChangeLogGenerationRulesOutput
    }

    Var payment is Ternary(Equals(input.activatePayload._modelName, "CapDentalPaymentDefinition"), input.activatePayload, Null())
    Var openlRequest is createOpenlRequest(input.balancePayload, input.balanceTransactionTypeCd, payment)
    Var response is ExecuteRules("claim-dental-financial", "_api_balance_change_log_generation", openlRequest)

    Attr balanceChangeLogs is response.balanceChangeLogs

    Producer createOpenlRequest(balance, balanceTransactionTypeCd, payment) {
        Attr transactionTypeCd is balanceTransactionTypeCd
        Attr payeeBalance is createPayeeBalance(balance)
        Attr payment is SafeInvoke(payment, payment)
    }

    Producer createPayeeBalance(balance) {
        Attr totalBalanceAmount is balance.totalBalanceAmount
        Attr originSource is balance.originSource
        Attr payee is balance.payee
    }
}