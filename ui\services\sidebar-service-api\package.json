{"name": "@eisgroup/sidebar-service-api", "version": "25.7.0-SNAPSHOT.202508120630", "description": "Sidebar service dxp-api interfaces", "main": "target/dist/js/src/index.js", "typings": "target/dist/definitions/src/index.d.ts", "files": ["target/dist/"], "dependencies": {"@eisgroup/common-types": "100.0.0", "@eisgroup/common": "100.0.0", "@eisgroup/ioc": "100.0.0", "@eisgroup/work-common": "25.10.0-SNAPSHOT.202507212214", "@eisgroup/bam-common": "25.10.0-SNAPSHOT.202507212214"}, "scripts": {"build": "tsc --project tsconfig.build.json", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "lint": "yarn eslint \"src/**/*.ts{,x}\""}, "publishConfig": {"registry": "https://genesis-npm-release.exigengroup.com/repository/genesis-npm-release/"}, "v20ModuleType": "reference implementation library"}