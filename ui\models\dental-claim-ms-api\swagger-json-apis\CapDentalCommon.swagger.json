{"swagger": "2.0", "info": {"description": "API for common", "version": "1", "title": "common model API facade"}, "basePath": "/", "schemes": ["http"], "paths": {"/api/common/common/v1/verifyDentalPaymentScheduleActivation": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "", "required": false, "schema": {"$ref": "#/definitions/VerifyDentalPaymentScheduleActivationInputsBody"}}], "responses": {"200": {"description": "", "schema": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleEntitySuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapDentalPaymentSchedule_CapDentalPaymentAllocationAccumulatorDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationAccumulatorDetailsEntity"}, "accumulatorAmount": {"$ref": "#/definitions/Money"}, "accumulatorType": {"type": "string", "description": "Accumulator type."}, "appliesToProcedureCategories": {"type": "array", "items": {"type": "string", "description": "Defines to which procedure category(s) maximum/deductible applies to."}}, "appliesToProcedureCategory": {"type": "string", "description": "Defines to which procedure category accumulator applies to."}, "networkType": {"type": "string", "description": "Network type for accumulator."}, "renewalType": {"type": "string", "description": "Renewal type for accumulator."}, "term": {"$ref": "#/definitions/CapDentalPaymentSchedule_Term"}}, "title": "CapDentalPaymentSchedule CapDentalPaymentAllocationAccumulatorDetailsEntity", "description": "Allocation acumulator details."}, "CapDentalPaymentSchedule_CapDentalPaymentAllocationDentalDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationDentalDetailsEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentAllocationAccumulatorDetailsEntity"}}, "patient": {"$ref": "#/definitions/EntityLink"}, "transactionTypeCd": {"type": "string", "description": "Dental claim transaction type."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentAllocationDentalDetailsEntity", "description": "Dental allocation details."}, "CapDentalPaymentSchedule_CapDentalPaymentAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationEntity"}, "allocationDentalDetails": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentAllocationDentalDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business code."}, "allocationLossInfo": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentAllocationLossInfoEntity"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "reserveType": {"type": "string", "description": "Allocation reserve type."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentAllocationEntity", "description": "An object which extends payment allocations details."}, "CapDentalPaymentSchedule_CapDentalPaymentAllocationLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationLossInfoEntity"}, "lossSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapDentalPaymentSchedule CapDentalPaymentAllocationLossInfoEntity", "description": "Allocation claim details."}, "CapDentalPaymentSchedule_CapDentalPaymentAllocationPayableItemEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationPayableItemEntity"}, "claimSource": {"$ref": "#/definitions/EntityLink"}, "orthoMonth": {"type": "integer", "format": "int64", "description": "Month number for which allocation is paid."}, "procedureID": {"type": "string", "description": "Related Settlement Result entry's procedure ID."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentAllocationPayableItemEntity", "description": "Dental allocation payable item details."}, "CapDentalPaymentSchedule_CapDentalPaymentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentDetailsEntity"}, "payeeDetails": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentPayeeDetailsEntity"}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentAllocationEntity"}}, "paymentDate": {"type": "string", "format": "date-time", "description": "The payment post date."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentDetailsEntity", "description": "An object which extends payment details."}, "CapDentalPaymentSchedule_CapDentalPaymentPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentPayeeDetailsEntity"}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapDentalPaymentSchedule CapDentalPaymentPayeeDetailsEntity", "description": "An object which extends payment payee details."}, "CapDentalPaymentSchedule_CapDentalPaymentScheduleActivationResult": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentScheduleActivationResult"}, "activationMessages": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleMessageEntity"}}, "activationStatus": {"type": "string"}}, "title": "CapDentalPaymentSchedule CapDentalPaymentScheduleActivationResult"}, "CapDentalPaymentSchedule_CapDentalPaymentScheduleEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalPaymentSchedule"}, "_modelType": {"type": "string", "example": "CapPaymentSchedule"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalPaymentScheduleEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the schedule was created."}, "originSource": {"$ref": "#/definitions/EntityLink"}, "paymentScheduleActivationResult": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleActivationResult"}, "paymentTemplate": {"$ref": "#/definitions/EntityLink"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalScheduledPaymentEntity"}}, "scheduleMessages": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleMessageEntity"}}, "scheduleNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment schedule."}, "state": {"type": "string", "description": "State of a payment schedule lifecycle."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentScheduleEntity", "description": "The Root Entity of CAP Payment Schedule Domain."}, "CapDentalPaymentSchedule_CapDentalPaymentScheduleEntitySuccess": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleEntity"}}, "title": "CapDentalPaymentSchedule_CapDentalPaymentScheduleEntitySuccess"}, "CapDentalPaymentSchedule_CapDentalPaymentScheduleEntitySuccessBody": {"properties": {"body": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleEntitySuccess"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "CapDentalPaymentSchedule_CapDentalPaymentScheduleEntitySuccessBody"}, "CapDentalPaymentSchedule_CapDentalPaymentScheduleMessageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentScheduleMessageEntity"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentScheduleMessageEntity", "description": "Stores Payment Schedule messages"}, "CapDentalPaymentSchedule_CapDentalScheduledPaymentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalScheduledPaymentEntity"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the payment was created."}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_MessageType"}}, "paymentDetails": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentDetailsEntity"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment."}}, "title": "CapDentalPaymentSchedule CapDentalScheduledPaymentEntity", "description": "Defines payment transaction information."}, "CapDentalPaymentSchedule_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapDentalPaymentSchedule MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapDentalPaymentSchedule_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapDentalPaymentSchedule Term"}, "DentalInternal_CapUserCarryingEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapUserCarryingEntity"}, "userId": {"type": "string"}}, "title": "DentalInternal CapUserCarryingEntity"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}, "VerifyDentalPaymentScheduleActivationInputs": {"properties": {"schedule": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleEntity"}, "userRequest": {"$ref": "#/definitions/DentalInternal_CapUserCarryingEntity"}}, "title": "VerifyDentalPaymentScheduleActivationInputs"}, "VerifyDentalPaymentScheduleActivationInputsBody": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/VerifyDentalPaymentScheduleActivationInputs"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "VerifyDentalPaymentScheduleActivationInputsBody"}}}