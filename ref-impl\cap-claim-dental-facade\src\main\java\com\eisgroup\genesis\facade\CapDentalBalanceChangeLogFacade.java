/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.facade;

import com.eisgroup.genesis.cap.transformation.endpoint.ModelAwareTransformationEndpoint;
import com.eisgroup.genesis.facade.endpoint.command.CommandEndpoint;
import com.eisgroup.genesis.facade.endpoint.load.LoadEntityRestEndpoint;
import com.eisgroup.genesis.facade.endpoint.load.link.EntityLinkEndpoint;
import com.eisgroup.genesis.facade.endpoint.model.ModelFacade;
import com.eisgroup.genesis.facade.module.EndpointPackage;
import com.eisgroup.genesis.facade.module.FacadeModule;

import java.util.Arrays;
import java.util.Collection;

public class CapDentalBalanceChangeLogFacade implements FacadeModule {

    @Override
    public Collection<EndpointPackage> getEndpoints() {
        return Arrays.asList(
                new ModelFacade(),
                new CommandEndpoint(),
                new LoadEntityRestEndpoint<>(),
                new ModelAwareTransformationEndpoint(),
                new EntityLinkEndpoint()
        );
    }

    @Override
    public String getModelType() { return "CapBalanceChangeLog"; }

    @Override
    public String getModelName() {
        return "CapDentalBalanceChangeLog";
    }

    @Override
    public int getFacadeVersion() {
        return 1;
    }
}
