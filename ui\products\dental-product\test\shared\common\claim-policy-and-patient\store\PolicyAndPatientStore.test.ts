/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {runInAction} from 'mobx'
import {Observable} from 'rxjs/Observable'
import {describe, expect, it, vi} from 'vitest'

import {authentication} from '@eisgroup/auth'
import {customerCommonService, organizationCustomerService} from '@eisgroup/claim-dental-customer-service-api'
import {dentalPolicyService} from '@eisgroup/claim-dental-policy-service-api'
import {Right} from '@eisgroup/data.either'
import {dentalClaimService} from '@eisgroup/dental-product-services'
import {IoC} from '@eisgroup/ioc'

import {DentalPolicyAndPatientStoreImpl} from '../../../../../src/shared/common/claim-policy-and-patient/store/PolicyAndPatientStore'

vi.mock('@eisgroup/auth', () => ({
    authentication: {
        TYPES: {
            AuthenticationFacade: Symbol.for('AuthenticationFacade')
        }
    },
    authenticationFacade: {
        getService: vi.fn()
    }
}))

vi.mock('@eisgroup/environment', () => ({
    environment: {
        dxp: {
            claimDentalMsPath: 'http://localhost:8080/dental-ms',
            capDxpPath: 'http://localhost:8080/cap-dxp'
        }
    }
}))

vi.mock('@eisgroup/common', () => ({
    promiseToRxResult: vi.fn(fn => {
        return {
            first: vi.fn(() => ({
                subscribe: vi.fn((observer: any) => {
                    try {
                        Promise.resolve(fn({})).then(
                            result => {
                                observer(Right(result))
                            },
                            error => {
                                observer({
                                    fold: (errorFn: any, successFn: any) => errorFn(error)
                                })
                            }
                        )
                    } catch (error) {
                        observer({
                            fold: (errorFn: any, successFn: any) => errorFn(error)
                        })
                    }
                    return {unsubscribe: vi.fn()}
                })
            }))
        }
    })
}))

beforeAll(async () => {
    try {
        const facade = IoC.get<{init: (s: any) => void; isInitialized?: () => boolean}>(
            authentication.TYPES.AuthenticationFacade
        )
        if (!facade.isInitialized || !facade.isInitialized()) {
            type AuthService = Partial<authentication.AuthenticationService>
            const authService: AuthService = {
                encodeRequest: vi.fn().mockImplementation(item => Observable.of(Right(item))),
                hasAuthorities: vi.fn(() => true)
            }
            facade.init(authService)
        }
    } catch (e) {
        console.error(e)
    }
})

vi.mock('@eisgroup/claim-dental-customer-service-api', () => ({
    customerCommonService: () => ({
        searchCustomers: vi.fn().mockResolvedValue([])
    }),
    organizationCustomerService: () => ({
        loadOrganizationCustomer: vi.fn().mockResolvedValue([])
    })
}))

vi.mock('@eisgroup/claim-dental-policy-service-api', () => {
    return {
        dentalPolicyService: () => ({
            searchDentalPolicy: vi.fn().mockResolvedValue([]),
            loadDentalPolicy: vi.fn().mockResolvedValue([]),
            loadDentalMasterPolicy: vi.fn().mockResolvedValue([]),
            searchDentalMasterPolicyList: vi.fn().mockResolvedValue([]),
            searchPolicyWithRootId: vi.fn().mockResolvedValue([])
        })
    }
})

vi.mock('@eisgroup/dental-product-services', () => {
    return {
        dentalClaimService: () => ({
            commonSearchClaimPolicy: vi.fn().mockResolvedValue([])
        })
    }
})

describe('PolicyAndPatientStore', () => {
    let dentalPolicyAndPatientStore: DentalPolicyAndPatientStoreImpl
    let dentalPolicyServiceMock: any
    let customerServiceMock: any
    let mockDentalClaimService = {
        commonSearchClaimPolicy: vi.fn().mockResolvedValue('')
    }
    beforeEach(() => {
        dentalPolicyAndPatientStore = new DentalPolicyAndPatientStoreImpl({
            getState: vi.fn().mockReturnValue({
                values: {
                    entity: {
                        policyId: 'testPolicyId'
                    }
                }
            }),
            changes: vi.fn(),
            batch: vi.fn()
        } as any)
    })

    it('should setEditMode', () => {
        runInAction(() => {
            dentalPolicyAndPatientStore.setEditMode(true)
        })

        expect(dentalPolicyAndPatientStore.isEditMode).toBe(true)
    })

    it('should setPolicyEditable', () => {
        runInAction(() => {
            dentalPolicyAndPatientStore.setPolicyEditable(true)
        })

        expect(dentalPolicyAndPatientStore.policyEditable).toBe(true)
    })

    it('should removePolicy', () => {
        runInAction(() => {
            dentalPolicyAndPatientStore.removePolicy()
        })

        expect(dentalPolicyAndPatientStore.policy).toBe(null)
        expect(dentalPolicyAndPatientStore.policyWithInsured).toEqual({})
        expect(dentalPolicyAndPatientStore.dentalMasterPolicy).toEqual({})
        expect(dentalPolicyAndPatientStore.groupSponsor).toEqual({})
        expect(dentalPolicyAndPatientStore.insureds).toEqual([])
    })

    it('should setCurrentPolicy', () => {
        runInAction(() => {
            dentalPolicyAndPatientStore.setCurrentPolicy({
                testPolicyId: 'testPolicyId'
            })
        })

        expect(dentalPolicyAndPatientStore.policy).toEqual({
            testPolicyId: 'testPolicyId'
        })
    })

    it('should setPolicyList', () => {
        runInAction(() => {
            dentalPolicyAndPatientStore.setPolicyList([])
        })

        expect(dentalPolicyAndPatientStore.policyList).toEqual([])
    })

    it('should call searchPolicy with no customeNumber', () => {
        const dentalServicerInner = dentalPolicyService()
        dentalPolicyServiceMock = vi.spyOn(dentalServicerInner, 'searchDentalPolicy').mockResolvedValue([])
        Object.assign(dentalClaimService, mockDentalClaimService)
        dentalPolicyAndPatientStore.searchPolicy('test')
        expect(dentalPolicyAndPatientStore.policyList).toEqual([])
    })

    it('should call searchDentalMasterPolicyList', async () => {
        const dentalServicerInner = dentalPolicyService()
        dentalPolicyServiceMock = vi.spyOn(dentalServicerInner, 'searchDentalMasterPolicyList').mockResolvedValue([])
        await dentalPolicyAndPatientStore.searchDentalMasterPolicyList('test')
        expect(dentalPolicyAndPatientStore.masterPolicyList).toEqual([])
    })

    it('should call searchDentalMasterPolicy', async () => {
        dentalPolicyAndPatientStore.policyWithInsured = {
            masterLink: {
                source: {
                    _uri: 'gentity://Customer/INDIVIDUALCUSTOMER//rootId/1'
                }
            }
        } as any
        const dentalServicerInner = dentalPolicyService()
        dentalPolicyServiceMock = vi.spyOn(dentalServicerInner, 'loadDentalMasterPolicy').mockResolvedValue([] as any)
        await dentalPolicyAndPatientStore.searchDentalMasterPolicy()
        expect(dentalPolicyAndPatientStore.dentalMasterPolicy).toEqual([])
    })

    it('should call searchGroupSponsor', async () => {
        dentalPolicyAndPatientStore.dentalMasterPolicy = {
            customer: {
                _uri: 'gentity://Customer/INDIVIDUALCUSTOMER//rootId/1'
            }
        } as any
        const organziationCustomerInner = organizationCustomerService()
        customerServiceMock = vi
            .spyOn(organziationCustomerInner, 'loadOrganizationCustomer')
            .mockResolvedValue({} as any)
        await dentalPolicyAndPatientStore.searchGroupSponsor()
        expect(dentalPolicyAndPatientStore.groupSponsor).toEqual([])
    })

    it('should call loadDentalPolicyWithInsureds', async () => {
        dentalPolicyAndPatientStore.policy = {
            rootId: 'testRootId'
        } as any
        const dentalServicerInner = dentalPolicyService()
        dentalPolicyServiceMock = vi.spyOn(dentalServicerInner, 'loadDentalPolicy').mockResolvedValue({} as any)
        await dentalPolicyAndPatientStore.loadDentalPolicyWithInsureds(dentalPolicyAndPatientStore.policy?.rootId!)
        expect(dentalPolicyAndPatientStore.policyWithInsured).toEqual([])
        expect(dentalPolicyAndPatientStore.isEditMode).toBe(false)
    })

    it('should call loadInsureds', () => {
        dentalPolicyAndPatientStore.policyWithInsured = {
            insureds: []
        } as any
        const customerServiceInner = customerCommonService()
        customerServiceMock = vi.spyOn(customerServiceInner, 'searchCustomers').mockResolvedValue({} as any)
        dentalPolicyAndPatientStore.loadInsureds()
        expect(dentalPolicyAndPatientStore.insureds).toEqual([])
    })
})
