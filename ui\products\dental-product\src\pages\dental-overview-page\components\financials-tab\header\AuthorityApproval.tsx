/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {useState} from 'react'
import {observer} from 'mobx-react'

import {useForm, useFormState} from '@eisgroup/form'
import {useTranslate} from '@eisgroup/i18n'
import {AntPopconfirm, Button, notification} from '@eisgroup/ui-kit'
import {ValidationMediumError, ValidationMediumWarning} from '@eisgroup/ui-kit-icons'

import {hasAuthorities, Privileges} from '../../../../../utils'
import {PaymentScheduledState, PaymentSelectActions} from '../contants'
import {activatePaymentSchedule, checkPaymentSchedule, getPayments} from '../paymentServiceUtils'
import {formatLossSource, formatPaymentTableData} from '../utils'

export const AuthorityApproval: React.FC = observer(props => {
    const {t} = useTranslate()
    const form = useForm()
    const {entity} = useFormState().values

    const [isAuthoritative, setIsAuthoritative] = useState<boolean>(false)
    const [isPopconfirmVisible, setIsPopconfirmVisible] = useState<boolean>(false)

    const {paymentSchedules} = useFormState().values
    const paymentSchedule = paymentSchedules?.length ? paymentSchedules?.[0] : null
    const paymentScheduleState = paymentSchedule?.state

    const activatePaymentButtonLabel = t('dental-product:dental-claim-financial-actions-activate-payment-btn')

    const handlePopconfirmCancel = () => {
        setIsPopconfirmVisible(false)
    }

    const actionPaymentScheduleEither = async (result, actionType: string) => {
        const dentalLossSource = formatLossSource(entity)
        const payments = await getPayments(dentalLossSource)
        const filteredPaymentSchedules = result?.state !== PaymentScheduledState.Canceled ? [result] : []
        const paymentTableData = formatPaymentTableData(payments.result || [], filteredPaymentSchedules)
        form.change('paymentList', paymentTableData)
        form.change('paymentSchedules', filteredPaymentSchedules)
        notification.success({
            message: t(`dental-product:dental-claim-financial-payments-action-${actionType}-payments-successful-msg`)
        })
    }

    const handlePopconfirmConfirm = async () => {
        setIsPopconfirmVisible(false)
        const activateResult = await activatePaymentSchedule(paymentSchedule._key)
        actionPaymentScheduleEither(activateResult, PaymentSelectActions.AUTHORITY_APPROVAL)
    }

    const checkPaymentScheduleEither = checkResult => {
        if (checkResult.paymentScheduleActivationResult.activationStatus === 'Activate') {
            setIsAuthoritative(true)
        } else {
            setIsAuthoritative(false)
        }
        setIsPopconfirmVisible(true)
    }

    const onAuthorityApprovalClick = async (e: React.MouseEvent) => {
        e.stopPropagation()
        // check if the payment schedule is activated
        const checkResult = await checkPaymentSchedule(paymentSchedule)
        checkPaymentScheduleEither(checkResult)
    }

    return hasAuthorities([Privileges.FINANCIAL_ACTIVATE_PAYMENT_SCHEDULE]) &&
        paymentScheduleState === PaymentScheduledState.Open ? (
        <div>
            <AntPopconfirm
                visible={isPopconfirmVisible}
                okButtonProps={{style: {display: isAuthoritative ? 'unset' : 'none'}}}
                title={
                    isAuthoritative
                        ? t(
                              `dental-product:dental-claim-financial-actions-${PaymentSelectActions.AUTHORITY_APPROVAL}-popconfirm-msg`
                          )
                        : t(
                              'dental-product:dental-claim-financial-actions-do-not-have-authorize-activate-payment-popconfirm-msg'
                          )
                }
                okText={t(
                    `dental-product:dental-claim-financial-popconfirm-${PaymentSelectActions.AUTHORITY_APPROVAL}`
                )}
                cancelText={isAuthoritative ? t('dental-product:cancel') : t('dental-product:close')}
                disabled={false}
                onCancel={handlePopconfirmCancel}
                onConfirm={handlePopconfirmConfirm}
                trigger='click'
                destroyTooltipOnHide
                icon={!isAuthoritative ? <ValidationMediumError style={{color: 'red'}} /> : <ValidationMediumWarning />}
            >
                <Button type='secondary' size='medium' onClick={onAuthorityApprovalClick}>
                    {activatePaymentButtonLabel}
                </Button>
            </AntPopconfirm>
        </div>
    ) : null
})
