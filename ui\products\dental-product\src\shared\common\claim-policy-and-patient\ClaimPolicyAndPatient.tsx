/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {useCallback, useEffect, useMemo, useState} from 'react'
import {get} from 'lodash'
import {toJS} from 'mobx'
import {observer} from 'mobx-react'

import {AutocompletableSearch} from '@eisgroup/dental-core'
import {CapDentalLoss} from '@eisgroup/dental-models'
import {DentalPolicySummary} from '@eisgroup/dental-product-services'
import {useForm, useFormState} from '@eisgroup/form-core'
import {t} from '@eisgroup/i18n'
import {Col, Row} from '@eisgroup/ui-kit'

import {CLAIM_MODE} from '../../../pages/dental-edit-page/constants'
import {useContextRefStores} from '../../../pages/dental-edit-page/hooks'
import {useIntakeRootStore} from '../../../pages/dental-edit-page/store'
import {getParamFromURL} from '../../../utils/helpers'
import {ContactCardPatient, RelativeSpinner, SelectPatientCard, Validation} from '../../components'
import {CLAIM_INFO_SECTION_TITLE} from '../claim-info/classnames'
import {
    CLAIM_POLICY_AND_PATIENT,
    CLAIM_POLICY_AND_PATIENT_AUTO_COMPLETE_SEARCH,
    CLAIM_POLICY_AND_PATIENT_SEARCH_POLICY_LABEL,
    CLAIM_POLICY_AND_PATIENT_WRAPPER
} from './classnames'
import {INDIVIDUALCUSTOMER, ORGANIZATIONCUSTOMER} from './constants'
import {DentalPolicyAndPatientStore, DentalPolicyAndPatientStoreImpl} from './store/PolicyAndPatientStore'
import {composeSuggestion, getPatientOptions, mapPatientInfo, mapPolicyDetails} from './utils'

import CapDentalLossEntity = CapDentalLoss.CapDentalLossEntity

const PATIENT_URI_PATH = 'entity.lossDetail.claimData.patientRole.registryId'

interface ClaimPolicyAndPatientProps {
    className?: string
}

interface ClaimPolicyAndPatientComponentProps {
    store: DentalPolicyAndPatientStore
}

export const ClaimPolicyAndPatientComponent: React.FC<ClaimPolicyAndPatientComponentProps> = observer(({store}) => {
    const customerNumber = getParamFromURL('customerNumber')
    const customerType = getParamFromURL('customerType')?.toLowerCase()
    const [searchValue, setSearchValue] = useState<string>('')
    const {
        policy,
        policyList,
        policyWithInsured,
        insureds,
        groupSponsor,
        isEditMode,
        policyEditable,
        removePolicy,
        setPolicyList,
        addPolicy,
        searchPolicy,
        setCurrentPolicy,
        searchDentalMasterPolicyList,
        isLoading
    } = store
    const formState = useFormState<{entity: CapDentalLossEntity}>({
        subscription: {
            values: true
        }
    })
    const {mode} = useIntakeRootStore()
    useEffect(() => {
        if (!customerNumber) {
            return
        }
        const isUrlHasICNumber = customerType === INDIVIDUALCUSTOMER
        const isUrlHasOCNumber = customerType === ORGANIZATIONCUSTOMER
        if (isUrlHasICNumber) {
            setSearchValue(customerNumber)
            searchPolicy(customerNumber, customerType || '')
        }
        if (isUrlHasOCNumber) {
            searchDentalMasterPolicyList(customerNumber)
        }
    }, [])

    const handleSearchPolicy = useCallback(
        (val: string) => {
            if (customerType === INDIVIDUALCUSTOMER) {
                return
            }
            if (!policy && val) {
                searchPolicy(val, customerType || '')
            }
        },
        [customerType, policy]
    )

    if (isLoading) {
        return <RelativeSpinner />
    }

    const onSearchPolicySuggestionSelected = val => {
        const selelectedPolicy = toJS(policyList).find(policyItem => policyItem?.rootId === val) as DentalPolicySummary
        setSearchValue(`${selelectedPolicy?.personFirstName} ${selelectedPolicy?.personLastName}`)
        setCurrentPolicy(selelectedPolicy)
        addPolicy(selelectedPolicy)
    }

    const onSearchPolicyInputChange = (value: string): void => {
        if (customerType === INDIVIDUALCUSTOMER) {
            return
        }
        if (!value) {
            setPolicyList([])
        }
        setSearchValue(value)
    }

    if (isEditMode) {
        const suggestions = toJS(policyList)?.map(composeSuggestion) || []
        return (
            <Col span={12}>
                <h4 className={CLAIM_POLICY_AND_PATIENT_SEARCH_POLICY_LABEL}>
                    {t('dental-product:dental-claim-intake-wizard-search-for-policy')}
                </h4>
                <AutocompletableSearch
                    isHighLight
                    allowClear={false}
                    value={searchValue}
                    suggestions={suggestions}
                    valueChangeHandler={onSearchPolicyInputChange}
                    onSearchInput={handleSearchPolicy}
                    onSuggestionSelected={onSearchPolicySuggestionSelected}
                    placeholder={t('dental-product:dental-claim-intake-wizard-enter-name-or-policy')}
                    dropdownClassName={CLAIM_POLICY_AND_PATIENT_AUTO_COMPLETE_SEARCH}
                    withDebounce
                />
                <Validation name='entity.policyId' touchOnChange />
            </Col>
        )
    }

    const patientUri = formState?.values?.entity?.lossDetail?.claimData?.patientRole?.registryId
    const onRemovePolicy = () => {
        removePolicy()
        if (!customerNumber?.includes(INDIVIDUALCUSTOMER)) {
            setSearchValue('')
            setPolicyList([])
        }
    }
    return (
        <Row className={CLAIM_POLICY_AND_PATIENT} gutter={24}>
            <Col span={12}>
                <SelectPatientCard
                    shouldDisplaySelect={mode === CLAIM_MODE.INTAKE}
                    policyNumber={policyWithInsured?.policyNumber}
                    details={mapPolicyDetails(formState.values.entity, policyWithInsured, insureds, groupSponsor)}
                    name={PATIENT_URI_PATH}
                    options={getPatientOptions(insureds)}
                    onRemove={policyEditable && mode === CLAIM_MODE.INTAKE ? onRemovePolicy : undefined}
                />
            </Col>
            {patientUri && (
                <Col span={12}>
                    <ContactCardPatient {...mapPatientInfo(insureds, get(formState.values, PATIENT_URI_PATH))} />
                </Col>
            )}
        </Row>
    )
})

export const ClaimPolicyAndPatient: React.FC<ClaimPolicyAndPatientProps> = props => {
    const form = useForm()
    const store = useMemo(() => new DentalPolicyAndPatientStoreImpl(form), [])

    const {registerStore} = useContextRefStores()
    useEffect(() => {
        store.initStore()
        registerStore({
            policyAndPatientStore: store
        })
    }, [])

    return (
        <div className={CLAIM_POLICY_AND_PATIENT_WRAPPER}>
            <h3 className={CLAIM_INFO_SECTION_TITLE}>
                {t('dental-product:dental-claim-intake-wizard-select-policy-and-patient')}
            </h3>
            <ClaimPolicyAndPatientComponent store={store} {...props} />
        </div>
    )
}
