/*
 * Copyright © 2024 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {sumBy} from 'lodash'
import {observer} from 'mobx-react'

import {opt} from '@eisgroup/common-types'
import {PaymentTableDataType} from '@eisgroup/dental-product-services'
import {LocalizationUtils, t} from '@eisgroup/i18n'
import {Table, Tooltip} from '@eisgroup/ui-kit'
import {ValidationMediumError} from '@eisgroup/ui-kit-icons'

import {formatCustomerDisplayName, moneyByLocale} from '../../../../../utils/helpers'
import {PAYMENT_MODEL_TYPE, PaymentStatus} from '../../../../../utils/types/enums'
import {DentalClaimParties} from '../../../store/DentalOverviewPageStore'
import {
    DENTAL_PAYMENT_TABLE_CANCEL_REASON_ICON,
    DENTAL_PAYMENT_TABLE_EXPAND_SECTION,
    DENTAL_PAYMENT_TABLE_WRAPPER
} from '../classnames'
import {PAYMENT_CLAIM_TYPE} from '../utils'
import {PaymentAllocation, PaymentDetails, PaymentTableExpandRow} from './PaymentTableExpandRow'

import dateValue = LocalizationUtils.dateValue

export interface PaymentsTableProps {
    paymentList: PaymentTableDataType
    parties: DentalClaimParties
}

export const PaymentsTable: React.FC<PaymentsTableProps> = observer(({...props}) => {
    const getColumns = () => {
        return [
            {
                title: t('dental-product:dental-claim-payment-table-payment-number'),
                key: 'paymentNumber',
                render: entity => entity.paymentNumber || entity.scheduleNumber || t('dental-product:empty')
            },
            {
                title: t('dental-product:dental-claim-payment-table-payment-date'),
                key: 'paymentDate',
                render: entity => {
                    let displayName = ''
                    if (
                        entity._modelType === PAYMENT_MODEL_TYPE.CapPaymentSchedule &&
                        entity.paymentDetails?.paymentAllocations?.[0]?.allocationDentalDetails?.transactionTypeCd !==
                            PAYMENT_CLAIM_TYPE.ORTHODONTIC_SERVICES
                    ) {
                        displayName = opt(entity?.creationDate && dateValue(entity.creationDate).toString()).orElse(
                            t('dental-product:empty')
                        )
                    } else {
                        displayName = opt(
                            entity?.paymentDetails?.paymentDate &&
                                dateValue(entity.paymentDetails.paymentDate).toString()
                        ).orElse(t('dental-product:empty'))
                    }
                    return displayName
                }
            },
            {
                title: t('dental-product:dental-claim-payment-table-payee'),
                key: 'payee',
                render: entity => {
                    const payeeLink = entity.paymentDetails?.payeeDetails?.payee?._uri || ''
                    const partyList = Object.values(props.parties).filter(Boolean)
                    const customer = partyList.find(
                        party =>
                            payeeLink.includes(party?._key?.rootId) || payeeLink.includes(party?.provider?._key?.rootId)
                    )

                    let displayName = ''
                    if (customer?.provider && customer.customer) {
                        displayName = formatCustomerDisplayName(customer.customer)
                    } else if (customer) {
                        displayName = formatCustomerDisplayName(customer)
                    }
                    return displayName
                }
            },
            {
                title: t('dental-product:dental-claim-payment-table-payment-net-amount'),
                key: 'paymentNetAmount',
                align: 'right' as const,
                render: entity =>
                    opt(entity.paymentNetAmount && moneyByLocale(entity.paymentNetAmount)).orElse(
                        t('dental-product:empty')
                    )
            },
            {
                title: t('dental-product:dental-claim-payment-table-status'),
                key: 'status',
                width: '200px',
                render: entity => {
                    let displayName = ''
                    if (entity._modelType === PAYMENT_MODEL_TYPE.CapPaymentSchedule) {
                        displayName = t(
                            `dental-product:dental-claim-payment-table-peyment-schedule-state-${entity.state}`
                        )
                    } else {
                        displayName = opt(entity.state).orElse(t('dental-product:empty'))
                    }
                    return (
                        <>
                            {displayName}
                            {entity.state === PaymentStatus.Canceled && entity.messages?.length > 0 && (
                                <Tooltip
                                    placement='topLeft'
                                    title={entity.messages.map(v => (
                                        <div key={v.code}>{v.message}</div>
                                    ))}
                                >
                                    <ValidationMediumError className={DENTAL_PAYMENT_TABLE_CANCEL_REASON_ICON} />
                                </Tooltip>
                            )}
                        </>
                    )
                }
            }
        ]
    }

    const calculateTotalAmount = (propertyKey: string, paymentDetails?: PaymentDetails) =>
        sumBy(paymentDetails?.paymentAllocations ?? [], (alloc: PaymentAllocation) => alloc[propertyKey]?.amount ?? 0)

    const calculateInterest = (paymentDetails?: PaymentDetails) => {
        const interestAdditionNums = (paymentDetails?.paymentAdditions ?? [])
            .filter((add: {additionType: string}) => add.additionType === 'INTEREST')
            .map((add: {additionNumber: number}) => add.additionNumber)

        return sumBy(
            paymentDetails?.paymentAllocations?.flatMap(
                (alloc: PaymentAllocation) => alloc.additionSplitResults ?? []
            ) ?? [],
            (split: {additionNumber: number; appliedAmount?: {amount?: number}}) =>
                interestAdditionNums.includes(split.additionNumber) ? split.appliedAmount?.amount ?? 0 : 0
        )
    }

    const expandedRowRender = (record: any, index: number): React.ReactNode => {
        const paymentDetails = record?.paymentDetails
        const totalAllocation = calculateTotalAmount('allocationGrossAmount', paymentDetails)
        const interest = calculateInterest(paymentDetails)
        const totalPayment = calculateTotalAmount('allocationNetAmount', paymentDetails)
        return (
            <div className={DENTAL_PAYMENT_TABLE_EXPAND_SECTION}>
                <PaymentTableExpandRow
                    expandName={t('dental-product:dental_claim_payment_table_expand_total_allocation_label')}
                    amount={totalAllocation}
                />
                <PaymentTableExpandRow
                    expandName={t('dental-product:dental_claim_payment_table_expand_interest')}
                    amount={interest}
                />
                <PaymentTableExpandRow
                    expandName={t('dental-product:dental_claim_payment_table_expand_total_payment_amount_label')}
                    amount={totalPayment}
                />
            </div>
        )
    }

    return (
        <div className={DENTAL_PAYMENT_TABLE_WRAPPER}>
            <Table
                dataSource={props.paymentList}
                columns={getColumns()}
                pagination={{
                    size: 'small',
                    defaultPageSize: 5,
                    pageSizeOptions: ['5', '10', '15', '20', '25'],
                    showQuickJumper: true,
                    showSizeChanger: true,
                    total: props.paymentList.length
                }}
                expandedRowRender={expandedRowRender}
                bordered={false}
            />
        </div>
    )
})
