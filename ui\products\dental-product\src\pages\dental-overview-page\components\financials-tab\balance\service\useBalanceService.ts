import {useEffect, useState} from 'react'

import {createFormSelector} from '@eisgroup/dental-core'
import {CapDentalBalance, CapDentalLoss, CapDentalSettlement} from '@eisgroup/dental-models'
import {dentalBalanceService} from '@eisgroup/dental-product-services'

import {formatLossSource} from '../../utils'

import CapDentalBalanceEntity = CapDentalBalance.CapDentalBalanceEntity
import DentalLoss = CapDentalLoss.CapDentalLossEntity
import CapDentalSettlementEntity = CapDentalSettlement.CapDentalSettlementEntity

export const useBalanceService = () => {
    const [balanceData, setBalanceData] = useState<CapDentalBalanceEntity>()
    const [balanceLoading, setBalanceLoading] = useState<boolean>(false)

    const loss = createFormSelector<{entity: DentalLoss}, DentalLoss>(state => state.entity)()
    const settlements = createFormSelector<{settlementList: CapDentalSettlementEntity[]}, CapDentalSettlementEntity[]>(
        state => state.settlementList
    )()

    const lossSource = formatLossSource(loss)

    useEffect(() => {
        setBalanceLoading(true)
        dentalBalanceService
            .searchBalance({
                query: {
                    originSource: {
                        matches: [lossSource]
                    }
                },
                sort: {
                    creationDate: 'desc'
                }
            })
            .subscribe(
                result => {
                    const balanceResult = result.getOrElse({count: 0, result: []})
                    const currentBalance = balanceResult?.result?.[0]
                    setBalanceData(currentBalance as CapDentalBalanceEntity)
                },
                undefined,
                () => setBalanceLoading(false)
            )
    }, [lossSource])

    return {
        settlements,
        balanceData,
        balanceLoading
    }
}
