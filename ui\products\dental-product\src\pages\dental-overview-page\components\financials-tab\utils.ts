import {CapDentalPaymentDefinition, CapDentalPaymentSchedule} from '@eisgroup/dental-models'
import {PaymentTableDataType} from '@eisgroup/dental-product-services'
import {LocalizationUtils} from '@eisgroup/i18n'

import {PAYMENT_MODEL_TYPE, PaymentStatus} from '../../../../utils/types/enums'
import {PaymentScheduledState} from './contants'

import dateValue = LocalizationUtils.dateValue

import CapDentalPaymentEntity = CapDentalPaymentDefinition.CapDentalPaymentEntity
import CapDentalPaymentScheduleEntity = CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity

export const formatLossSource = loss => {
    if (!loss) {
        return ''
    }
    return `gentity://${loss._modelType}/${loss._modelName}//${loss._key.rootId}/${loss._key.revisionNo}`
}

export const getClaimTypeCd = entity =>
    entity.paymentDetails?.paymentAllocations?.[0]?.allocationDentalDetails?.transactionTypeCd

export enum PAYMENT_CLAIM_TYPE {
    ACTUAL_SERVICES = 'ActualServices',
    ORTHODONTIC_SERVICES = 'OrthodonticServices'
}

const filterSchedulesHasSameIssuedPayment = (
    payments: CapDentalPaymentEntity[],
    paymentSchedules: CapDentalPaymentScheduleEntity[]
) => {
    const paymentLookup = new Map()
    const cancelledPaymentLookup = new Map()

    payments.forEach(payment => {
        const paymentDateKey = payment.paymentDetails?.paymentDate?.getTime() ?? null
        const allocations = payment.paymentDetails?.paymentAllocations || []

        if (payment.state === PaymentStatus.Canceled) {
            cancelledPaymentLookup.set(payment.paymentDetails?.paymentDate?.getTime?.(), true)
            return
        }
        allocations.forEach(allocation => {
            const item = allocation.allocationPayableItem
            if (!item) {
                return
            }

            const key = `${item.procedureID}-${item.orthoMonth}-${paymentDateKey}`
            paymentLookup.set(key, true)
        })
    })

    const filteredPaymentSchedules = paymentSchedules
        .map(schedule => {
            const filteredPayments = schedule.payments
                .filter(onePay => {
                    return !cancelledPaymentLookup.has(onePay.paymentDetails?.paymentDate?.getTime?.())
                })
                .filter(schedulePayment => {
                    const scheduleDateKey = schedulePayment.paymentDetails?.paymentDate?.getTime() ?? null
                    const scheduleAllocations = schedulePayment.paymentDetails?.paymentAllocations || []

                    return !scheduleAllocations.some(allocation => {
                        const item = allocation.allocationPayableItem
                        if (!item) {
                            return false
                        }

                        const key = `${item.procedureID}-${item.orthoMonth}-${scheduleDateKey}`

                        return paymentLookup.has(key)
                    })
                })
            return {...schedule, payments: filteredPayments}
        })
        .filter(schedule => schedule.payments.length > 0)
    return filteredPaymentSchedules
}

export const filterPaymentData = (
    payments: CapDentalPaymentEntity[],
    paymentSchedules: CapDentalPaymentScheduleEntity[]
): PaymentTableDataType => {
    const filteredPaymentSchedules = filterSchedulesHasSameIssuedPayment(payments, paymentSchedules)

    let paymentTableData = [
        ...filteredPaymentSchedules.filter(paymentSchedule => paymentSchedule.state !== PaymentScheduledState.Canceled),
        ...payments.filter(
            payment =>
                !(payment.state === PaymentStatus.Canceled && (!payment.messages || payment.messages.length === 0))
        )
    ] as PaymentTableDataType

    if (
        payments.length &&
        filteredPaymentSchedules.length &&
        getClaimTypeCd(payments[0]) === PAYMENT_CLAIM_TYPE.ACTUAL_SERVICES &&
        payments[0].paymentSchedule?._uri.includes(filteredPaymentSchedules[0]?._key?.rootId)
    ) {
        paymentTableData = [...payments]
    }
    return paymentTableData
}

export const filterOrthoPaymentSchedule = (result: PaymentTableDataType): PaymentTableDataType => {
    const orthoPayments = result.filter(
        payment =>
            payment._modelType === PAYMENT_MODEL_TYPE.CapPayment &&
            getClaimTypeCd(payment) === PAYMENT_CLAIM_TYPE.ORTHODONTIC_SERVICES
    )
    const orthoPaymentsMap = {}
    orthoPayments.forEach(oPayment => {
        if (!orthoPaymentsMap[oPayment.paymentSchedule._uri]) {
            orthoPaymentsMap[oPayment.paymentSchedule._uri] = [
                dateValue(oPayment.paymentDetails.paymentDate).toString()
            ]
        } else {
            orthoPaymentsMap[oPayment.paymentSchedule._uri].push(
                dateValue(oPayment.paymentDetails.paymentDate).toString()
            )
        }
    })
    return result.filter(payment => {
        const paymentScheduleUri = `gentity://${payment._modelType}/${payment._modelName}//${payment._key?.rootId}/${payment._key?.revisionNo}`
        return (
            payment._modelType === PAYMENT_MODEL_TYPE.CapPayment ||
            (payment._modelType === PAYMENT_MODEL_TYPE.CapPaymentSchedule &&
                payment.paymentDetails?.paymentDate &&
                !orthoPaymentsMap[paymentScheduleUri]?.includes(
                    dateValue(payment.paymentDetails?.paymentDate).toString()
                ))
        )
    })
}

export const formatPaymentTableData = (
    payments: CapDentalPaymentEntity[],
    paymentSchedules: CapDentalPaymentScheduleEntity[]
): PaymentTableDataType => {
    const paymentTableData = filterPaymentData(payments, paymentSchedules)
    let result: any[] = []
    paymentTableData.forEach(item => {
        if (item._modelType === PAYMENT_MODEL_TYPE.CapPaymentSchedule) {
            const tempResult = item.payments.map(payment => {
                return {
                    ...payment,
                    ...item
                }
            })
            result = [...result, ...tempResult]
        } else {
            result.push(item)
        }
    })

    return filterOrthoPaymentSchedule(result)
}
