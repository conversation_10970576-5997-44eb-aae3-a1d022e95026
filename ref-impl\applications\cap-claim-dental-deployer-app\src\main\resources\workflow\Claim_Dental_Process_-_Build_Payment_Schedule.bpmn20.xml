<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:design="http://flowable.org/design" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" design:palette="flowable-work-process-palette">
  <process id="claimdentalbpmnbuildPaymentSchedule" name="Claim Dental Process - Build Payment Schedule" isExecutable="true">
    <serviceTask id="cancelLossPaymentSchedules" name="cancelLossPaymentSchedules" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_key}" target="_key"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalPaymentSchedule" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="{'originSource':{'_uri': '${_uri}'}}" target="payload"></flowable:eventInParameter>
        <flowable:eventInParameter source="cancelLossPaymentSchedules" target="commandName"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="payload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <serviceTask id="buildPaymentSchedule" name="BuildPaymentSchedule" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter  target="_key"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalPaymentSchedule" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="buildPaymentSchedule" target="commandName"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="{'originSource':{'_uri': '${_uri}'}, 'settlements': [{'_uri': '${settlementURI}'}]}" target="payload"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="payload"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_uri" sourceType="string" target="_uri"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_key" sourceType="string" target="_key"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <serviceTask id="activatePaymentSchedule" name="ActivatePaymentSchedule" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_key}" target="_key"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalPaymentSchedule" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="activatePaymentSchedule" target="commandName"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="payload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <serviceTask id="schedule_approval_rules" name="Check Activation Rules" flowable:type="http">
      <extensionElements>
        <flowable:field name="requestMethod">
          <flowable:string><![CDATA[POST]]></flowable:string>
        </flowable:field>
        <flowable:field name="requestUrl">
          <flowable:expression><![CDATA[${HTTPHelper.serviceDiscoveryQuery(execution).serviceGroup('common').serviceName('verifyDentalPaymentScheduleActivation').query()}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestHeaders">
          <flowable:expression><![CDATA[Authorization: ${HTTPHelper.systemUserToken()}
Accept-Encoding: identity]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestBody">
          <flowable:expression><![CDATA[{ "body": { "userRequest": ${HTTPHelper.payloadBuilder().addObject('userId', execution.getVariable('userId')).addObject('_modelName', 'DentalInternal').addObject('_type', 'CapUserCarryingEntity').build()}, "schedule":  ${payload} }}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="responseVariableName">
          <flowable:string><![CDATA[checkPayload]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseParametersTransient">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseVariableAsJson">
          <flowable:expression><![CDATA[${true}]]></flowable:expression>
        </flowable:field>
        <design:stencilid><![CDATA[HttpTask]]></design:stencilid>
        <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
      </extensionElements>
    </serviceTask>
    <exclusiveGateway id="bpmnGateway_2">
      <extensionElements>
        <design:stencilid><![CDATA[Exclusive_Databased_Gateway]]></design:stencilid>
      </extensionElements>
    </exclusiveGateway>
    <startEvent id="sid-167C34DB-4CB3-4E91-92C8-F4581B53505A" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:work-form-field-validation><![CDATA[false]]></flowable:work-form-field-validation>
        <design:stencilid><![CDATA[StartNoneEvent]]></design:stencilid>
      </extensionElements>
    </startEvent>
    <endEvent id="end">
      <extensionElements>
        <design:stencilid><![CDATA[EndNoneEvent]]></design:stencilid>
      </extensionElements>
    </endEvent>
    <sequenceFlow id="toBuildTemplate" sourceRef="cancelLossPaymentSchedules" targetRef="buildPaymentSchedule">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="toActivateTemplate" sourceRef="buildPaymentSchedule" targetRef="schedule_approval_rules">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="sid-615CF673-6236-4BD2-9762-1F70428052FF" sourceRef="activatePaymentSchedule" targetRef="end">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="bpmnSequenceFlow_3" sourceRef="schedule_approval_rules" targetRef="bpmnGateway_2">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <sequenceFlow id="bpmnSequenceFlow_4" name="No" sourceRef="bpmnGateway_2" targetRef="end">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
        <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${checkPayload.body.success.paymentScheduleActivationResult.activationStatus != 'Activate'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="bpmnSequenceFlow_5" name="Yes" sourceRef="bpmnGateway_2" targetRef="activatePaymentSchedule">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
        <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
      </extensionElements>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${checkPayload.body.success.paymentScheduleActivationResult.activationStatus == 'Activate'}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-80B9AC7C-AC38-4D7B-85DF-17488DF389E0" sourceRef="sid-167C34DB-4CB3-4E91-92C8-F4581B53505A" targetRef="cancelLossPaymentSchedules">
      <extensionElements>
        <design:stencilid><![CDATA[SequenceFlow]]></design:stencilid>
      </extensionElements>
    </sequenceFlow>
    <textAnnotation id="bpmnTextAnnotation_6">
      <extensionElements>
        <design:stencilid><![CDATA[TextAnnotation]]></design:stencilid>
        <design:text><![CDATA[Activation decision is Activate?]]></design:text>
      </extensionElements>
      <text>Activation decision is Activate?</text>
    </textAnnotation>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_claimdentalbpmnbuildPaymentSchedule">
    <bpmndi:BPMNPlane bpmnElement="claimdentalbpmnbuildPaymentSchedule" id="BPMNPlane_claimdentalbpmnbuildPaymentSchedule">
      <bpmndi:BPMNShape bpmnElement="cancelLossPaymentSchedules" id="BPMNShape_cancelLossPaymentSchedules">
        <omgdc:Bounds height="80.0" width="100.0" x="240.0" y="188.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="buildPaymentSchedule" id="BPMNShape_buildPaymentSchedule">
        <omgdc:Bounds height="80.0" width="100.0" x="420.0" y="188.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="activatePaymentSchedule" id="BPMNShape_activatePaymentSchedule">
        <omgdc:Bounds height="80.0" width="100.0" x="606.5" y="188.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="schedule_approval_rules" id="BPMNShape_schedule_approval_rules">
        <omgdc:Bounds height="80.0" width="100.0" x="420.0" y="371.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="bpmnTextAnnotation_6" id="BPMNShape_bpmnTextAnnotation_6">
        <omgdc:Bounds height="50.0" width="120.0" x="700.0" y="428.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="bpmnGateway_2" id="BPMNShape_bpmnGateway_2">
        <omgdc:Bounds height="40.0" width="40.0" x="636.5" y="391.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-167C34DB-4CB3-4E91-92C8-F4581B53505A" id="BPMNShape_sid-167C34DB-4CB3-4E91-92C8-F4581B53505A">
        <omgdc:Bounds height="30.0" width="30.0" x="70.5" y="207.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="end" id="BPMNShape_end">
        <omgdc:Bounds height="28.0" width="28.0" x="848.5" y="214.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="toActivateTemplate" id="BPMNEdge_toActivateTemplate" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="470.0" y="268.0"></omgdi:waypoint>
        <omgdi:waypoint x="470.0" y="371.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-80B9AC7C-AC38-4D7B-85DF-17488DF389E0" id="BPMNEdge_sid-80B9AC7C-AC38-4D7B-85DF-17488DF389E0" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="100.0" y="222.0"></omgdi:waypoint>
        <omgdi:waypoint x="239.0" y="226.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="toBuildTemplate" id="BPMNEdge_toBuildTemplate" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="339.0" y="228.0"></omgdi:waypoint>
        <omgdi:waypoint x="419.0" y="228.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="bpmnSequenceFlow_3" id="BPMNEdge_bpmnSequenceFlow_3" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="520.0" y="411.0"></omgdi:waypoint>
        <omgdi:waypoint x="636.5" y="411.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="bpmnSequenceFlow_4" id="BPMNEdge_bpmnSequenceFlow_4" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="676.5" y="411.0"></omgdi:waypoint>
        <omgdi:waypoint x="862.5" y="411.0"></omgdi:waypoint>
        <omgdi:waypoint x="862.5" y="242.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="16.0" x="686.5" y="381.8"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-615CF673-6236-4BD2-9762-1F70428052FF" id="BPMNEdge_sid-615CF673-6236-4BD2-9762-1F70428052FF" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="706.0" y="228.0"></omgdi:waypoint>
        <omgdi:waypoint x="848.0" y="228.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="bpmnSequenceFlow_5" id="BPMNEdge_bpmnSequenceFlow_5" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="656.5" y="391.0"></omgdi:waypoint>
        <omgdi:waypoint x="656.5" y="268.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="18.0" x="666.5" y="361.8"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>