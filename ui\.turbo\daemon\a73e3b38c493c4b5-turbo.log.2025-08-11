2025-08-11T07:06:12.932656Z  WARN daemon_server: turborepo_lib::commands::daemon: daemon already running
2025-08-11T07:08:27.444286Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("products\\dental-product\\src\\pages\\dental-overview-page\\components\\financials-tab\\payments\\PaymentsTable.tsx")}
2025-08-11T07:08:27.526750Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@eisgroup/dental-product"), path: AnchoredSystemPathBuf("products\\dental-product") }}))
2025-08-11T07:11:15.133380Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("products\\dental-product\\src\\pages\\dental-overview-page\\components\\financials-tab\\payments\\PaymentsTable.tsx")}
2025-08-11T07:11:15.133410Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@eisgroup/dental-product"), path: AnchoredSystemPathBuf("products\\dental-product") }}))
2025-08-11T07:11:26.431790Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("products\\dental-product\\src\\pages\\dental-overview-page\\components\\financials-tab\\payments\\PaymentsTable.tsx")}
2025-08-11T07:11:26.431823Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@eisgroup/dental-product"), path: AnchoredSystemPathBuf("products\\dental-product") }}))
2025-08-11T07:11:27.440739Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("products\\dental-product\\src\\pages\\dental-overview-page\\components\\financials-tab\\payments\\PaymentsTable.tsx")}
2025-08-11T07:11:27.440762Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@eisgroup/dental-product"), path: AnchoredSystemPathBuf("products\\dental-product") }}))
2025-08-11T07:11:27.600702Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-08-11T07:11:29.942838Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("products\\dental-product\\src\\pages\\dental-overview-page\\components\\financials-tab\\payments\\PaymentsTable.tsx")}
2025-08-11T07:11:29.942860Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@eisgroup/dental-product"), path: AnchoredSystemPathBuf("products\\dental-product") }}))
2025-08-11T07:11:31.337236Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("products\\dental-product\\src\\pages\\dental-overview-page\\components\\financials-tab\\payments\\PaymentsTable.tsx")}
2025-08-11T07:11:31.337263Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@eisgroup/dental-product"), path: AnchoredSystemPathBuf("products\\dental-product") }}))
2025-08-11T07:11:34.142299Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("products\\dental-product\\src\\pages\\dental-overview-page\\components\\financials-tab\\payments\\PaymentsTable.tsx")}
2025-08-11T07:11:34.142317Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@eisgroup/dental-product"), path: AnchoredSystemPathBuf("products\\dental-product") }}))
2025-08-11T07:11:35.338250Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("products\\dental-product\\src\\pages\\dental-overview-page\\components\\financials-tab\\payments\\PaymentsTable.tsx")}
2025-08-11T07:11:35.338264Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@eisgroup/dental-product"), path: AnchoredSystemPathBuf("products\\dental-product") }}))
2025-08-11T07:11:35.402049Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
