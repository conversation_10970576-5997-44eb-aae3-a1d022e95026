// tslint:disable
export const KRAKEN_MODEL_TREE_CAPDENTALSETTLEMENT = {"contexts":{"Reserve":{"name":"Reserve","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"expenseAmount":{"name":"expenseAmount","fieldType":"MONEY","fieldPath":"expenseAmount","cardinality":"SINGLE"},"indemnityAmount":{"name":"indemnityAmount","fieldType":"MONEY","fieldPath":"indemnityAmount","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"recoveryAmount":{"name":"recoveryAmount","fieldType":"MONEY","fieldPath":"recoveryAmount","cardinality":"SINGLE"}},"inheritedContexts":["ClaimReserveAmounts","MessageTypeHolder"],"system":false},"CapAlternatePayeeRole":{"name":"CapAlternatePayeeRole","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyRole","ClaimPartyAware"],"system":false},"CapDentalPolicyInfoInsuredDetailsEntity":{"name":"CapDentalPolicyInfoInsuredDetailsEntity","children":{},"fields":{"hireDate":{"name":"hireDate","fieldType":"DATE","fieldPath":"hireDate","cardinality":"SINGLE"},"isMain":{"name":"isMain","fieldType":"BOOLEAN","fieldPath":"isMain","cardinality":"SINGLE"},"isFullTimeStudent":{"name":"isFullTimeStudent","fieldType":"BOOLEAN","fieldPath":"isFullTimeStudent","cardinality":"SINGLE"},"insuredRoleNameCd":{"name":"insuredRoleNameCd","fieldType":"STRING","fieldPath":"insuredRoleNameCd","cardinality":"SINGLE"},"relationshipToPrimaryInsuredCd":{"name":"relationshipToPrimaryInsuredCd","fieldType":"STRING","fieldPath":"relationshipToPrimaryInsuredCd","cardinality":"SINGLE"},"registryTypeId":{"name":"registryTypeId","fieldType":"STRING","fieldPath":"registryTypeId","cardinality":"SINGLE"}},"inheritedContexts":["CapInsuredInfo","InsuredInfoAware"],"system":false},"AccessTrackableEntity":{"name":"AccessTrackableEntity","children":{"AccessTrackInfo":{"targetName":"AccessTrackInfo","navigationExpression":{"expressionString":"accessTrackInfo","originalExpressionString":"accessTrackInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"accessTrackInfo":{"name":"accessTrackInfo","fieldType":"AccessTrackInfo","fieldPath":"accessTrackInfo","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"InsuredAware":{"name":"InsuredAware","children":{},"fields":{"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyAware"],"system":false},"CapPayment":{"name":"CapPayment","children":{"CapPaymentDetails":{"targetName":"CapPaymentDetails","navigationExpression":{"expressionString":"paymentDetails","originalExpressionString":"paymentDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"},"paymentNetAmount":{"name":"paymentNetAmount","fieldType":"MONEY","fieldPath":"paymentNetAmount","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"paymentDetails":{"name":"paymentDetails","fieldType":"CapPaymentDetails","fieldPath":"paymentDetails","cardinality":"SINGLE","forbidTarget":true},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"},"paymentNumber":{"name":"paymentNumber","fieldType":"STRING","fieldPath":"paymentNumber","cardinality":"SINGLE"},"direction":{"name":"direction","fieldType":"STRING","fieldPath":"direction","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentInfo","RootEntity"],"system":false},"TimedCoverableItemCondition":{"name":"TimedCoverableItemCondition","children":{},"fields":{"period":{"name":"period","fieldType":"STRING","fieldPath":"period","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition"],"system":false},"CapDentalRuleInputEntity":{"name":"CapDentalRuleInputEntity","children":{"CapDentalEntryPreviouslyUsedEntity":{"targetName":"CapDentalEntryPreviouslyUsedEntity","navigationExpression":{"expressionString":"entryPreviouslyUsed","originalExpressionString":"entryPreviouslyUsed","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalClaimInfoEntity":{"targetName":"CapDentalClaimInfoEntity","navigationExpression":{"expressionString":"loss","originalExpressionString":"loss","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalSettlementDetailEntity":{"targetName":"CapDentalSettlementDetailEntity","navigationExpression":{"expressionString":"details","originalExpressionString":"details","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"loss":{"name":"loss","fieldType":"CapDentalClaimInfoEntity","fieldPath":"loss","cardinality":"SINGLE","forbidTarget":true},"entryPreviouslyUsed":{"name":"entryPreviouslyUsed","fieldType":"CapDentalEntryPreviouslyUsedEntity","fieldPath":"entryPreviouslyUsed","cardinality":"MULTIPLE","forbidTarget":true},"details":{"name":"details","fieldType":"CapDentalSettlementDetailEntity","fieldPath":"details","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapDentalPaymentPayeeDetailsEntity":{"name":"CapDentalPaymentPayeeDetailsEntity","children":{},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"paymentMethodDetails":{"name":"paymentMethodDetails","fieldType":"CapPaymentMethodDetails","fieldPath":"paymentMethodDetails","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["CapPaymentPayeeDetails"],"system":false},"CapDentalRemarkMessageEntity":{"name":"CapDentalRemarkMessageEntity","children":{},"fields":{"remarkCode":{"name":"remarkCode","fieldType":"STRING","fieldPath":"remarkCode","cardinality":"SINGLE"},"remarkMessage":{"name":"remarkMessage","fieldType":"STRING","fieldPath":"remarkMessage","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalProviderFeesEntity":{"name":"CapDentalProviderFeesEntity","children":{},"fields":{"fee":{"name":"fee","fieldType":"MONEY","fieldPath":"fee","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalUnverifiedInfoEntity":{"name":"CapDentalUnverifiedInfoEntity","children":{},"fields":{"employerName":{"name":"employerName","fieldType":"STRING","fieldPath":"employerName","cardinality":"SINGLE"},"groupNumber":{"name":"groupNumber","fieldType":"STRING","fieldPath":"groupNumber","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalPolicyInfoCoinsuranceEntity":{"name":"CapDentalPolicyInfoCoinsuranceEntity","children":{},"fields":{"coinsuranceOONPct":{"name":"coinsuranceOONPct","fieldType":"DECIMAL","fieldPath":"coinsuranceOONPct","cardinality":"SINGLE"},"coinsuranceServiceType":{"name":"coinsuranceServiceType","fieldType":"STRING","fieldPath":"coinsuranceServiceType","cardinality":"SINGLE"},"coinsuranceINPct":{"name":"coinsuranceINPct","fieldType":"DECIMAL","fieldPath":"coinsuranceINPct","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalWaiveServiceValueEntity":{"name":"CapDentalWaiveServiceValueEntity","children":{},"fields":{"waiveServiceFrequencyLimit":{"name":"waiveServiceFrequencyLimit","fieldType":"BOOLEAN","fieldPath":"waiveServiceFrequencyLimit","cardinality":"SINGLE"},"waiveDeductible":{"name":"waiveDeductible","fieldType":"BOOLEAN","fieldPath":"waiveDeductible","cardinality":"SINGLE"},"waiveMaximumLimitAmounts":{"name":"waiveMaximumLimitAmounts","fieldType":"BOOLEAN","fieldPath":"waiveMaximumLimitAmounts","cardinality":"SINGLE"},"waiveReplacementLimit":{"name":"waiveReplacementLimit","fieldType":"BOOLEAN","fieldPath":"waiveReplacementLimit","cardinality":"SINGLE"},"waiveEligibilityPriorStartDate":{"name":"waiveEligibilityPriorStartDate","fieldType":"BOOLEAN","fieldPath":"waiveEligibilityPriorStartDate","cardinality":"SINGLE"},"waiveEligibilityAfterStartDate":{"name":"waiveEligibilityAfterStartDate","fieldType":"BOOLEAN","fieldPath":"waiveEligibilityAfterStartDate","cardinality":"SINGLE"},"waiveServiceWaitingPeriod":{"name":"waiveServiceWaitingPeriod","fieldType":"BOOLEAN","fieldPath":"waiveServiceWaitingPeriod","cardinality":"SINGLE"},"waiveLateEntrantWaitingPeriod":{"name":"waiveLateEntrantWaitingPeriod","fieldType":"BOOLEAN","fieldPath":"waiveLateEntrantWaitingPeriod","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyInfo":{"name":"CapPolicyInfo","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapInsuredInfo":{"targetName":"CapInsuredInfo","navigationExpression":{"expressionString":"insureds","originalExpressionString":"insureds","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"currencyCd":{"name":"currencyCd","fieldType":"STRING","fieldPath":"currencyCd","cardinality":"SINGLE"},"riskStateCd":{"name":"riskStateCd","fieldType":"STRING","fieldPath":"riskStateCd","cardinality":"SINGLE"},"isVerified":{"name":"isVerified","fieldType":"BOOLEAN","fieldPath":"isVerified","cardinality":"SINGLE"},"insureds":{"name":"insureds","fieldType":"CapInsuredInfo","fieldPath":"insureds","cardinality":"MULTIPLE","forbidTarget":true},"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyType":{"name":"policyType","fieldType":"STRING","fieldPath":"policyType","cardinality":"SINGLE"},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"policyStatus":{"name":"policyStatus","fieldType":"STRING","fieldPath":"policyStatus","cardinality":"SINGLE"},"txEffectiveDate":{"name":"txEffectiveDate","fieldType":"STRING","fieldPath":"txEffectiveDate","cardinality":"SINGLE"},"capPolicyVersionId":{"name":"capPolicyVersionId","fieldType":"STRING","fieldPath":"capPolicyVersionId","cardinality":"SINGLE"},"productCd":{"name":"productCd","fieldType":"STRING","fieldPath":"productCd","cardinality":"SINGLE"}},"inheritedContexts":["CapClaimHeaderPolicy","ClaimPolicyAware"],"system":false},"CapTimelineClaimInfo":{"name":"CapTimelineClaimInfo","children":{},"fields":{"appliedCoverage":{"name":"appliedCoverage","fieldType":"STRING","fieldPath":"appliedCoverage","cardinality":"SINGLE"},"claimType":{"name":"claimType","fieldType":"STRING","fieldPath":"claimType","cardinality":"SINGLE"},"examinerName":{"name":"examinerName","fieldType":"STRING","fieldPath":"examinerName","cardinality":"SINGLE"},"caseNumber":{"name":"caseNumber","fieldType":"STRING","fieldPath":"caseNumber","cardinality":"SINGLE"},"self":{"name":"self","fieldType":"ExternalLink","fieldPath":"self","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"claimNumber":{"name":"claimNumber","fieldType":"STRING","fieldPath":"claimNumber","cardinality":"SINGLE"}},"inheritedContexts":["SelfAware"],"system":false},"PrimaryEntityLinkAware":{"name":"PrimaryEntityLinkAware","children":{},"fields":{"primaryURI":{"name":"primaryURI","fieldType":"ExternalLink","fieldPath":"primaryURI","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationTemplateLossInfo":{"name":"CapPaymentAllocationTemplateLossInfo","children":{},"fields":{"lossSource":{"name":"lossSource","fieldType":"ExternalLink","fieldPath":"lossSource","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CoverableItemHolder":{"name":"CoverableItemHolder","children":{"CoverableItem":{"targetName":"CoverableItem","navigationExpression":{"expressionString":"coverableItems","originalExpressionString":"coverableItems","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"coverableItems":{"name":"coverableItems","fieldType":"CoverableItem","fieldPath":"coverableItems","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"RequestedReserve":{"name":"RequestedReserve","children":{"RequestedReserveItem":{"targetName":"RequestedReserveItem","navigationExpression":{"expressionString":"items","originalExpressionString":"items","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"modificationReason":{"name":"modificationReason","fieldType":"STRING","fieldPath":"modificationReason","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"items":{"name":"items","fieldType":"RequestedReserveItem","fieldPath":"items","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"InsuredInfoAware":{"name":"InsuredInfoAware","children":{},"fields":{"isMain":{"name":"isMain","fieldType":"BOOLEAN","fieldPath":"isMain","cardinality":"SINGLE"},"registryTypeId":{"name":"registryTypeId","fieldType":"STRING","fieldPath":"registryTypeId","cardinality":"SINGLE"},"insuredRoleNameCd":{"name":"insuredRoleNameCd","fieldType":"STRING","fieldPath":"insuredRoleNameCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentTaxTemplate":{"name":"CapPaymentTaxTemplate","children":{},"fields":{"taxSource":{"name":"taxSource","fieldType":"ExternalLink","fieldPath":"taxSource","cardinality":"SINGLE"},"taxNumber":{"name":"taxNumber","fieldType":"STRING","fieldPath":"taxNumber","cardinality":"SINGLE"},"taxType":{"name":"taxType","fieldType":"STRING","fieldPath":"taxType","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentTax"],"system":false},"Applicability":{"name":"Applicability","children":{"CoverableItemHolder":{"targetName":"CoverableItemHolder","navigationExpression":{"expressionString":"itemHolder","originalExpressionString":"itemHolder","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"itemHolder":{"name":"itemHolder","fieldType":"CoverableItemHolder","fieldPath":"itemHolder","cardinality":"SINGLE","forbidTarget":true},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["MessageTypeHolder"],"system":false},"CorrelationIdHolder":{"name":"CorrelationIdHolder","children":{},"fields":{"correlationId":{"name":"correlationId","fieldType":"STRING","fieldPath":"correlationId","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"JsonType":{"name":"JsonType","children":{},"fields":{},"inheritedContexts":[],"system":false},"CapPolicyBasicLimitationsEntity":{"name":"CapPolicyBasicLimitationsEntity","children":{},"fields":{"basicPeriodontalSurgery":{"name":"basicPeriodontalSurgery","fieldType":"STRING","fieldPath":"basicPeriodontalSurgery","cardinality":"SINGLE"},"basicStainlessSteelCrownsAgeLimit":{"name":"basicStainlessSteelCrownsAgeLimit","fieldType":"STRING","fieldPath":"basicStainlessSteelCrownsAgeLimit","cardinality":"SINGLE"},"basicStainlessSteelCrowns":{"name":"basicStainlessSteelCrowns","fieldType":"STRING","fieldPath":"basicStainlessSteelCrowns","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicy":{"name":"CapPolicy","children":{"CoverableItem":{"targetName":"CoverableItem","navigationExpression":{"expressionString":"coverableItems","originalExpressionString":"coverableItems","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPolicyExclusion":{"targetName":"CapPolicyExclusion","navigationExpression":{"expressionString":"exclusions","originalExpressionString":"exclusions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"InsurableRisk":{"targetName":"InsurableRisk","navigationExpression":{"expressionString":"risks","originalExpressionString":"risks","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapInsuredInfo":{"targetName":"CapInsuredInfo","navigationExpression":{"expressionString":"insureds","originalExpressionString":"insureds","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"currencyCd":{"name":"currencyCd","fieldType":"STRING","fieldPath":"currencyCd","cardinality":"SINGLE"},"isVerified":{"name":"isVerified","fieldType":"BOOLEAN","fieldPath":"isVerified","cardinality":"SINGLE"},"insureds":{"name":"insureds","fieldType":"CapInsuredInfo","fieldPath":"insureds","cardinality":"MULTIPLE","forbidTarget":true},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"},"exclusions":{"name":"exclusions","fieldType":"CapPolicyExclusion","fieldPath":"exclusions","cardinality":"MULTIPLE","forbidTarget":true},"policyStatus":{"name":"policyStatus","fieldType":"STRING","fieldPath":"policyStatus","cardinality":"SINGLE"},"risks":{"name":"risks","fieldType":"InsurableRisk","fieldPath":"risks","cardinality":"MULTIPLE","forbidTarget":true},"coverableItems":{"name":"coverableItems","fieldType":"CoverableItem","fieldPath":"coverableItems","cardinality":"MULTIPLE","forbidTarget":true},"riskStateCd":{"name":"riskStateCd","fieldType":"STRING","fieldPath":"riskStateCd","cardinality":"SINGLE"},"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyType":{"name":"policyType","fieldType":"STRING","fieldPath":"policyType","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"txEffectiveDate":{"name":"txEffectiveDate","fieldType":"STRING","fieldPath":"txEffectiveDate","cardinality":"SINGLE"},"capPolicyVersionId":{"name":"capPolicyVersionId","fieldType":"STRING","fieldPath":"capPolicyVersionId","cardinality":"SINGLE"},"productCd":{"name":"productCd","fieldType":"STRING","fieldPath":"productCd","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemHolder","CapPolicyInfo","RootEntity","CapClaimHeaderPolicy","ClaimPolicyAware"],"system":false},"CapDentalProcedureCoordinationOfBenefitsEntity":{"name":"CapDentalProcedureCoordinationOfBenefitsEntity","children":{},"fields":{"coverageType":{"name":"coverageType","fieldType":"STRING","fieldPath":"coverageType","cardinality":"SINGLE"},"primaryCoverageStatus":{"name":"primaryCoverageStatus","fieldType":"STRING","fieldPath":"primaryCoverageStatus","cardinality":"SINGLE"},"allowed":{"name":"allowed","fieldType":"MONEY","fieldPath":"allowed","cardinality":"SINGLE"},"considered":{"name":"considered","fieldType":"MONEY","fieldPath":"considered","cardinality":"SINGLE"},"innOnn":{"name":"innOnn","fieldType":"STRING","fieldPath":"innOnn","cardinality":"SINGLE"},"paid":{"name":"paid","fieldType":"MONEY","fieldPath":"paid","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"TimeZoneInfo":{"name":"TimeZoneInfo","children":{},"fields":{"timeZoneId":{"name":"timeZoneId","fieldType":"STRING","fieldPath":"timeZoneId","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"AbsenceRefHolder":{"name":"AbsenceRefHolder","children":{},"fields":{"absence":{"name":"absence","fieldType":"ExternalLink","fieldPath":"absence","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalanceItemAllocationTax":{"name":"CapBalanceItemAllocationTax","children":{},"fields":{"taxSubType":{"name":"taxSubType","fieldType":"STRING","fieldPath":"taxSubType","cardinality":"SINGLE"},"taxState":{"name":"taxState","fieldType":"STRING","fieldPath":"taxState","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"},"taxType":{"name":"taxType","fieldType":"STRING","fieldPath":"taxType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalDiagnosisCodeEntity":{"name":"CapDentalDiagnosisCodeEntity","children":{},"fields":{"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"},"qualifier":{"name":"qualifier","fieldType":"STRING","fieldPath":"qualifier","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalOverrideServiceValueEntity":{"name":"CapDentalOverrideServiceValueEntity","children":{"Period":{"targetName":"Period","navigationExpression":{"expressionString":"overrideEligibilityPeriod","originalExpressionString":"overrideEligibilityPeriod","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"overridePaymentInterestAmount":{"name":"overridePaymentInterestAmount","fieldType":"MONEY","fieldPath":"overridePaymentInterestAmount","cardinality":"SINGLE"},"overrideDeductible":{"name":"overrideDeductible","fieldType":"MONEY","fieldPath":"overrideDeductible","cardinality":"SINGLE"},"overrideMaximumAmount":{"name":"overrideMaximumAmount","fieldType":"MONEY","fieldPath":"overrideMaximumAmount","cardinality":"SINGLE"},"overrideGracePeriod":{"name":"overrideGracePeriod","fieldType":"INTEGER","fieldPath":"overrideGracePeriod","cardinality":"SINGLE"},"overrideEligibilityPeriod":{"name":"overrideEligibilityPeriod","fieldType":"Period","fieldPath":"overrideEligibilityPeriod","cardinality":"SINGLE","forbidTarget":true},"overridePaymentInterestDays":{"name":"overridePaymentInterestDays","fieldType":"INTEGER","fieldPath":"overridePaymentInterestDays","cardinality":"SINGLE"},"overrideStudentDependentStatus":{"name":"overrideStudentDependentStatus","fieldType":"STRING","fieldPath":"overrideStudentDependentStatus","cardinality":"SINGLE"},"overrideCobApplied":{"name":"overrideCobApplied","fieldType":"MONEY","fieldPath":"overrideCobApplied","cardinality":"SINGLE"},"overrideLateEntrantWaitingPeriod":{"name":"overrideLateEntrantWaitingPeriod","fieldType":"INTEGER","fieldPath":"overrideLateEntrantWaitingPeriod","cardinality":"SINGLE"},"overrideServiceWaitingPeriod":{"name":"overrideServiceWaitingPeriod","fieldType":"INTEGER","fieldPath":"overrideServiceWaitingPeriod","cardinality":"SINGLE"},"overrideServiceFrequencyLimit":{"name":"overrideServiceFrequencyLimit","fieldType":"INTEGER","fieldPath":"overrideServiceFrequencyLimit","cardinality":"SINGLE"},"overridePaymentInterestState":{"name":"overridePaymentInterestState","fieldType":"STRING","fieldPath":"overridePaymentInterestState","cardinality":"SINGLE"},"overrideAllowedAmount":{"name":"overrideAllowedAmount","fieldType":"MONEY","fieldPath":"overrideAllowedAmount","cardinality":"SINGLE"},"overridePatientResponsibility":{"name":"overridePatientResponsibility","fieldType":"MONEY","fieldPath":"overridePatientResponsibility","cardinality":"SINGLE"},"overrideCopayAmount":{"name":"overrideCopayAmount","fieldType":"MONEY","fieldPath":"overrideCopayAmount","cardinality":"SINGLE"},"overrideCoveredCdtCode":{"name":"overrideCoveredCdtCode","fieldType":"STRING","fieldPath":"overrideCoveredCdtCode","cardinality":"SINGLE"},"overrideServiceCategory":{"name":"overrideServiceCategory","fieldType":"STRING","fieldPath":"overrideServiceCategory","cardinality":"SINGLE"},"overrideFeeSchedule":{"name":"overrideFeeSchedule","fieldType":"STRING","fieldPath":"overrideFeeSchedule","cardinality":"SINGLE"},"overrideConsideredAmount":{"name":"overrideConsideredAmount","fieldType":"MONEY","fieldPath":"overrideConsideredAmount","cardinality":"SINGLE"},"overrideReplacementLimit":{"name":"overrideReplacementLimit","fieldType":"INTEGER","fieldPath":"overrideReplacementLimit","cardinality":"SINGLE"},"overrideCoinsurancePct":{"name":"overrideCoinsurancePct","fieldType":"DECIMAL","fieldPath":"overrideCoinsurancePct","cardinality":"SINGLE"},"overrideCoveredAmount":{"name":"overrideCoveredAmount","fieldType":"MONEY","fieldPath":"overrideCoveredAmount","cardinality":"SINGLE"},"overrideEssentialHealthBenefit":{"name":"overrideEssentialHealthBenefit","fieldType":"STRING","fieldPath":"overrideEssentialHealthBenefit","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalanceItemAllocationAddition":{"name":"CapBalanceItemAllocationAddition","children":{},"fields":{"additionSubType":{"name":"additionSubType","fieldType":"STRING","fieldPath":"additionSubType","cardinality":"SINGLE"},"additionType":{"name":"additionType","fieldType":"STRING","fieldPath":"additionType","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapTimelineMetric":{"name":"CapTimelineMetric","children":{},"fields":{"unit":{"name":"unit","fieldType":"STRING","fieldPath":"unit","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"value":{"name":"value","fieldType":"DECIMAL","fieldPath":"value","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalCalculationResultEntity":{"name":"CapDentalCalculationResultEntity","children":{},"fields":{"procedureType":{"name":"procedureType","fieldType":"STRING","fieldPath":"procedureType","cardinality":"SINGLE"},"charge":{"name":"charge","fieldType":"MONEY","fieldPath":"charge","cardinality":"SINGLE"},"coveredFee":{"name":"coveredFee","fieldType":"MONEY","fieldPath":"coveredFee","cardinality":"SINGLE"},"payableDeductible":{"name":"payableDeductible","fieldType":"MONEY","fieldPath":"payableDeductible","cardinality":"SINGLE"},"allowedFee":{"name":"allowedFee","fieldType":"MONEY","fieldPath":"allowedFee","cardinality":"SINGLE"},"procedureID":{"name":"procedureID","fieldType":"STRING","fieldPath":"procedureID","cardinality":"SINGLE"},"submittedCode":{"name":"submittedCode","fieldType":"STRING","fieldPath":"submittedCode","cardinality":"SINGLE"},"coinsuranceAmt":{"name":"coinsuranceAmt","fieldType":"MONEY","fieldPath":"coinsuranceAmt","cardinality":"SINGLE"},"coveredCode":{"name":"coveredCode","fieldType":"STRING","fieldPath":"coveredCode","cardinality":"SINGLE"},"patientResponsibility":{"name":"patientResponsibility","fieldType":"MONEY","fieldPath":"patientResponsibility","cardinality":"SINGLE"},"consideredFee":{"name":"consideredFee","fieldType":"MONEY","fieldPath":"consideredFee","cardinality":"SINGLE"},"netBenefitAmount":{"name":"netBenefitAmount","fieldType":"MONEY","fieldPath":"netBenefitAmount","cardinality":"SINGLE"},"copay":{"name":"copay","fieldType":"MONEY","fieldPath":"copay","cardinality":"SINGLE"},"coveredFeeSchedule":{"name":"coveredFeeSchedule","fieldType":"MONEY","fieldPath":"coveredFeeSchedule","cardinality":"SINGLE"},"contributionToMOOP":{"name":"contributionToMOOP","fieldType":"MONEY","fieldPath":"contributionToMOOP","cardinality":"SINGLE"},"coinsurancePercentage":{"name":"coinsurancePercentage","fieldType":"DECIMAL","fieldPath":"coinsurancePercentage","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationReductionSplitResult":{"name":"CapPaymentAllocationReductionSplitResult","children":{},"fields":{"reductionNumber":{"name":"reductionNumber","fieldType":"STRING","fieldPath":"reductionNumber","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalanceChangeLogDetails":{"name":"CapBalanceChangeLogDetails","children":{},"fields":{"totalBalanceAmount":{"name":"totalBalanceAmount","fieldType":"MONEY","fieldPath":"totalBalanceAmount","cardinality":"SINGLE"},"transactionNumber":{"name":"transactionNumber","fieldType":"STRING","fieldPath":"transactionNumber","cardinality":"SINGLE"},"description":{"name":"description","fieldType":"STRING","fieldPath":"description","cardinality":"SINGLE"},"transactionTypeCd":{"name":"transactionTypeCd","fieldType":"STRING","fieldPath":"transactionTypeCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationPayableItem":{"name":"CapPaymentAllocationPayableItem","children":{},"fields":{},"inheritedContexts":[],"system":false},"CapPaymentPayeeDetails":{"name":"CapPaymentPayeeDetails","children":{"CapPaymentMethodDetails":{"targetName":"CapPaymentMethodDetails","navigationExpression":{"expressionString":"paymentMethodDetails","originalExpressionString":"paymentMethodDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"paymentMethodDetails":{"name":"paymentMethodDetails","fieldType":"CapPaymentMethodDetails","fieldPath":"paymentMethodDetails","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"PaymentDetailAwareSettlementResult":{"name":"PaymentDetailAwareSettlementResult","children":{"Period":{"targetName":"Period","navigationExpression":{"expressionString":"allocationPeriod","originalExpressionString":"allocationPeriod","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"allocationPeriod":{"name":"allocationPeriod","fieldType":"Period","fieldPath":"allocationPeriod","cardinality":"SINGLE","forbidTarget":true},"initiatePayment":{"name":"initiatePayment","fieldType":"BOOLEAN","fieldPath":"initiatePayment","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ExternalReferenceHolder":{"name":"ExternalReferenceHolder","children":{"ExternalReference":{"targetName":"ExternalReference","navigationExpression":{"expressionString":"externalReference","originalExpressionString":"externalReference","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"externalReference":{"name":"externalReference","fieldType":"ExternalReference","fieldPath":"externalReference","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapBalance":{"name":"CapBalance","children":{"CapBalanceSuspenseItem":{"targetName":"CapBalanceSuspenseItem","navigationExpression":{"expressionString":"suspenseItems","originalExpressionString":"suspenseItems","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapBalanceItem":{"targetName":"CapBalanceItem","navigationExpression":{"expressionString":"balanceItems","originalExpressionString":"balanceItems","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"totalBalanceAmount":{"name":"totalBalanceAmount","fieldType":"MONEY","fieldPath":"totalBalanceAmount","cardinality":"SINGLE"},"balanceItems":{"name":"balanceItems","fieldType":"CapBalanceItem","fieldPath":"balanceItems","cardinality":"MULTIPLE","forbidTarget":true},"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"},"suspenseItems":{"name":"suspenseItems","fieldType":"CapBalanceSuspenseItem","fieldPath":"suspenseItems","cardinality":"MULTIPLE","forbidTarget":true},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"Generatable":{"name":"Generatable","children":{},"fields":{"isGenerated":{"name":"isGenerated","fieldType":"BOOLEAN","fieldPath":"isGenerated","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"PartyInfo":{"name":"PartyInfo","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"lastName":{"name":"lastName","fieldType":"STRING","fieldPath":"lastName","cardinality":"SINGLE"},"city":{"name":"city","fieldType":"STRING","fieldPath":"city","cardinality":"MULTIPLE"},"stateProvinceCd":{"name":"stateProvinceCd","fieldType":"STRING","fieldPath":"stateProvinceCd","cardinality":"MULTIPLE"},"postalCode":{"name":"postalCode","fieldType":"STRING","fieldPath":"postalCode","cardinality":"MULTIPLE"},"legalId":{"name":"legalId","fieldType":"STRING","fieldPath":"legalId","cardinality":"SINGLE"},"customerNumber":{"name":"customerNumber","fieldType":"STRING","fieldPath":"customerNumber","cardinality":"SINGLE"},"legalName":{"name":"legalName","fieldType":"STRING","fieldPath":"legalName","cardinality":"SINGLE"},"firstName":{"name":"firstName","fieldType":"STRING","fieldPath":"firstName","cardinality":"SINGLE"},"phoneNumber":{"name":"phoneNumber","fieldType":"STRING","fieldPath":"phoneNumber","cardinality":"MULTIPLE"},"streetAddress":{"name":"streetAddress","fieldType":"STRING","fieldPath":"streetAddress","cardinality":"MULTIPLE"},"taxId":{"name":"taxId","fieldType":"STRING","fieldPath":"taxId","cardinality":"SINGLE"},"customerId":{"name":"customerId","fieldType":"STRING","fieldPath":"customerId","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"email":{"name":"email","fieldType":"STRING","fieldPath":"email","cardinality":"MULTIPLE"}},"inheritedContexts":["Retriable","MessageTypeHolder"],"system":false},"CapDentalFeeRateEntity":{"name":"CapDentalFeeRateEntity","children":{},"fields":{"feeAmount":{"name":"feeAmount","fieldType":"MONEY","fieldPath":"feeAmount","cardinality":"SINGLE"},"feeCode":{"name":"feeCode","fieldType":"STRING","fieldPath":"feeCode","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentMethodDetails":{"name":"CapPaymentMethodDetails","children":{},"fields":{},"inheritedContexts":[],"system":false},"CapPaymentAdditionTemplate":{"name":"CapPaymentAdditionTemplate","children":{},"fields":{"additionSource":{"name":"additionSource","fieldType":"ExternalLink","fieldPath":"additionSource","cardinality":"SINGLE"},"additionNumber":{"name":"additionNumber","fieldType":"STRING","fieldPath":"additionNumber","cardinality":"SINGLE"},"additionType":{"name":"additionType","fieldType":"STRING","fieldPath":"additionType","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentAddition"],"system":false},"CapPolicyAware":{"name":"CapPolicyAware","children":{},"fields":{"policyIds":{"name":"policyIds","fieldType":"STRING","fieldPath":"policyIds","cardinality":"MULTIPLE"}},"inheritedContexts":[],"system":false},"CapDentalProviderDiscountEntity":{"name":"CapDentalProviderDiscountEntity","children":{},"fields":{"discountPercentage":{"name":"discountPercentage","fieldType":"DECIMAL","fieldPath":"discountPercentage","cardinality":"SINGLE"},"discountName":{"name":"discountName","fieldType":"STRING","fieldPath":"discountName","cardinality":"SINGLE"},"discountAmount":{"name":"discountAmount","fieldType":"MONEY","fieldPath":"discountAmount","cardinality":"SINGLE"},"discountType":{"name":"discountType","fieldType":"STRING","fieldPath":"discountType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapClaimHeaderPolicy":{"name":"CapClaimHeaderPolicy","children":{},"fields":{"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"},"productCd":{"name":"productCd","fieldType":"STRING","fieldPath":"productCd","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPolicyAware"],"system":false},"CoverableItemCondition":{"name":"CoverableItemCondition","children":{},"fields":{},"inheritedContexts":[],"system":false},"Eligibility":{"name":"Eligibility","children":{"CoverableItemHolder":{"targetName":"CoverableItemHolder","navigationExpression":{"expressionString":"itemHolder","originalExpressionString":"itemHolder","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"itemHolder":{"name":"itemHolder","fieldType":"CoverableItemHolder","fieldPath":"itemHolder","cardinality":"SINGLE","forbidTarget":true},"eligibilityCd":{"name":"eligibilityCd","fieldType":"STRING","fieldPath":"eligibilityCd","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["MessageTypeHolder"],"system":false},"CompositeCoverableItemCondition":{"name":"CompositeCoverableItemCondition","children":{"CoverableItem":{"targetName":"CoverableItem","navigationExpression":{"expressionString":"items","originalExpressionString":"items","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"items":{"name":"items","fieldType":"CoverableItem","fieldPath":"items","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["CoverableItemCondition"],"system":false},"ExternalClaimDataHolder":{"name":"ExternalClaimDataHolder","children":{"ExternalClaimData":{"targetName":"ExternalClaimData","navigationExpression":{"expressionString":"externalClaims","originalExpressionString":"externalClaims","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"externalClaims":{"name":"externalClaims","fieldType":"ExternalClaimData","fieldPath":"externalClaims","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapPaymentTax":{"name":"CapPaymentTax","children":{},"fields":{"taxSource":{"name":"taxSource","fieldType":"ExternalLink","fieldPath":"taxSource","cardinality":"SINGLE"},"taxNumber":{"name":"taxNumber","fieldType":"STRING","fieldPath":"taxNumber","cardinality":"SINGLE"},"taxType":{"name":"taxType","fieldType":"STRING","fieldPath":"taxType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyMajorLimitationsEntity":{"name":"CapPolicyMajorLimitationsEntity","children":{},"fields":{"majorCrowns":{"name":"majorCrowns","fieldType":"STRING","fieldPath":"majorCrowns","cardinality":"SINGLE"},"majorDentureAdjustments":{"name":"majorDentureAdjustments","fieldType":"STRING","fieldPath":"majorDentureAdjustments","cardinality":"SINGLE"},"majorImplants":{"name":"majorImplants","fieldType":"STRING","fieldPath":"majorImplants","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"AccessTrackInfo":{"name":"AccessTrackInfo","children":{},"fields":{"updatedBy":{"name":"updatedBy","fieldType":"STRING","fieldPath":"updatedBy","cardinality":"SINGLE"},"createdBy":{"name":"createdBy","fieldType":"STRING","fieldPath":"createdBy","cardinality":"SINGLE"},"updatedOn":{"name":"updatedOn","fieldType":"DATETIME","fieldPath":"updatedOn","cardinality":"SINGLE"},"createdOn":{"name":"createdOn","fieldType":"DATETIME","fieldPath":"createdOn","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ClaimPolicyAware":{"name":"ClaimPolicyAware","children":{},"fields":{"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalPatientEntity":{"name":"CapDentalPatientEntity","children":{"CapDentalAccumulatorEntity":{"targetName":"CapDentalAccumulatorEntity","navigationExpression":{"expressionString":"accumulators","originalExpressionString":"accumulators","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalProcedureEntity":{"targetName":"CapDentalProcedureEntity","navigationExpression":{"expressionString":"historyProcedures","originalExpressionString":"historyProcedures","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"disabilities":{"name":"disabilities","fieldType":"STRING","fieldPath":"disabilities","cardinality":"MULTIPLE"},"patientID":{"name":"patientID","fieldType":"STRING","fieldPath":"patientID","cardinality":"SINGLE"},"historyProcedures":{"name":"historyProcedures","fieldType":"CapDentalProcedureEntity","fieldPath":"historyProcedures","cardinality":"MULTIPLE","forbidTarget":true},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"},"accumulators":{"name":"accumulators","fieldType":"CapDentalAccumulatorEntity","fieldPath":"accumulators","cardinality":"MULTIPLE","forbidTarget":true},"birthDate":{"name":"birthDate","fieldType":"DATE","fieldPath":"birthDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapClaimEvent":{"name":"CapClaimEvent","children":{},"fields":{},"inheritedContexts":[],"system":false},"ClaimReserveAmounts":{"name":"ClaimReserveAmounts","children":{},"fields":{"expenseAmount":{"name":"expenseAmount","fieldType":"MONEY","fieldPath":"expenseAmount","cardinality":"SINGLE"},"indemnityAmount":{"name":"indemnityAmount","fieldType":"MONEY","fieldPath":"indemnityAmount","cardinality":"SINGLE"},"recoveryAmount":{"name":"recoveryAmount","fieldType":"MONEY","fieldPath":"recoveryAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationTemplate":{"name":"CapPaymentAllocationTemplate","children":{"CapPaymentAllocationTemplateLossInfo":{"targetName":"CapPaymentAllocationTemplateLossInfo","navigationExpression":{"expressionString":"allocationLossInfo","originalExpressionString":"allocationLossInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPaymentAllocationTemplatePayeeDetails":{"targetName":"CapPaymentAllocationTemplatePayeeDetails","navigationExpression":{"expressionString":"allocationPayeeDetails","originalExpressionString":"allocationPayeeDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"allocationLobCd":{"name":"allocationLobCd","fieldType":"STRING","fieldPath":"allocationLobCd","cardinality":"SINGLE"},"allocationPayeeDetails":{"name":"allocationPayeeDetails","fieldType":"CapPaymentAllocationTemplatePayeeDetails","fieldPath":"allocationPayeeDetails","cardinality":"SINGLE","forbidTarget":true},"allocationLossInfo":{"name":"allocationLossInfo","fieldType":"CapPaymentAllocationTemplateLossInfo","fieldPath":"allocationLossInfo","cardinality":"SINGLE","forbidTarget":true},"allocationSource":{"name":"allocationSource","fieldType":"ExternalLink","fieldPath":"allocationSource","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalSettlementDetailEntity":{"name":"CapDentalSettlementDetailEntity","children":{"CapDentalServiceOverrideEntity":{"targetName":"CapDentalServiceOverrideEntity","navigationExpression":{"expressionString":"serviceOverrides","originalExpressionString":"serviceOverrides","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalClaimOverrideEntity":{"targetName":"CapDentalClaimOverrideEntity","navigationExpression":{"expressionString":"claimOverride","originalExpressionString":"claimOverride","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"claimOverride":{"name":"claimOverride","fieldType":"CapDentalClaimOverrideEntity","fieldPath":"claimOverride","cardinality":"SINGLE","forbidTarget":true},"overrideCd":{"name":"overrideCd","fieldType":"STRING","fieldPath":"overrideCd","cardinality":"SINGLE"},"serviceOverrides":{"name":"serviceOverrides","fieldType":"CapDentalServiceOverrideEntity","fieldPath":"serviceOverrides","cardinality":"MULTIPLE","forbidTarget":true},"eobFreeFormMessage":{"name":"eobFreeFormMessage","fieldType":"STRING","fieldPath":"eobFreeFormMessage","cardinality":"SINGLE"}},"inheritedContexts":["CapSettlementDetail"],"system":false},"Accumulative":{"name":"Accumulative","children":{},"fields":{"accumulatorType":{"name":"accumulatorType","fieldType":"STRING","fieldPath":"accumulatorType","cardinality":"SINGLE"},"amount":{"name":"amount","fieldType":"DECIMAL","fieldPath":"amount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"MessageType":{"name":"MessageType","children":{},"fields":{"severity":{"name":"severity","fieldType":"STRING","fieldPath":"severity","cardinality":"SINGLE"},"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"},"source":{"name":"source","fieldType":"STRING","fieldPath":"source","cardinality":"SINGLE"},"message":{"name":"message","fieldType":"STRING","fieldPath":"message","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CompensationSchedule":{"name":"CompensationSchedule","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"scheduleTypeCd":{"name":"scheduleTypeCd","fieldType":"STRING","fieldPath":"scheduleTypeCd","cardinality":"SINGLE"},"schedule":{"name":"schedule","fieldType":"STRING","fieldPath":"schedule","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"initiatePayment":{"name":"initiatePayment","fieldType":"BOOLEAN","fieldPath":"initiatePayment","cardinality":"SINGLE"},"paymentAmount":{"name":"paymentAmount","fieldType":"MONEY","fieldPath":"paymentAmount","cardinality":"SINGLE"}},"inheritedContexts":["MessageTypeHolder"],"system":false},"CapPolicyServiceCategoryEntity":{"name":"CapPolicyServiceCategoryEntity","children":{},"fields":{"scFillings":{"name":"scFillings","fieldType":"STRING","fieldPath":"scFillings","cardinality":"SINGLE"},"scAllOtherRadiographs":{"name":"scAllOtherRadiographs","fieldType":"STRING","fieldPath":"scAllOtherRadiographs","cardinality":"SINGLE"},"scImplantServices":{"name":"scImplantServices","fieldType":"STRING","fieldPath":"scImplantServices","cardinality":"SINGLE"},"scSurgicalPeriodontics":{"name":"scSurgicalPeriodontics","fieldType":"STRING","fieldPath":"scSurgicalPeriodontics","cardinality":"SINGLE"},"scCrowns":{"name":"scCrowns","fieldType":"STRING","fieldPath":"scCrowns","cardinality":"SINGLE"},"scFullMouthRadiographs":{"name":"scFullMouthRadiographs","fieldType":"STRING","fieldPath":"scFullMouthRadiographs","cardinality":"SINGLE"},"scFluorides":{"name":"scFluorides","fieldType":"STRING","fieldPath":"scFluorides","cardinality":"SINGLE"},"scBitewingRadiographs":{"name":"scBitewingRadiographs","fieldType":"STRING","fieldPath":"scBitewingRadiographs","cardinality":"SINGLE"},"scStainlessSteelCrowns":{"name":"scStainlessSteelCrowns","fieldType":"STRING","fieldPath":"scStainlessSteelCrowns","cardinality":"SINGLE"},"scOralEvaluations":{"name":"scOralEvaluations","fieldType":"STRING","fieldPath":"scOralEvaluations","cardinality":"SINGLE"},"scRootCanals":{"name":"scRootCanals","fieldType":"STRING","fieldPath":"scRootCanals","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapSettlementAbsenceInfo":{"name":"CapSettlementAbsenceInfo","children":{},"fields":{},"inheritedContexts":[],"system":false},"ClaimSubjectInfo":{"name":"ClaimSubjectInfo","children":{},"fields":{"subject":{"name":"subject","fieldType":"STRING","fieldPath":"subject","cardinality":"SINGLE"},"subjectId":{"name":"subjectId","fieldType":"STRING","fieldPath":"subjectId","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"PreExistingCoverableItemCondition":{"name":"PreExistingCoverableItemCondition","children":{},"fields":{"duration":{"name":"duration","fieldType":"INTEGER","fieldPath":"duration","cardinality":"SINGLE"},"continuouslyInsuredPeriod":{"name":"continuouslyInsuredPeriod","fieldType":"INTEGER","fieldPath":"continuouslyInsuredPeriod","cardinality":"SINGLE"},"lookBackPeriod":{"name":"lookBackPeriod","fieldType":"INTEGER","fieldPath":"lookBackPeriod","cardinality":"SINGLE"},"isApplied":{"name":"isApplied","fieldType":"BOOLEAN","fieldPath":"isApplied","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"treatmentFreePeriod":{"name":"treatmentFreePeriod","fieldType":"INTEGER","fieldPath":"treatmentFreePeriod","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition"],"system":false},"CapDentalBaseBalanceItemAllocationTax":{"name":"CapDentalBaseBalanceItemAllocationTax","children":{},"fields":{"taxSubType":{"name":"taxSubType","fieldType":"STRING","fieldPath":"taxSubType","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"},"taxType":{"name":"taxType","fieldType":"STRING","fieldPath":"taxType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"Term":{"name":"Term","children":{},"fields":{"effectiveDate":{"name":"effectiveDate","fieldType":"DATETIME","fieldPath":"effectiveDate","cardinality":"SINGLE"},"expirationDate":{"name":"expirationDate","fieldType":"DATETIME","fieldPath":"expirationDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyEligibilityEntity":{"name":"CapPolicyEligibilityEntity","children":{},"fields":{"waitingPeriodAmount":{"name":"waitingPeriodAmount","fieldType":"INTEGER","fieldPath":"waitingPeriodAmount","cardinality":"SINGLE"},"waitingPeriodModeCd":{"name":"waitingPeriodModeCd","fieldType":"STRING","fieldPath":"waitingPeriodModeCd","cardinality":"SINGLE"},"eligibilityTypeCd":{"name":"eligibilityTypeCd","fieldType":"STRING","fieldPath":"eligibilityTypeCd","cardinality":"SINGLE"},"waitingPeriodDefCd":{"name":"waitingPeriodDefCd","fieldType":"STRING","fieldPath":"waitingPeriodDefCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapTimelinePeriod":{"name":"CapTimelinePeriod","children":{},"fields":{"endDate":{"name":"endDate","fieldType":"DATETIME","fieldPath":"endDate","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"startDate":{"name":"startDate","fieldType":"DATETIME","fieldPath":"startDate","cardinality":"SINGLE"}},"inheritedContexts":["Period"],"system":false},"CapDentalPreauthorizationEntity":{"name":"CapDentalPreauthorizationEntity","children":{"Period":{"targetName":"Period","navigationExpression":{"expressionString":"authorizationPeriod","originalExpressionString":"authorizationPeriod","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"authorizedBy":{"name":"authorizedBy","fieldType":"STRING","fieldPath":"authorizedBy","cardinality":"SINGLE"},"authorizationPeriod":{"name":"authorizationPeriod","fieldType":"Period","fieldPath":"authorizationPeriod","cardinality":"SINGLE"},"isProcedureAuthorized":{"name":"isProcedureAuthorized","fieldType":"BOOLEAN","fieldPath":"isProcedureAuthorized","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalClaimCoordinationOfBenefitsEntity":{"name":"CapDentalClaimCoordinationOfBenefitsEntity","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"period","originalExpressionString":"period","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"period":{"name":"period","fieldType":"Term","fieldPath":"period","cardinality":"SINGLE","forbidTarget":true},"address":{"name":"address","fieldType":"STRING","fieldPath":"address","cardinality":"SINGLE"},"otherPolicyType":{"name":"otherPolicyType","fieldType":"STRING","fieldPath":"otherPolicyType","cardinality":"SINGLE"},"PolicyNumber":{"name":"PolicyNumber","fieldType":"STRING","fieldPath":"PolicyNumber","cardinality":"SINGLE"},"otherInsuranceCompany":{"name":"otherInsuranceCompany","fieldType":"STRING","fieldPath":"otherInsuranceCompany","cardinality":"SINGLE"},"policyholderFirstName":{"name":"policyholderFirstName","fieldType":"STRING","fieldPath":"policyholderFirstName","cardinality":"SINGLE"},"otherCoverageType":{"name":"otherCoverageType","fieldType":"STRING","fieldPath":"otherCoverageType","cardinality":"SINGLE"},"typeOfCob":{"name":"typeOfCob","fieldType":"STRING","fieldPath":"typeOfCob","cardinality":"SINGLE"},"policyholderRelationshipToPatient":{"name":"policyholderRelationshipToPatient","fieldType":"STRING","fieldPath":"policyholderRelationshipToPatient","cardinality":"SINGLE"},"policyholderGender":{"name":"policyholderGender","fieldType":"STRING","fieldPath":"policyholderGender","cardinality":"SINGLE"},"policyholderDateOfBirth":{"name":"policyholderDateOfBirth","fieldType":"DATE","fieldPath":"policyholderDateOfBirth","cardinality":"SINGLE"},"plan":{"name":"plan","fieldType":"STRING","fieldPath":"plan","cardinality":"SINGLE"},"policyholderLastName":{"name":"policyholderLastName","fieldType":"STRING","fieldPath":"policyholderLastName","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"RestrictedCoverableItemCondition":{"name":"RestrictedCoverableItemCondition","children":{},"fields":{"restriction":{"name":"restriction","fieldType":"STRING","fieldPath":"restriction","cardinality":"SINGLE"},"appliesTo":{"name":"appliesTo","fieldType":"STRING","fieldPath":"appliesTo","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition"],"system":false},"CapPaymentReductionTemplate":{"name":"CapPaymentReductionTemplate","children":{},"fields":{"reductionNumber":{"name":"reductionNumber","fieldType":"STRING","fieldPath":"reductionNumber","cardinality":"SINGLE"},"reductionSource":{"name":"reductionSource","fieldType":"ExternalLink","fieldPath":"reductionSource","cardinality":"SINGLE"},"reductionType":{"name":"reductionType","fieldType":"STRING","fieldPath":"reductionType","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentReduction"],"system":false},"CapDentalProcedureEntity":{"name":"CapDentalProcedureEntity","children":{"CapDentalPreauthorizationEntity":{"targetName":"CapDentalPreauthorizationEntity","navigationExpression":{"expressionString":"preauthorization","originalExpressionString":"preauthorization","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalPolicyInfoEntity":{"targetName":"CapDentalPolicyInfoEntity","navigationExpression":{"expressionString":"[__dataObject__.certPolicyInfo,__dataObject__.masterPolicyInfo]","originalExpressionString":"{certPolicyInfo, masterPolicyInfo}","expressionType":"COMPLEX","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalOrthodonticEntity":{"targetName":"CapDentalOrthodonticEntity","navigationExpression":{"expressionString":"ortho","originalExpressionString":"ortho","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalFeeRateEntity":{"targetName":"CapDentalFeeRateEntity","navigationExpression":{"expressionString":"[__dataObject__.feeSchedule,__dataObject__.feeUCR,__dataObject__.feeUCRME]","originalExpressionString":"{feeSchedule, feeUCR, feeUCRME}","expressionType":"COMPLEX","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalProcedureCoordinationOfBenefitsEntity":{"targetName":"CapDentalProcedureCoordinationOfBenefitsEntity","navigationExpression":{"expressionString":"cob","originalExpressionString":"cob","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalPPOFrequencyEntity":{"targetName":"CapDentalPPOFrequencyEntity","navigationExpression":{"expressionString":"frequencyPPO","originalExpressionString":"frequencyPPO","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalDiagnosisCodeEntity":{"targetName":"CapDentalDiagnosisCodeEntity","navigationExpression":{"expressionString":"diagnosisCodes","originalExpressionString":"diagnosisCodes","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalDentistEntity":{"targetName":"CapDentalDentistEntity","navigationExpression":{"expressionString":"dentist","originalExpressionString":"dentist","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalDHMOFrequencyEntity":{"targetName":"CapDentalDHMOFrequencyEntity","navigationExpression":{"expressionString":"frequencyDHMO","originalExpressionString":"frequencyDHMO","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalCalculationStatusEntity":{"targetName":"CapDentalCalculationStatusEntity","navigationExpression":{"expressionString":"status","originalExpressionString":"status","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalConsultantReviewEntity":{"targetName":"CapDentalConsultantReviewEntity","navigationExpression":{"expressionString":"consultantReview","originalExpressionString":"consultantReview","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"procedureType":{"name":"procedureType","fieldType":"STRING","fieldPath":"procedureType","cardinality":"SINGLE"},"procedureCode":{"name":"procedureCode","fieldType":"STRING","fieldPath":"procedureCode","cardinality":"SINGLE"},"preauthorization":{"name":"preauthorization","fieldType":"CapDentalPreauthorizationEntity","fieldPath":"preauthorization","cardinality":"SINGLE","forbidTarget":true},"description":{"name":"description","fieldType":"STRING","fieldPath":"description","cardinality":"SINGLE"},"diagnosisCodes":{"name":"diagnosisCodes","fieldType":"CapDentalDiagnosisCodeEntity","fieldPath":"diagnosisCodes","cardinality":"MULTIPLE","forbidTarget":true},"submittedFeeUCR":{"name":"submittedFeeUCR","fieldType":"MONEY","fieldPath":"submittedFeeUCR","cardinality":"SINGLE"},"submittedFeeSchedule":{"name":"submittedFeeSchedule","fieldType":"MONEY","fieldPath":"submittedFeeSchedule","cardinality":"SINGLE"},"preauthorizationNumber":{"name":"preauthorizationNumber","fieldType":"STRING","fieldPath":"preauthorizationNumber","cardinality":"SINGLE"},"surfaces":{"name":"surfaces","fieldType":"STRING","fieldPath":"surfaces","cardinality":"MULTIPLE"},"coveredFeeUCRME":{"name":"coveredFeeUCRME","fieldType":"MONEY","fieldPath":"coveredFeeUCRME","cardinality":"SINGLE"},"predetInd":{"name":"predetInd","fieldType":"BOOLEAN","fieldPath":"predetInd","cardinality":"SINGLE"},"frequencyPPO":{"name":"frequencyPPO","fieldType":"CapDentalPPOFrequencyEntity","fieldPath":"frequencyPPO","cardinality":"SINGLE","forbidTarget":true},"priorProsthesisPlacementDate":{"name":"priorProsthesisPlacementDate","fieldType":"DATE","fieldPath":"priorProsthesisPlacementDate","cardinality":"SINGLE"},"procedureStatus":{"name":"procedureStatus","fieldType":"STRING","fieldPath":"procedureStatus","cardinality":"SINGLE"},"toothSystem":{"name":"toothSystem","fieldType":"STRING","fieldPath":"toothSystem","cardinality":"SINGLE"},"feeUCRME":{"name":"feeUCRME","fieldType":"CapDentalFeeRateEntity","fieldPath":"feeUCRME","cardinality":"MULTIPLE","forbidTarget":true},"quantity":{"name":"quantity","fieldType":"INTEGER","fieldPath":"quantity","cardinality":"SINGLE"},"feeSchedule":{"name":"feeSchedule","fieldType":"CapDentalFeeRateEntity","fieldPath":"feeSchedule","cardinality":"MULTIPLE","forbidTarget":true},"ortho":{"name":"ortho","fieldType":"CapDentalOrthodonticEntity","fieldPath":"ortho","cardinality":"SINGLE"},"consultantReview":{"name":"consultantReview","fieldType":"CapDentalConsultantReviewEntity","fieldPath":"consultantReview","cardinality":"SINGLE","forbidTarget":true},"dentist":{"name":"dentist","fieldType":"CapDentalDentistEntity","fieldPath":"dentist","cardinality":"SINGLE","forbidTarget":true},"feeUCR":{"name":"feeUCR","fieldType":"CapDentalFeeRateEntity","fieldPath":"feeUCR","cardinality":"MULTIPLE","forbidTarget":true},"coveredFeeUCR":{"name":"coveredFeeUCR","fieldType":"MONEY","fieldPath":"coveredFeeUCR","cardinality":"SINGLE"},"lossNumber":{"name":"lossNumber","fieldType":"STRING","fieldPath":"lossNumber","cardinality":"SINGLE"},"submittedFeeUCRME":{"name":"submittedFeeUCRME","fieldType":"MONEY","fieldPath":"submittedFeeUCRME","cardinality":"SINGLE"},"toothArea":{"name":"toothArea","fieldType":"STRING","fieldPath":"toothArea","cardinality":"SINGLE"},"masterPolicyInfo":{"name":"masterPolicyInfo","fieldType":"CapDentalPolicyInfoEntity","fieldPath":"masterPolicyInfo","cardinality":"MULTIPLE","forbidTarget":true},"cob":{"name":"cob","fieldType":"CapDentalProcedureCoordinationOfBenefitsEntity","fieldPath":"cob","cardinality":"SINGLE","forbidTarget":true},"submittedFee":{"name":"submittedFee","fieldType":"MONEY","fieldPath":"submittedFee","cardinality":"SINGLE"},"toothCodes":{"name":"toothCodes","fieldType":"STRING","fieldPath":"toothCodes","cardinality":"MULTIPLE"},"coveredFeeSchedule":{"name":"coveredFeeSchedule","fieldType":"MONEY","fieldPath":"coveredFeeSchedule","cardinality":"SINGLE"},"frequencyDHMO":{"name":"frequencyDHMO","fieldType":"CapDentalDHMOFrequencyEntity","fieldPath":"frequencyDHMO","cardinality":"SINGLE","forbidTarget":true},"dateOfService":{"name":"dateOfService","fieldType":"DATE","fieldPath":"dateOfService","cardinality":"SINGLE"},"paidAmount":{"name":"paidAmount","fieldType":"MONEY","fieldPath":"paidAmount","cardinality":"SINGLE"},"certPolicyInfo":{"name":"certPolicyInfo","fieldType":"CapDentalPolicyInfoEntity","fieldPath":"certPolicyInfo","cardinality":"SINGLE","forbidTarget":true},"status":{"name":"status","fieldType":"CapDentalCalculationStatusEntity","fieldPath":"status","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["CapDentalBaseProcedure"],"system":false},"CapBalanceItemAllocation":{"name":"CapBalanceItemAllocation","children":{"CapBalanceItemAllocationTax":{"targetName":"CapBalanceItemAllocationTax","navigationExpression":{"expressionString":"allocationTaxes","originalExpressionString":"allocationTaxes","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapBalanceItemAllocationReduction":{"targetName":"CapBalanceItemAllocationReduction","navigationExpression":{"expressionString":"allocationReductions","originalExpressionString":"allocationReductions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapBalanceItemAllocationAddition":{"targetName":"CapBalanceItemAllocationAddition","navigationExpression":{"expressionString":"allocationAdditions","originalExpressionString":"allocationAdditions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentAllocationPayableItem":{"targetName":"CapPaymentAllocationPayableItem","navigationExpression":{"expressionString":"allocationPayableItem","originalExpressionString":"allocationPayableItem","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"allocationGrossAmount":{"name":"allocationGrossAmount","fieldType":"MONEY","fieldPath":"allocationGrossAmount","cardinality":"SINGLE"},"allocationReductions":{"name":"allocationReductions","fieldType":"CapBalanceItemAllocationReduction","fieldPath":"allocationReductions","cardinality":"MULTIPLE","forbidTarget":true},"allocationTaxes":{"name":"allocationTaxes","fieldType":"CapBalanceItemAllocationTax","fieldPath":"allocationTaxes","cardinality":"MULTIPLE","forbidTarget":true},"allocationSource":{"name":"allocationSource","fieldType":"ExternalLink","fieldPath":"allocationSource","cardinality":"SINGLE"},"allocationAdditions":{"name":"allocationAdditions","fieldType":"CapBalanceItemAllocationAddition","fieldPath":"allocationAdditions","cardinality":"MULTIPLE","forbidTarget":true},"allocationPayableItem":{"name":"allocationPayableItem","fieldType":"CapPaymentAllocationPayableItem","fieldPath":"allocationPayableItem","cardinality":"SINGLE","forbidTarget":true},"allocationNetAmount":{"name":"allocationNetAmount","fieldType":"MONEY","fieldPath":"allocationNetAmount","cardinality":"SINGLE"},"isInterestOnly":{"name":"isInterestOnly","fieldType":"BOOLEAN","fieldPath":"isInterestOnly","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"DelayableCoverableItemCondition":{"name":"DelayableCoverableItemCondition","children":{},"fields":{"delay":{"name":"delay","fieldType":"STRING","fieldPath":"delay","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition"],"system":false},"CapDentalMaximumEntity":{"name":"CapDentalMaximumEntity","children":{},"fields":{"individualBasicINNAnnualMaximum":{"name":"individualBasicINNAnnualMaximum","fieldType":"MONEY","fieldPath":"individualBasicINNAnnualMaximum","cardinality":"SINGLE"},"implantsINNAnnualMaximum":{"name":"implantsINNAnnualMaximum","fieldType":"MONEY","fieldPath":"implantsINNAnnualMaximum","cardinality":"SINGLE"},"orthoINNAnnualMaximum":{"name":"orthoINNAnnualMaximum","fieldType":"MONEY","fieldPath":"orthoINNAnnualMaximum","cardinality":"SINGLE"},"individualPreventiveINNAnnualMaximum":{"name":"individualPreventiveINNAnnualMaximum","fieldType":"MONEY","fieldPath":"individualPreventiveINNAnnualMaximum","cardinality":"SINGLE"},"individualMajorINNAnnualMaximum":{"name":"individualMajorINNAnnualMaximum","fieldType":"MONEY","fieldPath":"individualMajorINNAnnualMaximum","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapSettlementLossInfo":{"name":"CapSettlementLossInfo","children":{},"fields":{},"inheritedContexts":[],"system":false},"ExternalCapClaimHeader":{"name":"ExternalCapClaimHeader","children":{"CapClaimHeaderCase":{"targetName":"CapClaimHeaderCase","navigationExpression":{"expressionString":"claimCase","originalExpressionString":"claimCase","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"ClaimSubjectInfo":{"targetName":"ClaimSubjectInfo","navigationExpression":{"expressionString":"subjects","originalExpressionString":"subjects","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"ClaimantAware":{"targetName":"ClaimantAware","navigationExpression":{"expressionString":"claimant","originalExpressionString":"claimant","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapClaimHeaderPolicy":{"targetName":"CapClaimHeaderPolicy","navigationExpression":{"expressionString":"policies","originalExpressionString":"policies","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"InsuredAware":{"targetName":"InsuredAware","navigationExpression":{"expressionString":"insured","originalExpressionString":"insured","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"insured":{"name":"insured","fieldType":"InsuredAware","fieldPath":"insured","cardinality":"SINGLE","forbidTarget":true},"subjects":{"name":"subjects","fieldType":"ClaimSubjectInfo","fieldPath":"subjects","cardinality":"MULTIPLE","forbidTarget":true},"policies":{"name":"policies","fieldType":"CapClaimHeaderPolicy","fieldPath":"policies","cardinality":"MULTIPLE","forbidTarget":true},"totalIncurred":{"name":"totalIncurred","fieldType":"MONEY","fieldPath":"totalIncurred","cardinality":"SINGLE"},"claimType":{"name":"claimType","fieldType":"STRING","fieldPath":"claimType","cardinality":"SINGLE"},"claimCase":{"name":"claimCase","fieldType":"CapClaimHeaderCase","fieldPath":"claimCase","cardinality":"SINGLE","forbidTarget":true},"claimDate":{"name":"claimDate","fieldType":"DATETIME","fieldPath":"claimDate","cardinality":"SINGLE"},"caseNumber":{"name":"caseNumber","fieldType":"STRING","fieldPath":"caseNumber","cardinality":"SINGLE"},"reportedDate":{"name":"reportedDate","fieldType":"DATETIME","fieldPath":"reportedDate","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"claimModelName":{"name":"claimModelName","fieldType":"STRING","fieldPath":"claimModelName","cardinality":"SINGLE"},"claimant":{"name":"claimant","fieldType":"ClaimantAware","fieldPath":"claimant","cardinality":"SINGLE","forbidTarget":true},"claimNumber":{"name":"claimNumber","fieldType":"STRING","fieldPath":"claimNumber","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyHolder":{"name":"CapPolicyHolder","children":{"CapPolicyInfo":{"targetName":"CapPolicyInfo","navigationExpression":{"expressionString":"policies","originalExpressionString":"policies","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"policies":{"name":"policies","fieldType":"CapPolicyInfo","fieldPath":"policies","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapFrequencyConfiguration":{"name":"CapFrequencyConfiguration","children":{},"fields":{"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalBaseBalanceItemAllocationReduction":{"name":"CapDentalBaseBalanceItemAllocationReduction","children":{},"fields":{"reductionSubType":{"name":"reductionSubType","fieldType":"STRING","fieldPath":"reductionSubType","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"},"reductionType":{"name":"reductionType","fieldType":"STRING","fieldPath":"reductionType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalBalance":{"name":"CapDentalBalance","children":{},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"CapDentalPolicyInfoEntity":{"name":"CapDentalPolicyInfoEntity","children":{"CapPolicyEligibilityEntity":{"targetName":"CapPolicyEligibilityEntity","navigationExpression":{"expressionString":"coverageEligibility","originalExpressionString":"coverageEligibility","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalMaximumEntity":{"targetName":"CapDentalMaximumEntity","navigationExpression":{"expressionString":"dentalMaximums","originalExpressionString":"dentalMaximums","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDeductibleDetailEntity":{"targetName":"CapDeductibleDetailEntity","navigationExpression":{"expressionString":"deductibleDetails","originalExpressionString":"deductibleDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalPolicyInfoInsuredDetailsEntity":{"targetName":"CapDentalPolicyInfoInsuredDetailsEntity","navigationExpression":{"expressionString":"insureds","originalExpressionString":"insureds","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity":{"targetName":"CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity","navigationExpression":{"expressionString":"lateEntrantWaitingPeriodsDetails","originalExpressionString":"lateEntrantWaitingPeriodsDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPolicyServiceCategoryEntity":{"targetName":"CapPolicyServiceCategoryEntity","navigationExpression":{"expressionString":"serviceCategory","originalExpressionString":"serviceCategory","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalUnverifiedInfoEntity":{"targetName":"CapDentalUnverifiedInfoEntity","navigationExpression":{"expressionString":"unverifiedInfo","originalExpressionString":"unverifiedInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPolicyFrequencyLimitationsEntity":{"targetName":"CapPolicyFrequencyLimitationsEntity","navigationExpression":{"expressionString":"frequencyLimitations","originalExpressionString":"frequencyLimitations","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalPolicyInfoCoinsuranceEntity":{"targetName":"CapDentalPolicyInfoCoinsuranceEntity","navigationExpression":{"expressionString":"coinsurances","originalExpressionString":"coinsurances","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalTermEntity":{"targetName":"CapDentalTermEntity","navigationExpression":{"expressionString":"pcdTerm","originalExpressionString":"pcdTerm","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"isOrthoCoverageIncluded":{"name":"isOrthoCoverageIncluded","fieldType":"BOOLEAN","fieldPath":"isOrthoCoverageIncluded","cardinality":"SINGLE"},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"},"planName":{"name":"planName","fieldType":"STRING","fieldPath":"planName","cardinality":"SINGLE"},"planCategory":{"name":"planCategory","fieldType":"STRING","fieldPath":"planCategory","cardinality":"SINGLE"},"serviceCategory":{"name":"serviceCategory","fieldType":"CapPolicyServiceCategoryEntity","fieldPath":"serviceCategory","cardinality":"SINGLE","forbidTarget":true},"pcdTerm":{"name":"pcdTerm","fieldType":"CapDentalTermEntity","fieldPath":"pcdTerm","cardinality":"SINGLE","forbidTarget":true},"coinsurances":{"name":"coinsurances","fieldType":"CapDentalPolicyInfoCoinsuranceEntity","fieldPath":"coinsurances","cardinality":"MULTIPLE","forbidTarget":true},"pcdId":{"name":"pcdId","fieldType":"STRING","fieldPath":"pcdId","cardinality":"SINGLE"},"applyLateEntrantBenefitWaitingPeriods":{"name":"applyLateEntrantBenefitWaitingPeriods","fieldType":"BOOLEAN","fieldPath":"applyLateEntrantBenefitWaitingPeriods","cardinality":"SINGLE"},"plan":{"name":"plan","fieldType":"STRING","fieldPath":"plan","cardinality":"SINGLE"},"unverifiedInfo":{"name":"unverifiedInfo","fieldType":"CapDentalUnverifiedInfoEntity","fieldPath":"unverifiedInfo","cardinality":"SINGLE","forbidTarget":true},"childMaxAgeCd":{"name":"childMaxAgeCd","fieldType":"STRING","fieldPath":"childMaxAgeCd","cardinality":"SINGLE"},"orthoINNCoinsurancePercent":{"name":"orthoINNCoinsurancePercent","fieldType":"DECIMAL","fieldPath":"orthoINNCoinsurancePercent","cardinality":"SINGLE"},"orthoChildAgeLimit":{"name":"orthoChildAgeLimit","fieldType":"INTEGER","fieldPath":"orthoChildAgeLimit","cardinality":"SINGLE"},"orthoOONCoinsurancePercent":{"name":"orthoOONCoinsurancePercent","fieldType":"DECIMAL","fieldPath":"orthoOONCoinsurancePercent","cardinality":"SINGLE"},"implantsWaitingPeriod":{"name":"implantsWaitingPeriod","fieldType":"STRING","fieldPath":"implantsWaitingPeriod","cardinality":"SINGLE"},"waitingPeriodApplyTo":{"name":"waitingPeriodApplyTo","fieldType":"STRING","fieldPath":"waitingPeriodApplyTo","cardinality":"SINGLE"},"deductibleDetails":{"name":"deductibleDetails","fieldType":"CapDeductibleDetailEntity","fieldPath":"deductibleDetails","cardinality":"MULTIPLE","forbidTarget":true},"enrollmentTypeCd":{"name":"enrollmentTypeCd","fieldType":"STRING","fieldPath":"enrollmentTypeCd","cardinality":"SINGLE"},"tmjDeductibleType":{"name":"tmjDeductibleType","fieldType":"STRING","fieldPath":"tmjDeductibleType","cardinality":"SINGLE"},"isCosmeticServicesIncluded":{"name":"isCosmeticServicesIncluded","fieldType":"BOOLEAN","fieldPath":"isCosmeticServicesIncluded","cardinality":"SINGLE"},"currencyCd":{"name":"currencyCd","fieldType":"STRING","fieldPath":"currencyCd","cardinality":"SINGLE"},"isVerified":{"name":"isVerified","fieldType":"BOOLEAN","fieldPath":"isVerified","cardinality":"SINGLE"},"basicWaitingPeriod":{"name":"basicWaitingPeriod","fieldType":"STRING","fieldPath":"basicWaitingPeriod","cardinality":"SINGLE"},"policyPaidToDate":{"name":"policyPaidToDate","fieldType":"DATE","fieldPath":"policyPaidToDate","cardinality":"SINGLE"},"policyStatus":{"name":"policyStatus","fieldType":"STRING","fieldPath":"policyStatus","cardinality":"SINGLE"},"dentalMaximums":{"name":"dentalMaximums","fieldType":"CapDentalMaximumEntity","fieldPath":"dentalMaximums","cardinality":"MULTIPLE","forbidTarget":true},"isImplantsMaximumAppliedTowardPlanMaximum":{"name":"isImplantsMaximumAppliedTowardPlanMaximum","fieldType":"BOOLEAN","fieldPath":"isImplantsMaximumAppliedTowardPlanMaximum","cardinality":"SINGLE"},"policyPaidToDateWithGracePeriod":{"name":"policyPaidToDateWithGracePeriod","fieldType":"DATE","fieldPath":"policyPaidToDateWithGracePeriod","cardinality":"SINGLE"},"lateEntrantWaitingPeriodsDetails":{"name":"lateEntrantWaitingPeriodsDetails","fieldType":"CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity","fieldPath":"lateEntrantWaitingPeriodsDetails","cardinality":"MULTIPLE","forbidTarget":true},"majorWaitingPeriod":{"name":"majorWaitingPeriod","fieldType":"STRING","fieldPath":"majorWaitingPeriod","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"cosmeticWaitingPeriod":{"name":"cosmeticWaitingPeriod","fieldType":"STRING","fieldPath":"cosmeticWaitingPeriod","cardinality":"SINGLE"},"txEffectiveDate":{"name":"txEffectiveDate","fieldType":"STRING","fieldPath":"txEffectiveDate","cardinality":"SINGLE"},"orthoWaitingPeriod":{"name":"orthoWaitingPeriod","fieldType":"STRING","fieldPath":"orthoWaitingPeriod","cardinality":"SINGLE"},"frequencyLimitations":{"name":"frequencyLimitations","fieldType":"CapPolicyFrequencyLimitationsEntity","fieldPath":"frequencyLimitations","cardinality":"SINGLE","forbidTarget":true},"orthoDeductibleType":{"name":"orthoDeductibleType","fieldType":"STRING","fieldPath":"orthoDeductibleType","cardinality":"SINGLE"},"insureds":{"name":"insureds","fieldType":"CapDentalPolicyInfoInsuredDetailsEntity","fieldPath":"insureds","cardinality":"MULTIPLE","forbidTarget":true},"orthoLateEntrantWaitingPeriod":{"name":"orthoLateEntrantWaitingPeriod","fieldType":"STRING","fieldPath":"orthoLateEntrantWaitingPeriod","cardinality":"SINGLE"},"isImplantCoverageIncluded":{"name":"isImplantCoverageIncluded","fieldType":"BOOLEAN","fieldPath":"isImplantCoverageIncluded","cardinality":"SINGLE"},"fullTimeStudentAgeCd":{"name":"fullTimeStudentAgeCd","fieldType":"STRING","fieldPath":"fullTimeStudentAgeCd","cardinality":"SINGLE"},"cosmeticDeductibleType":{"name":"cosmeticDeductibleType","fieldType":"STRING","fieldPath":"cosmeticDeductibleType","cardinality":"SINGLE"},"riskStateCd":{"name":"riskStateCd","fieldType":"STRING","fieldPath":"riskStateCd","cardinality":"SINGLE"},"preventWaitingPeriod":{"name":"preventWaitingPeriod","fieldType":"STRING","fieldPath":"preventWaitingPeriod","cardinality":"SINGLE"},"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyType":{"name":"policyType","fieldType":"STRING","fieldPath":"policyType","cardinality":"SINGLE"},"isTmjCoverageIncluded":{"name":"isTmjCoverageIncluded","fieldType":"BOOLEAN","fieldPath":"isTmjCoverageIncluded","cardinality":"SINGLE"},"capPolicyVersionId":{"name":"capPolicyVersionId","fieldType":"STRING","fieldPath":"capPolicyVersionId","cardinality":"SINGLE"},"coverageEligibility":{"name":"coverageEligibility","fieldType":"CapPolicyEligibilityEntity","fieldPath":"coverageEligibility","cardinality":"SINGLE","forbidTarget":true},"orthoAvailability":{"name":"orthoAvailability","fieldType":"STRING","fieldPath":"orthoAvailability","cardinality":"SINGLE"},"productCd":{"name":"productCd","fieldType":"STRING","fieldPath":"productCd","cardinality":"SINGLE"}},"inheritedContexts":["CapPolicyInfo","CapClaimHeaderPolicy","ClaimPolicyAware"],"system":false},"CapDentalResultEntryEntity":{"name":"CapDentalResultEntryEntity","children":{"CapDentalAccumulatorEntity":{"targetName":"CapDentalAccumulatorEntity","navigationExpression":{"expressionString":"reservedAccumulators","originalExpressionString":"reservedAccumulators","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalCalculationResultEntity":{"targetName":"CapDentalCalculationResultEntity","navigationExpression":{"expressionString":"calculationResult","originalExpressionString":"calculationResult","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalCalculationStatusEntity":{"targetName":"CapDentalCalculationStatusEntity","navigationExpression":{"expressionString":"status","originalExpressionString":"status","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"calculationResult":{"name":"calculationResult","fieldType":"CapDentalCalculationResultEntity","fieldPath":"calculationResult","cardinality":"SINGLE","forbidTarget":true},"serviceSource":{"name":"serviceSource","fieldType":"STRING","fieldPath":"serviceSource","cardinality":"SINGLE"},"reservedAccumulators":{"name":"reservedAccumulators","fieldType":"CapDentalAccumulatorEntity","fieldPath":"reservedAccumulators","cardinality":"MULTIPLE","forbidTarget":true},"status":{"name":"status","fieldType":"CapDentalCalculationStatusEntity","fieldPath":"status","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"Claimant":{"name":"Claimant","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyRole","ClaimantAware","ClaimPartyAware"],"system":false},"EntityRef":{"name":"EntityRef","children":{},"fields":{"rootId":{"name":"rootId","fieldType":"STRING","fieldPath":"rootId","cardinality":"SINGLE"},"revisionNo":{"name":"revisionNo","fieldType":"INTEGER","fieldPath":"revisionNo","cardinality":"SINGLE"},"id":{"name":"id","fieldType":"STRING","fieldPath":"id","cardinality":"SINGLE"},"parentId":{"name":"parentId","fieldType":"STRING","fieldPath":"parentId","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationLossInfo":{"name":"CapPaymentAllocationLossInfo","children":{},"fields":{"lossSource":{"name":"lossSource","fieldType":"ExternalLink","fieldPath":"lossSource","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationTemplatePayeeDetails":{"name":"CapPaymentAllocationTemplatePayeeDetails","children":{},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentTemplate":{"name":"CapPaymentTemplate","children":{"CapPaymentDetailsTemplate":{"targetName":"CapPaymentDetailsTemplate","navigationExpression":{"expressionString":"paymentDetailsTemplate","originalExpressionString":"paymentDetailsTemplate","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"},"paymentDetailsTemplate":{"name":"paymentDetailsTemplate","fieldType":"CapPaymentDetailsTemplate","fieldPath":"paymentDetailsTemplate","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["RootEntity"],"system":false},"CapDentalClaimOverrideEntity":{"name":"CapDentalClaimOverrideEntity","children":{"CapDentalOverrideClaimValueEntity":{"targetName":"CapDentalOverrideClaimValueEntity","navigationExpression":{"expressionString":"overrideClaimValue","originalExpressionString":"overrideClaimValue","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalBypassClaimLogicEntity":{"targetName":"CapDentalBypassClaimLogicEntity","navigationExpression":{"expressionString":"bypassClaimLogic","originalExpressionString":"bypassClaimLogic","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalWaiveClaimValueEntity":{"targetName":"CapDentalWaiveClaimValueEntity","navigationExpression":{"expressionString":"waiveClaimValue","originalExpressionString":"waiveClaimValue","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"overrideClaimValue":{"name":"overrideClaimValue","fieldType":"CapDentalOverrideClaimValueEntity","fieldPath":"overrideClaimValue","cardinality":"SINGLE","forbidTarget":true},"waiveClaimValue":{"name":"waiveClaimValue","fieldType":"CapDentalWaiveClaimValueEntity","fieldPath":"waiveClaimValue","cardinality":"SINGLE","forbidTarget":true},"isAllowed":{"name":"isAllowed","fieldType":"BOOLEAN","fieldPath":"isAllowed","cardinality":"SINGLE"},"isDenied":{"name":"isDenied","fieldType":"BOOLEAN","fieldPath":"isDenied","cardinality":"SINGLE"},"suppressMemberEob":{"name":"suppressMemberEob","fieldType":"BOOLEAN","fieldPath":"suppressMemberEob","cardinality":"SINGLE"},"suppressProviderEob":{"name":"suppressProviderEob","fieldType":"BOOLEAN","fieldPath":"suppressProviderEob","cardinality":"SINGLE"},"bypassClaimLogic":{"name":"bypassClaimLogic","fieldType":"CapDentalBypassClaimLogicEntity","fieldPath":"bypassClaimLogic","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"AdjudicationResult":{"name":"AdjudicationResult","children":{"Reserve":{"targetName":"Reserve","navigationExpression":{"expressionString":"reserve","originalExpressionString":"reserve","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CompensationSchedule":{"targetName":"CompensationSchedule","navigationExpression":{"expressionString":"compensationSchedule","originalExpressionString":"compensationSchedule","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"Eligibility":{"targetName":"Eligibility","navigationExpression":{"expressionString":"eligibility","originalExpressionString":"eligibility","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"EstimatedLoss":{"targetName":"EstimatedLoss","navigationExpression":{"expressionString":"estimatedLoss","originalExpressionString":"estimatedLoss","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"Applicability":{"targetName":"Applicability","navigationExpression":{"expressionString":"applicability","originalExpressionString":"applicability","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"error","originalExpressionString":"error","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"substate":{"name":"substate","fieldType":"STRING","fieldPath":"substate","cardinality":"SINGLE"},"reserve":{"name":"reserve","fieldType":"Reserve","fieldPath":"reserve","cardinality":"SINGLE","forbidTarget":true},"eligibility":{"name":"eligibility","fieldType":"Eligibility","fieldPath":"eligibility","cardinality":"SINGLE","forbidTarget":true},"estimatedLoss":{"name":"estimatedLoss","fieldType":"EstimatedLoss","fieldPath":"estimatedLoss","cardinality":"SINGLE","forbidTarget":true},"applicability":{"name":"applicability","fieldType":"Applicability","fieldPath":"applicability","cardinality":"SINGLE","forbidTarget":true},"error":{"name":"error","fieldType":"MessageType","fieldPath":"error","cardinality":"SINGLE","forbidTarget":true},"compensationSchedule":{"name":"compensationSchedule","fieldType":"CompensationSchedule","fieldPath":"compensationSchedule","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["SubstateAware"],"system":false},"CapEstimationResult":{"name":"CapEstimationResult","children":{},"fields":{},"inheritedContexts":[],"system":false},"ClaimHeaderLink":{"name":"ClaimHeaderLink","children":{},"fields":{"primaryURI":{"name":"primaryURI","fieldType":"ExternalLink","fieldPath":"primaryURI","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ClaimPartyAware":{"name":"ClaimPartyAware","children":{},"fields":{"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentReduction":{"name":"CapPaymentReduction","children":{},"fields":{"reductionNumber":{"name":"reductionNumber","fieldType":"STRING","fieldPath":"reductionNumber","cardinality":"SINGLE"},"reductionSource":{"name":"reductionSource","fieldType":"ExternalLink","fieldPath":"reductionSource","cardinality":"SINGLE"},"reductionType":{"name":"reductionType","fieldType":"STRING","fieldPath":"reductionType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalBaseBalanceItemAllocation":{"name":"CapDentalBaseBalanceItemAllocation","children":{"CapDentalBaseBalanceItemAllocationAddition":{"targetName":"CapDentalBaseBalanceItemAllocationAddition","navigationExpression":{"expressionString":"allocationAdditions","originalExpressionString":"allocationAdditions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalPaymentAllocationPayableItemEntity":{"targetName":"CapDentalPaymentAllocationPayableItemEntity","navigationExpression":{"expressionString":"allocationPayableItem","originalExpressionString":"allocationPayableItem","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalBaseBalanceItemAllocationReduction":{"targetName":"CapDentalBaseBalanceItemAllocationReduction","navigationExpression":{"expressionString":"allocationReductions","originalExpressionString":"allocationReductions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalBaseBalanceItemAllocationTax":{"targetName":"CapDentalBaseBalanceItemAllocationTax","navigationExpression":{"expressionString":"allocationTaxes","originalExpressionString":"allocationTaxes","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"allocationGrossAmount":{"name":"allocationGrossAmount","fieldType":"MONEY","fieldPath":"allocationGrossAmount","cardinality":"SINGLE"},"allocationReductions":{"name":"allocationReductions","fieldType":"CapDentalBaseBalanceItemAllocationReduction","fieldPath":"allocationReductions","cardinality":"MULTIPLE","forbidTarget":true},"allocationTaxes":{"name":"allocationTaxes","fieldType":"CapDentalBaseBalanceItemAllocationTax","fieldPath":"allocationTaxes","cardinality":"MULTIPLE","forbidTarget":true},"allocationSource":{"name":"allocationSource","fieldType":"ExternalLink","fieldPath":"allocationSource","cardinality":"SINGLE"},"allocationAdditions":{"name":"allocationAdditions","fieldType":"CapDentalBaseBalanceItemAllocationAddition","fieldPath":"allocationAdditions","cardinality":"MULTIPLE","forbidTarget":true},"allocationPayableItem":{"name":"allocationPayableItem","fieldType":"CapDentalPaymentAllocationPayableItemEntity","fieldPath":"allocationPayableItem","cardinality":"SINGLE","forbidTarget":true},"allocationNetAmount":{"name":"allocationNetAmount","fieldType":"MONEY","fieldPath":"allocationNetAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyExclusion":{"name":"CapPolicyExclusion","children":{},"fields":{"isExclusion":{"name":"isExclusion","fieldType":"BOOLEAN","fieldPath":"isExclusion","cardinality":"SINGLE"},"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ClaimantAware":{"name":"ClaimantAware","children":{},"fields":{"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyAware"],"system":false},"EstimatedLoss":{"name":"EstimatedLoss","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"amount":{"name":"amount","fieldType":"MONEY","fieldPath":"amount","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["MessageTypeHolder"],"system":false},"BLOBEntity":{"name":"BLOBEntity","children":{},"fields":{"blobCd":{"name":"blobCd","fieldType":"STRING","fieldPath":"blobCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAddition":{"name":"CapPaymentAddition","children":{},"fields":{"additionSource":{"name":"additionSource","fieldType":"ExternalLink","fieldPath":"additionSource","cardinality":"SINGLE"},"additionNumber":{"name":"additionNumber","fieldType":"STRING","fieldPath":"additionNumber","cardinality":"SINGLE"},"additionType":{"name":"additionType","fieldType":"STRING","fieldPath":"additionType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalClaimInfoEntity":{"name":"CapDentalClaimInfoEntity","children":{"CapDentalPatientEntity":{"targetName":"CapDentalPatientEntity","navigationExpression":{"expressionString":"patient","originalExpressionString":"patient","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalClaimDataEntity":{"targetName":"CapDentalClaimDataEntity","navigationExpression":{"expressionString":"claimData","originalExpressionString":"claimData","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalProcedureEntity":{"targetName":"CapDentalProcedureEntity","navigationExpression":{"expressionString":"submittedProcedures","originalExpressionString":"submittedProcedures","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"submittedProcedures":{"name":"submittedProcedures","fieldType":"CapDentalProcedureEntity","fieldPath":"submittedProcedures","cardinality":"MULTIPLE"},"patient":{"name":"patient","fieldType":"CapDentalPatientEntity","fieldPath":"patient","cardinality":"SINGLE","forbidTarget":true},"claimData":{"name":"claimData","fieldType":"CapDentalClaimDataEntity","fieldPath":"claimData","cardinality":"SINGLE","forbidTarget":true},"source":{"name":"source","fieldType":"STRING","fieldPath":"source","cardinality":"SINGLE"},"receivedDate":{"name":"receivedDate","fieldType":"DATE","fieldPath":"receivedDate","cardinality":"SINGLE"}},"inheritedContexts":["CapSettlementLossInfo"],"system":false},"CapDentalPaymentDetailsEntity":{"name":"CapDentalPaymentDetailsEntity","children":{"CapDentalPaymentAllocationEntity":{"targetName":"CapDentalPaymentAllocationEntity","navigationExpression":{"expressionString":"paymentAllocations","originalExpressionString":"paymentAllocations","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalPaymentPayeeDetailsEntity":{"targetName":"CapDentalPaymentPayeeDetailsEntity","navigationExpression":{"expressionString":"payeeDetails","originalExpressionString":"payeeDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"payeeDetails":{"name":"payeeDetails","fieldType":"CapDentalPaymentPayeeDetailsEntity","fieldPath":"payeeDetails","cardinality":"SINGLE"},"paymentAllocations":{"name":"paymentAllocations","fieldType":"CapDentalPaymentAllocationEntity","fieldPath":"paymentAllocations","cardinality":"MULTIPLE","forbidTarget":true},"paymentAdditions":{"name":"paymentAdditions","fieldType":"CapPaymentAddition","fieldPath":"paymentAdditions","cardinality":"MULTIPLE","forbidTarget":true},"paymentReductions":{"name":"paymentReductions","fieldType":"CapPaymentReduction","fieldPath":"paymentReductions","cardinality":"MULTIPLE","forbidTarget":true},"paymentDate":{"name":"paymentDate","fieldType":"DATETIME","fieldPath":"paymentDate","cardinality":"SINGLE"},"paymentTaxes":{"name":"paymentTaxes","fieldType":"CapPaymentTax","fieldPath":"paymentTaxes","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["CapPaymentDetails"],"system":false},"CapDentalTermEntity":{"name":"CapDentalTermEntity","children":{},"fields":{"effectiveDate":{"name":"effectiveDate","fieldType":"DATETIME","fieldPath":"effectiveDate","cardinality":"SINGLE"},"expirationDate":{"name":"expirationDate","fieldType":"DATETIME","fieldPath":"expirationDate","cardinality":"SINGLE"}},"inheritedContexts":["Term"],"system":false},"CapBalanceItem":{"name":"CapBalanceItem","children":{"CapBalanceItemAllocation":{"targetName":"CapBalanceItemAllocation","navigationExpression":{"expressionString":"actualAllocations","originalExpressionString":"actualAllocations","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"actualAllocations":{"name":"actualAllocations","fieldType":"CapBalanceItemAllocation","fieldPath":"actualAllocations","cardinality":"MULTIPLE","forbidTarget":true},"scheduledNetAmount":{"name":"scheduledNetAmount","fieldType":"MONEY","fieldPath":"scheduledNetAmount","cardinality":"SINGLE"},"actualNetAmount":{"name":"actualNetAmount","fieldType":"MONEY","fieldPath":"actualNetAmount","cardinality":"SINGLE"},"paymentDate":{"name":"paymentDate","fieldType":"DATETIME","fieldPath":"paymentDate","cardinality":"SINGLE"},"balancedNetAmount":{"name":"balancedNetAmount","fieldType":"MONEY","fieldPath":"balancedNetAmount","cardinality":"SINGLE"},"paymentNumber":{"name":"paymentNumber","fieldType":"STRING","fieldPath":"paymentNumber","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"MainInsuredInfo":{"name":"MainInsuredInfo","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"lastName":{"name":"lastName","fieldType":"STRING","fieldPath":"lastName","cardinality":"SINGLE"},"city":{"name":"city","fieldType":"STRING","fieldPath":"city","cardinality":"MULTIPLE"},"stateProvinceCd":{"name":"stateProvinceCd","fieldType":"STRING","fieldPath":"stateProvinceCd","cardinality":"MULTIPLE"},"postalCode":{"name":"postalCode","fieldType":"STRING","fieldPath":"postalCode","cardinality":"MULTIPLE"},"legalId":{"name":"legalId","fieldType":"STRING","fieldPath":"legalId","cardinality":"SINGLE"},"customerNumber":{"name":"customerNumber","fieldType":"STRING","fieldPath":"customerNumber","cardinality":"SINGLE"},"legalName":{"name":"legalName","fieldType":"STRING","fieldPath":"legalName","cardinality":"SINGLE"},"firstName":{"name":"firstName","fieldType":"STRING","fieldPath":"firstName","cardinality":"SINGLE"},"phoneNumber":{"name":"phoneNumber","fieldType":"STRING","fieldPath":"phoneNumber","cardinality":"MULTIPLE"},"streetAddress":{"name":"streetAddress","fieldType":"STRING","fieldPath":"streetAddress","cardinality":"MULTIPLE"},"taxId":{"name":"taxId","fieldType":"STRING","fieldPath":"taxId","cardinality":"SINGLE"},"customerId":{"name":"customerId","fieldType":"STRING","fieldPath":"customerId","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"email":{"name":"email","fieldType":"STRING","fieldPath":"email","cardinality":"MULTIPLE"}},"inheritedContexts":["PartyInfo","Retriable","MessageTypeHolder"],"system":false},"ExternalReference":{"name":"ExternalReference","children":{},"fields":{"sourceId":{"name":"sourceId","fieldType":"STRING","fieldPath":"sourceId","cardinality":"SINGLE"},"reference":{"name":"reference","fieldType":"ExternalLink","fieldPath":"reference","cardinality":"SINGLE"},"sourceType":{"name":"sourceType","fieldType":"STRING","fieldPath":"sourceType","cardinality":"SINGLE"},"sourceSystem":{"name":"sourceSystem","fieldType":"STRING","fieldPath":"sourceSystem","cardinality":"SINGLE"},"sourceSubType":{"name":"sourceSubType","fieldType":"STRING","fieldPath":"sourceSubType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalPaymentAllocationAccumulatorDetailsEntity":{"name":"CapDentalPaymentAllocationAccumulatorDetailsEntity","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"accumulatorType":{"name":"accumulatorType","fieldType":"STRING","fieldPath":"accumulatorType","cardinality":"SINGLE"},"appliesToProcedureCategories":{"name":"appliesToProcedureCategories","fieldType":"STRING","fieldPath":"appliesToProcedureCategories","cardinality":"MULTIPLE"},"renewalType":{"name":"renewalType","fieldType":"STRING","fieldPath":"renewalType","cardinality":"SINGLE"},"appliesToProcedureCategory":{"name":"appliesToProcedureCategory","fieldType":"STRING","fieldPath":"appliesToProcedureCategory","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"networkType":{"name":"networkType","fieldType":"STRING","fieldPath":"networkType","cardinality":"SINGLE"},"accumulatorAmount":{"name":"accumulatorAmount","fieldType":"MONEY","fieldPath":"accumulatorAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalEntryPreviouslyUsedEntity":{"name":"CapDentalEntryPreviouslyUsedEntity","children":{},"fields":{"deductibleUsedAmount":{"name":"deductibleUsedAmount","fieldType":"MONEY","fieldPath":"deductibleUsedAmount","cardinality":"SINGLE"},"procedureID":{"name":"procedureID","fieldType":"STRING","fieldPath":"procedureID","cardinality":"SINGLE"},"maximumUsedAmount":{"name":"maximumUsedAmount","fieldType":"MONEY","fieldPath":"maximumUsedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"PolicyIdentifierHolder":{"name":"PolicyIdentifierHolder","children":{},"fields":{"policySystemId":{"name":"policySystemId","fieldType":"ExternalLink","fieldPath":"policySystemId","cardinality":"SINGLE"},"policySystem":{"name":"policySystem","fieldType":"STRING","fieldPath":"policySystem","cardinality":"SINGLE"},"policySystemRevisionNo":{"name":"policySystemRevisionNo","fieldType":"INTEGER","fieldPath":"policySystemRevisionNo","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"EligibilityOverride":{"name":"EligibilityOverride","children":{},"fields":{"ruleCd":{"name":"ruleCd","fieldType":"STRING","fieldPath":"ruleCd","cardinality":"SINGLE"},"message":{"name":"message","fieldType":"STRING","fieldPath":"message","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentDetailsTemplate":{"name":"CapPaymentDetailsTemplate","children":{"CapPaymentAllocationTemplate":{"targetName":"CapPaymentAllocationTemplate","navigationExpression":{"expressionString":"paymentAllocationTemplates","originalExpressionString":"paymentAllocationTemplates","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"paymentAllocationTemplates":{"name":"paymentAllocationTemplates","fieldType":"CapPaymentAllocationTemplate","fieldPath":"paymentAllocationTemplates","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapPatientRole":{"name":"CapPatientRole","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyRole","ClaimPartyAware"],"system":false},"CapDentalPaymentAllocationLossInfoEntity":{"name":"CapDentalPaymentAllocationLossInfoEntity","children":{},"fields":{"lossSource":{"name":"lossSource","fieldType":"ExternalLink","fieldPath":"lossSource","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentAllocationLossInfo"],"system":false},"CapDentalPaymentAllocationPayableItemEntity":{"name":"CapDentalPaymentAllocationPayableItemEntity","children":{},"fields":{"orthoMonth":{"name":"orthoMonth","fieldType":"INTEGER","fieldPath":"orthoMonth","cardinality":"SINGLE"},"claimSource":{"name":"claimSource","fieldType":"ExternalLink","fieldPath":"claimSource","cardinality":"SINGLE"},"procedureID":{"name":"procedureID","fieldType":"STRING","fieldPath":"procedureID","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentAllocationPayableItem"],"system":false},"DistanceBoundCoverableItemCondition":{"name":"DistanceBoundCoverableItemCondition","children":{},"fields":{"distanceMoreThan":{"name":"distanceMoreThan","fieldType":"DECIMAL","fieldPath":"distanceMoreThan","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition"],"system":false},"CapScheduledPayment":{"name":"CapScheduledPayment","children":{"CapPaymentDetails":{"targetName":"CapPaymentDetails","navigationExpression":{"expressionString":"paymentDetails","originalExpressionString":"paymentDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"paymentNetAmount":{"name":"paymentNetAmount","fieldType":"MONEY","fieldPath":"paymentNetAmount","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"paymentDetails":{"name":"paymentDetails","fieldType":"CapPaymentDetails","fieldPath":"paymentDetails","cardinality":"SINGLE","forbidTarget":true},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"},"paymentNumber":{"name":"paymentNumber","fieldType":"STRING","fieldPath":"paymentNumber","cardinality":"SINGLE"},"direction":{"name":"direction","fieldType":"STRING","fieldPath":"direction","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentInfo"],"system":false},"CapDentalDHMOFrequencyEntity":{"name":"CapDentalDHMOFrequencyEntity","children":{},"fields":{"isAppliedTowardsMOOP":{"name":"isAppliedTowardsMOOP","fieldType":"STRING","fieldPath":"isAppliedTowardsMOOP","cardinality":"SINGLE"},"frequencyRange":{"name":"frequencyRange","fieldType":"STRING","fieldPath":"frequencyRange","cardinality":"SINGLE"},"frequencyPeriodType":{"name":"frequencyPeriodType","fieldType":"STRING","fieldPath":"frequencyPeriodType","cardinality":"SINGLE"},"frequencyPeriod":{"name":"frequencyPeriod","fieldType":"INTEGER","fieldPath":"frequencyPeriod","cardinality":"SINGLE"},"isCovered":{"name":"isCovered","fieldType":"BOOLEAN","fieldPath":"isCovered","cardinality":"SINGLE"},"frequency":{"name":"frequency","fieldType":"STRING","fieldPath":"frequency","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentSchedule":{"name":"CapPaymentSchedule","children":{"CapScheduledPayment":{"targetName":"CapScheduledPayment","navigationExpression":{"expressionString":"payments","originalExpressionString":"payments","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"},"payments":{"name":"payments","fieldType":"CapScheduledPayment","fieldPath":"payments","cardinality":"MULTIPLE","forbidTarget":true},"paymentTemplate":{"name":"paymentTemplate","fieldType":"ExternalLink","fieldPath":"paymentTemplate","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"scheduleNumber":{"name":"scheduleNumber","fieldType":"STRING","fieldPath":"scheduleNumber","cardinality":"SINGLE"},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"CapPaymentInfo":{"name":"CapPaymentInfo","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"paymentNetAmount":{"name":"paymentNetAmount","fieldType":"MONEY","fieldPath":"paymentNetAmount","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"},"paymentNumber":{"name":"paymentNumber","fieldType":"STRING","fieldPath":"paymentNumber","cardinality":"SINGLE"},"direction":{"name":"direction","fieldType":"STRING","fieldPath":"direction","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"Retriable":{"name":"Retriable","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["MessageTypeHolder"],"system":false},"CapDentalBypassServiceLogicEntity":{"name":"CapDentalBypassServiceLogicEntity","children":{},"fields":{"bypassOverpaymentLogic":{"name":"bypassOverpaymentLogic","fieldType":"BOOLEAN","fieldPath":"bypassOverpaymentLogic","cardinality":"SINGLE"},"bypassCobLogic":{"name":"bypassCobLogic","fieldType":"BOOLEAN","fieldPath":"bypassCobLogic","cardinality":"SINGLE"},"bypassDuplicateServiceLogic":{"name":"bypassDuplicateServiceLogic","fieldType":"BOOLEAN","fieldPath":"bypassDuplicateServiceLogic","cardinality":"SINGLE"},"bypassToothExtractionRules":{"name":"bypassToothExtractionRules","fieldType":"BOOLEAN","fieldPath":"bypassToothExtractionRules","cardinality":"SINGLE"},"bypassGracePeriodLogic":{"name":"bypassGracePeriodLogic","fieldType":"BOOLEAN","fieldPath":"bypassGracePeriodLogic","cardinality":"SINGLE"},"bypassClinicalReview":{"name":"bypassClinicalReview","fieldType":"BOOLEAN","fieldPath":"bypassClinicalReview","cardinality":"SINGLE"},"bypassInterestLogic":{"name":"bypassInterestLogic","fieldType":"BOOLEAN","fieldPath":"bypassInterestLogic","cardinality":"SINGLE"},"bypassMissingToothLogic":{"name":"bypassMissingToothLogic","fieldType":"BOOLEAN","fieldPath":"bypassMissingToothLogic","cardinality":"SINGLE"},"bypassAllRules":{"name":"bypassAllRules","fieldType":"BOOLEAN","fieldPath":"bypassAllRules","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapProviderRole":{"name":"CapProviderRole","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"},"providerLink":{"name":"providerLink","fieldType":"STRING","fieldPath":"providerLink","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyRole","ClaimPartyAware"],"system":false},"CapClaimHeaderCase":{"name":"CapClaimHeaderCase","children":{},"fields":{"caseNumber":{"name":"caseNumber","fieldType":"STRING","fieldPath":"caseNumber","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocation":{"name":"CapPaymentAllocation","children":{"CapPaymentAllocationTaxSplitResult":{"targetName":"CapPaymentAllocationTaxSplitResult","navigationExpression":{"expressionString":"taxSplitResults","originalExpressionString":"taxSplitResults","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentAllocationLossInfo":{"targetName":"CapPaymentAllocationLossInfo","navigationExpression":{"expressionString":"allocationLossInfo","originalExpressionString":"allocationLossInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPaymentAllocationReductionSplitResult":{"targetName":"CapPaymentAllocationReductionSplitResult","navigationExpression":{"expressionString":"reductionSplitResults","originalExpressionString":"reductionSplitResults","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentAllocationAdditionSplitResult":{"targetName":"CapPaymentAllocationAdditionSplitResult","navigationExpression":{"expressionString":"additionSplitResults","originalExpressionString":"additionSplitResults","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentAllocationPayableItem":{"targetName":"CapPaymentAllocationPayableItem","navigationExpression":{"expressionString":"allocationPayableItem","originalExpressionString":"allocationPayableItem","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"allocationLobCd":{"name":"allocationLobCd","fieldType":"STRING","fieldPath":"allocationLobCd","cardinality":"SINGLE"},"allocationGrossAmount":{"name":"allocationGrossAmount","fieldType":"MONEY","fieldPath":"allocationGrossAmount","cardinality":"SINGLE"},"allocationLossInfo":{"name":"allocationLossInfo","fieldType":"CapPaymentAllocationLossInfo","fieldPath":"allocationLossInfo","cardinality":"SINGLE","forbidTarget":true},"allocationSource":{"name":"allocationSource","fieldType":"ExternalLink","fieldPath":"allocationSource","cardinality":"SINGLE"},"allocationPayableItem":{"name":"allocationPayableItem","fieldType":"CapPaymentAllocationPayableItem","fieldPath":"allocationPayableItem","cardinality":"SINGLE","forbidTarget":true},"taxSplitResults":{"name":"taxSplitResults","fieldType":"CapPaymentAllocationTaxSplitResult","fieldPath":"taxSplitResults","cardinality":"MULTIPLE","forbidTarget":true},"allocationNetAmount":{"name":"allocationNetAmount","fieldType":"MONEY","fieldPath":"allocationNetAmount","cardinality":"SINGLE"},"reserveType":{"name":"reserveType","fieldType":"STRING","fieldPath":"reserveType","cardinality":"SINGLE"},"additionSplitResults":{"name":"additionSplitResults","fieldType":"CapPaymentAllocationAdditionSplitResult","fieldPath":"additionSplitResults","cardinality":"MULTIPLE","forbidTarget":true},"reductionSplitResults":{"name":"reductionSplitResults","fieldType":"CapPaymentAllocationReductionSplitResult","fieldPath":"reductionSplitResults","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapPaymentAllocationAdditionSplitResult":{"name":"CapPaymentAllocationAdditionSplitResult","children":{},"fields":{"additionNumber":{"name":"additionNumber","fieldType":"STRING","fieldPath":"additionNumber","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalTreatmentReasonEntity":{"name":"CapDentalTreatmentReasonEntity","children":{},"fields":{"treatmentResultingFrom":{"name":"treatmentResultingFrom","fieldType":"STRING","fieldPath":"treatmentResultingFrom","cardinality":"SINGLE"},"autoAccidentState":{"name":"autoAccidentState","fieldType":"STRING","fieldPath":"autoAccidentState","cardinality":"SINGLE"},"dateOfAccident":{"name":"dateOfAccident","fieldType":"DATE","fieldPath":"dateOfAccident","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"RetriableMessage":{"name":"RetriableMessage","children":{},"fields":{"severity":{"name":"severity","fieldType":"STRING","fieldPath":"severity","cardinality":"SINGLE"},"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"},"source":{"name":"source","fieldType":"STRING","fieldPath":"source","cardinality":"SINGLE"},"message":{"name":"message","fieldType":"STRING","fieldPath":"message","cardinality":"SINGLE"}},"inheritedContexts":["MessageType"],"system":false},"CapDentalBaseClaimData":{"name":"CapDentalBaseClaimData","children":{"CapProviderRole":{"targetName":"CapProviderRole","navigationExpression":{"expressionString":"providerRole","originalExpressionString":"providerRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapAlternatePayeeRole":{"targetName":"CapAlternatePayeeRole","navigationExpression":{"expressionString":"alternatePayeeRole","originalExpressionString":"alternatePayeeRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalClaimCoordinationOfBenefitsEntity":{"targetName":"CapDentalClaimCoordinationOfBenefitsEntity","navigationExpression":{"expressionString":"cob","originalExpressionString":"cob","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalProviderFeesEntity":{"targetName":"CapDentalProviderFeesEntity","navigationExpression":{"expressionString":"providerFees","originalExpressionString":"providerFees","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalProviderDiscountEntity":{"targetName":"CapDentalProviderDiscountEntity","navigationExpression":{"expressionString":"providerDiscount","originalExpressionString":"providerDiscount","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalTreatmentReasonEntity":{"targetName":"CapDentalTreatmentReasonEntity","navigationExpression":{"expressionString":"treatmentReason","originalExpressionString":"treatmentReason","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPatientRole":{"targetName":"CapPatientRole","navigationExpression":{"expressionString":"patientRole","originalExpressionString":"patientRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPolicyholderRole":{"targetName":"CapPolicyholderRole","navigationExpression":{"expressionString":"policyholderRole","originalExpressionString":"policyholderRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"providerFees":{"name":"providerFees","fieldType":"CapDentalProviderFeesEntity","fieldPath":"providerFees","cardinality":"MULTIPLE"},"providerRole":{"name":"providerRole","fieldType":"CapProviderRole","fieldPath":"providerRole","cardinality":"SINGLE"},"digitalImageNumbers":{"name":"digitalImageNumbers","fieldType":"STRING","fieldPath":"digitalImageNumbers","cardinality":"MULTIPLE"},"payeeType":{"name":"payeeType","fieldType":"STRING","fieldPath":"payeeType","cardinality":"SINGLE"},"cleanClaimDate":{"name":"cleanClaimDate","fieldType":"DATE","fieldPath":"cleanClaimDate","cardinality":"SINGLE"},"patientRole":{"name":"patientRole","fieldType":"CapPatientRole","fieldPath":"patientRole","cardinality":"SINGLE"},"isUnknownOrIntProvider":{"name":"isUnknownOrIntProvider","fieldType":"BOOLEAN","fieldPath":"isUnknownOrIntProvider","cardinality":"SINGLE"},"treatmentReason":{"name":"treatmentReason","fieldType":"CapDentalTreatmentReasonEntity","fieldPath":"treatmentReason","cardinality":"SINGLE","forbidTarget":true},"dateOfBirth":{"name":"dateOfBirth","fieldType":"DATE","fieldPath":"dateOfBirth","cardinality":"SINGLE"},"remark":{"name":"remark","fieldType":"STRING","fieldPath":"remark","cardinality":"SINGLE"},"source":{"name":"source","fieldType":"STRING","fieldPath":"source","cardinality":"SINGLE"},"providerDiscount":{"name":"providerDiscount","fieldType":"CapDentalProviderDiscountEntity","fieldPath":"providerDiscount","cardinality":"SINGLE"},"missingTooths":{"name":"missingTooths","fieldType":"STRING","fieldPath":"missingTooths","cardinality":"MULTIPLE"},"alternatePayeeRole":{"name":"alternatePayeeRole","fieldType":"CapAlternatePayeeRole","fieldPath":"alternatePayeeRole","cardinality":"SINGLE"},"transactionType":{"name":"transactionType","fieldType":"STRING","fieldPath":"transactionType","cardinality":"SINGLE"},"policyholderRole":{"name":"policyholderRole","fieldType":"CapPolicyholderRole","fieldPath":"policyholderRole","cardinality":"SINGLE"},"initialDateOfService":{"name":"initialDateOfService","fieldType":"DATETIME","fieldPath":"initialDateOfService","cardinality":"SINGLE"},"placeOfTreatment":{"name":"placeOfTreatment","fieldType":"STRING","fieldPath":"placeOfTreatment","cardinality":"SINGLE"},"cob":{"name":"cob","fieldType":"CapDentalClaimCoordinationOfBenefitsEntity","fieldPath":"cob","cardinality":"MULTIPLE","forbidTarget":true},"receivedDate":{"name":"receivedDate","fieldType":"DATE","fieldPath":"receivedDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"SelfAware":{"name":"SelfAware","children":{},"fields":{"self":{"name":"self","fieldType":"ExternalLink","fieldPath":"self","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationTaxSplitResult":{"name":"CapPaymentAllocationTaxSplitResult","children":{},"fields":{"taxNumber":{"name":"taxNumber","fieldType":"STRING","fieldPath":"taxNumber","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalProviderDiscountsFeesEntity":{"name":"CapDentalProviderDiscountsFeesEntity","children":{},"fields":{},"inheritedContexts":[],"system":false},"ClaimPartyInfo":{"name":"ClaimPartyInfo","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"lastName":{"name":"lastName","fieldType":"STRING","fieldPath":"lastName","cardinality":"SINGLE"},"city":{"name":"city","fieldType":"STRING","fieldPath":"city","cardinality":"MULTIPLE"},"stateProvinceCd":{"name":"stateProvinceCd","fieldType":"STRING","fieldPath":"stateProvinceCd","cardinality":"MULTIPLE"},"postalCode":{"name":"postalCode","fieldType":"STRING","fieldPath":"postalCode","cardinality":"MULTIPLE"},"legalId":{"name":"legalId","fieldType":"STRING","fieldPath":"legalId","cardinality":"SINGLE"},"customerNumber":{"name":"customerNumber","fieldType":"STRING","fieldPath":"customerNumber","cardinality":"SINGLE"},"legalName":{"name":"legalName","fieldType":"STRING","fieldPath":"legalName","cardinality":"SINGLE"},"firstName":{"name":"firstName","fieldType":"STRING","fieldPath":"firstName","cardinality":"SINGLE"},"phoneNumber":{"name":"phoneNumber","fieldType":"STRING","fieldPath":"phoneNumber","cardinality":"MULTIPLE"},"streetAddress":{"name":"streetAddress","fieldType":"STRING","fieldPath":"streetAddress","cardinality":"MULTIPLE"},"taxId":{"name":"taxId","fieldType":"STRING","fieldPath":"taxId","cardinality":"SINGLE"},"customerId":{"name":"customerId","fieldType":"STRING","fieldPath":"customerId","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"email":{"name":"email","fieldType":"STRING","fieldPath":"email","cardinality":"MULTIPLE"}},"inheritedContexts":["PartyInfo","Retriable","MessageTypeHolder"],"system":false},"CapTimelineEvent":{"name":"CapTimelineEvent","children":{},"fields":{"manuallyAdded":{"name":"manuallyAdded","fieldType":"BOOLEAN","fieldPath":"manuallyAdded","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"eventDate":{"name":"eventDate","fieldType":"DATETIME","fieldPath":"eventDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapSettlementResult":{"name":"CapSettlementResult","children":{},"fields":{"reserve":{"name":"reserve","fieldType":"DECIMAL","fieldPath":"reserve","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDeductibleDetailEntity":{"name":"CapDeductibleDetailEntity","children":{},"fields":{"individualPreventiveINNAnnualDeductible":{"name":"individualPreventiveINNAnnualDeductible","fieldType":"MONEY","fieldPath":"individualPreventiveINNAnnualDeductible","cardinality":"SINGLE"},"individualMajorINNAnnualDeductible":{"name":"individualMajorINNAnnualDeductible","fieldType":"MONEY","fieldPath":"individualMajorINNAnnualDeductible","cardinality":"SINGLE"},"individualBasicINNAnnualDeductible":{"name":"individualBasicINNAnnualDeductible","fieldType":"MONEY","fieldPath":"individualBasicINNAnnualDeductible","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"UnverifiedPolicy":{"name":"UnverifiedPolicy","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapInsuredInfo":{"targetName":"CapInsuredInfo","navigationExpression":{"expressionString":"insureds","originalExpressionString":"insureds","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"currencyCd":{"name":"currencyCd","fieldType":"STRING","fieldPath":"currencyCd","cardinality":"SINGLE"},"riskStateCd":{"name":"riskStateCd","fieldType":"STRING","fieldPath":"riskStateCd","cardinality":"SINGLE"},"isVerified":{"name":"isVerified","fieldType":"BOOLEAN","fieldPath":"isVerified","cardinality":"SINGLE"},"insureds":{"name":"insureds","fieldType":"CapInsuredInfo","fieldPath":"insureds","cardinality":"MULTIPLE","forbidTarget":true},"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyType":{"name":"policyType","fieldType":"STRING","fieldPath":"policyType","cardinality":"SINGLE"},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"policyStatus":{"name":"policyStatus","fieldType":"STRING","fieldPath":"policyStatus","cardinality":"SINGLE"},"txEffectiveDate":{"name":"txEffectiveDate","fieldType":"STRING","fieldPath":"txEffectiveDate","cardinality":"SINGLE"},"capPolicyVersionId":{"name":"capPolicyVersionId","fieldType":"STRING","fieldPath":"capPolicyVersionId","cardinality":"SINGLE"},"productCd":{"name":"productCd","fieldType":"STRING","fieldPath":"productCd","cardinality":"SINGLE"}},"inheritedContexts":["CapPolicyInfo","RootEntity","CapClaimHeaderPolicy","ClaimPolicyAware"],"system":false},"CapDentalPaymentAllocationDentalDetailsEntity":{"name":"CapDentalPaymentAllocationDentalDetailsEntity","children":{"CapDentalPaymentAllocationAccumulatorDetailsEntity":{"targetName":"CapDentalPaymentAllocationAccumulatorDetailsEntity","navigationExpression":{"expressionString":"accumulatorDetails","originalExpressionString":"accumulatorDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"accumulatorDetails":{"name":"accumulatorDetails","fieldType":"CapDentalPaymentAllocationAccumulatorDetailsEntity","fieldPath":"accumulatorDetails","cardinality":"MULTIPLE","forbidTarget":true},"patient":{"name":"patient","fieldType":"ExternalLink","fieldPath":"patient","cardinality":"SINGLE"},"transactionTypeCd":{"name":"transactionTypeCd","fieldType":"STRING","fieldPath":"transactionTypeCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"SubstateAware":{"name":"SubstateAware","children":{},"fields":{"substate":{"name":"substate","fieldType":"STRING","fieldPath":"substate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ClaimPartyRole":{"name":"ClaimPartyRole","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyAware"],"system":false},"CapDentalPaymentAllocationEntity":{"name":"CapDentalPaymentAllocationEntity","children":{"CapDentalPaymentAllocationLossInfoEntity":{"targetName":"CapDentalPaymentAllocationLossInfoEntity","navigationExpression":{"expressionString":"allocationLossInfo","originalExpressionString":"allocationLossInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalPaymentAllocationPayableItemEntity":{"targetName":"CapDentalPaymentAllocationPayableItemEntity","navigationExpression":{"expressionString":"allocationPayableItem","originalExpressionString":"allocationPayableItem","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalPaymentAllocationDentalDetailsEntity":{"targetName":"CapDentalPaymentAllocationDentalDetailsEntity","navigationExpression":{"expressionString":"allocationDentalDetails","originalExpressionString":"allocationDentalDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"allocationLobCd":{"name":"allocationLobCd","fieldType":"STRING","fieldPath":"allocationLobCd","cardinality":"SINGLE"},"allocationGrossAmount":{"name":"allocationGrossAmount","fieldType":"MONEY","fieldPath":"allocationGrossAmount","cardinality":"SINGLE"},"allocationLossInfo":{"name":"allocationLossInfo","fieldType":"CapDentalPaymentAllocationLossInfoEntity","fieldPath":"allocationLossInfo","cardinality":"SINGLE","forbidTarget":true},"allocationSource":{"name":"allocationSource","fieldType":"ExternalLink","fieldPath":"allocationSource","cardinality":"SINGLE"},"allocationPayableItem":{"name":"allocationPayableItem","fieldType":"CapDentalPaymentAllocationPayableItemEntity","fieldPath":"allocationPayableItem","cardinality":"SINGLE","forbidTarget":true},"taxSplitResults":{"name":"taxSplitResults","fieldType":"CapPaymentAllocationTaxSplitResult","fieldPath":"taxSplitResults","cardinality":"MULTIPLE","forbidTarget":true},"allocationNetAmount":{"name":"allocationNetAmount","fieldType":"MONEY","fieldPath":"allocationNetAmount","cardinality":"SINGLE"},"reserveType":{"name":"reserveType","fieldType":"STRING","fieldPath":"reserveType","cardinality":"SINGLE"},"allocationDentalDetails":{"name":"allocationDentalDetails","fieldType":"CapDentalPaymentAllocationDentalDetailsEntity","fieldPath":"allocationDentalDetails","cardinality":"SINGLE","forbidTarget":true},"additionSplitResults":{"name":"additionSplitResults","fieldType":"CapPaymentAllocationAdditionSplitResult","fieldPath":"additionSplitResults","cardinality":"MULTIPLE","forbidTarget":true},"reductionSplitResults":{"name":"reductionSplitResults","fieldType":"CapPaymentAllocationReductionSplitResult","fieldPath":"reductionSplitResults","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["CapPaymentAllocation"],"system":false},"CapDentalOverrideClaimValueEntity":{"name":"CapDentalOverrideClaimValueEntity","children":{"Period":{"targetName":"Period","navigationExpression":{"expressionString":"overrideEligibilityPeriod","originalExpressionString":"overrideEligibilityPeriod","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"overridePaymentInterestAmount":{"name":"overridePaymentInterestAmount","fieldType":"MONEY","fieldPath":"overridePaymentInterestAmount","cardinality":"SINGLE"},"overridePaymentInterestState":{"name":"overridePaymentInterestState","fieldType":"STRING","fieldPath":"overridePaymentInterestState","cardinality":"SINGLE"},"overrideBasicWaitingPeriod":{"name":"overrideBasicWaitingPeriod","fieldType":"INTEGER","fieldPath":"overrideBasicWaitingPeriod","cardinality":"SINGLE"},"overridePreventiveWaitingPeriod":{"name":"overridePreventiveWaitingPeriod","fieldType":"INTEGER","fieldPath":"overridePreventiveWaitingPeriod","cardinality":"SINGLE"},"overrideGracePeriod":{"name":"overrideGracePeriod","fieldType":"INTEGER","fieldPath":"overrideGracePeriod","cardinality":"SINGLE"},"overrideOrthoWaitingPeriod":{"name":"overrideOrthoWaitingPeriod","fieldType":"INTEGER","fieldPath":"overrideOrthoWaitingPeriod","cardinality":"SINGLE"},"overrideEligibilityPeriod":{"name":"overrideEligibilityPeriod","fieldType":"Period","fieldPath":"overrideEligibilityPeriod","cardinality":"SINGLE","forbidTarget":true},"overridePaymentInterestDays":{"name":"overridePaymentInterestDays","fieldType":"INTEGER","fieldPath":"overridePaymentInterestDays","cardinality":"SINGLE"},"overrideStudentDependentStatus":{"name":"overrideStudentDependentStatus","fieldType":"STRING","fieldPath":"overrideStudentDependentStatus","cardinality":"SINGLE"},"overrideFeeSchedule":{"name":"overrideFeeSchedule","fieldType":"STRING","fieldPath":"overrideFeeSchedule","cardinality":"SINGLE"},"overrideMajorWaitingPeriod":{"name":"overrideMajorWaitingPeriod","fieldType":"INTEGER","fieldPath":"overrideMajorWaitingPeriod","cardinality":"SINGLE"},"overrideLateEntrantWaitingPeriod":{"name":"overrideLateEntrantWaitingPeriod","fieldType":"INTEGER","fieldPath":"overrideLateEntrantWaitingPeriod","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"InternalLink":{"name":"InternalLink","children":{},"fields":{},"inheritedContexts":[],"system":true},"CapEventCaseRefHolder":{"name":"CapEventCaseRefHolder","children":{},"fields":{"eventCaseLink":{"name":"eventCaseLink","fieldType":"ExternalLink","fieldPath":"eventCaseLink","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalConsultantReviewEntity":{"name":"CapDentalConsultantReviewEntity","children":{},"fields":{"consultantReplyLetter":{"name":"consultantReplyLetter","fieldType":"STRING","fieldPath":"consultantReplyLetter","cardinality":"SINGLE"},"surface":{"name":"surface","fieldType":"STRING","fieldPath":"surface","cardinality":"SINGLE"},"consultantReply":{"name":"consultantReply","fieldType":"STRING","fieldPath":"consultantReply","cardinality":"SINGLE"},"alternateCDTCode":{"name":"alternateCDTCode","fieldType":"STRING","fieldPath":"alternateCDTCode","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"LimitedCoverableItemCondition":{"name":"LimitedCoverableItemCondition","children":{},"fields":{"accumulatorType":{"name":"accumulatorType","fieldType":"STRING","fieldPath":"accumulatorType","cardinality":"SINGLE"},"amount":{"name":"amount","fieldType":"DECIMAL","fieldPath":"amount","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition","Accumulative"],"system":false},"ExternalClaimData":{"name":"ExternalClaimData","children":{"ExternalCapClaimHeader":{"targetName":"ExternalCapClaimHeader","navigationExpression":{"expressionString":"claim","originalExpressionString":"claim","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"sourceId":{"name":"sourceId","fieldType":"STRING","fieldPath":"sourceId","cardinality":"SINGLE"},"sourceType":{"name":"sourceType","fieldType":"STRING","fieldPath":"sourceType","cardinality":"SINGLE"},"sourceSystem":{"name":"sourceSystem","fieldType":"STRING","fieldPath":"sourceSystem","cardinality":"SINGLE"},"claim":{"name":"claim","fieldType":"ExternalCapClaimHeader","fieldPath":"claim","cardinality":"SINGLE","forbidTarget":true},"sourceSubType":{"name":"sourceSubType","fieldType":"STRING","fieldPath":"sourceSubType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CoverableItem":{"name":"CoverableItem","children":{"DelayableCoverableItemCondition":{"targetName":"DelayableCoverableItemCondition","navigationExpression":{"expressionString":"delayableCoverableItemCondition","originalExpressionString":"delayableCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"LimitedCoverableItemCondition":{"targetName":"LimitedCoverableItemCondition","navigationExpression":{"expressionString":"limitedCoverableItemCondition","originalExpressionString":"limitedCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"RestrictedCoverableItemCondition":{"targetName":"RestrictedCoverableItemCondition","navigationExpression":{"expressionString":"restrictedCoverableItemCondition","originalExpressionString":"restrictedCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"PreExistingCoverableItemCondition":{"targetName":"PreExistingCoverableItemCondition","navigationExpression":{"expressionString":"preExistingCoverableItemCondition","originalExpressionString":"preExistingCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPolicyExclusion":{"targetName":"CapPolicyExclusion","navigationExpression":{"expressionString":"exclusions","originalExpressionString":"exclusions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"DistanceBoundCoverableItemCondition":{"targetName":"DistanceBoundCoverableItemCondition","navigationExpression":{"expressionString":"distanceBoundCoverableItemCondition","originalExpressionString":"distanceBoundCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CompositeCoverableItemCondition":{"targetName":"CompositeCoverableItemCondition","navigationExpression":{"expressionString":"compositeCoverableItemCondition","originalExpressionString":"compositeCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"TimedCoverableItemCondition":{"targetName":"TimedCoverableItemCondition","navigationExpression":{"expressionString":"timedCoverableItemCondition","originalExpressionString":"timedCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"OccurrableCoverableItemCondition":{"targetName":"OccurrableCoverableItemCondition","navigationExpression":{"expressionString":"occurrableCoverableItemCondition","originalExpressionString":"occurrableCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"compositeCoverableItemCondition":{"name":"compositeCoverableItemCondition","fieldType":"CompositeCoverableItemCondition","fieldPath":"compositeCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"distanceBoundCoverableItemCondition":{"name":"distanceBoundCoverableItemCondition","fieldType":"DistanceBoundCoverableItemCondition","fieldPath":"distanceBoundCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"},"occurrableCoverableItemCondition":{"name":"occurrableCoverableItemCondition","fieldType":"OccurrableCoverableItemCondition","fieldPath":"occurrableCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"delayableCoverableItemCondition":{"name":"delayableCoverableItemCondition","fieldType":"DelayableCoverableItemCondition","fieldPath":"delayableCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"exclusions":{"name":"exclusions","fieldType":"CapPolicyExclusion","fieldPath":"exclusions","cardinality":"MULTIPLE","forbidTarget":true},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"preExistingCoverableItemCondition":{"name":"preExistingCoverableItemCondition","fieldType":"PreExistingCoverableItemCondition","fieldPath":"preExistingCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"restrictedCoverableItemCondition":{"name":"restrictedCoverableItemCondition","fieldType":"RestrictedCoverableItemCondition","fieldPath":"restrictedCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"timedCoverableItemCondition":{"name":"timedCoverableItemCondition","fieldType":"TimedCoverableItemCondition","fieldPath":"timedCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"limitedCoverableItemCondition":{"name":"limitedCoverableItemCondition","fieldType":"LimitedCoverableItemCondition","fieldPath":"limitedCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapDentalBaseBalanceItemAllocationAddition":{"name":"CapDentalBaseBalanceItemAllocationAddition","children":{},"fields":{"additionSubType":{"name":"additionSubType","fieldType":"STRING","fieldPath":"additionSubType","cardinality":"SINGLE"},"additionType":{"name":"additionType","fieldType":"STRING","fieldPath":"additionType","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapClaim":{"name":"CapClaim","children":{},"fields":{},"inheritedContexts":[],"system":false},"MemberAware":{"name":"MemberAware","children":{},"fields":{"memberRegistryTypeId":{"name":"memberRegistryTypeId","fieldType":"STRING","fieldPath":"memberRegistryTypeId","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalAccumulatorEntity":{"name":"CapDentalAccumulatorEntity","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"accumulatorType":{"name":"accumulatorType","fieldType":"STRING","fieldPath":"accumulatorType","cardinality":"SINGLE"},"remainingAmount":{"name":"remainingAmount","fieldType":"MONEY","fieldPath":"remainingAmount","cardinality":"SINGLE"},"reservedAmount":{"name":"reservedAmount","fieldType":"MONEY","fieldPath":"reservedAmount","cardinality":"SINGLE"},"appliesToProcedureCategories":{"name":"appliesToProcedureCategories","fieldType":"STRING","fieldPath":"appliesToProcedureCategories","cardinality":"MULTIPLE"},"renewalType":{"name":"renewalType","fieldType":"STRING","fieldPath":"renewalType","cardinality":"SINGLE"},"appliesToProcedureCategory":{"name":"appliesToProcedureCategory","fieldType":"STRING","fieldPath":"appliesToProcedureCategory","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"networkType":{"name":"networkType","fieldType":"STRING","fieldPath":"networkType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapSettlementDetail":{"name":"CapSettlementDetail","children":{},"fields":{},"inheritedContexts":[],"system":false},"CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity":{"name":"CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity","children":{},"fields":{"preventWaitingPeriod":{"name":"preventWaitingPeriod","fieldType":"INTEGER","fieldPath":"preventWaitingPeriod","cardinality":"SINGLE"},"basicWaitingPeriod":{"name":"basicWaitingPeriod","fieldType":"INTEGER","fieldPath":"basicWaitingPeriod","cardinality":"SINGLE"},"majorWaitingPeriod":{"name":"majorWaitingPeriod","fieldType":"INTEGER","fieldPath":"majorWaitingPeriod","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalBypassClaimLogicEntity":{"name":"CapDentalBypassClaimLogicEntity","children":{},"fields":{"bypassOverpaymentLogic":{"name":"bypassOverpaymentLogic","fieldType":"BOOLEAN","fieldPath":"bypassOverpaymentLogic","cardinality":"SINGLE"},"bypassCobLogic":{"name":"bypassCobLogic","fieldType":"BOOLEAN","fieldPath":"bypassCobLogic","cardinality":"SINGLE"},"bypassDuplicateServiceLogic":{"name":"bypassDuplicateServiceLogic","fieldType":"BOOLEAN","fieldPath":"bypassDuplicateServiceLogic","cardinality":"SINGLE"},"bypassToothExtractionRules":{"name":"bypassToothExtractionRules","fieldType":"BOOLEAN","fieldPath":"bypassToothExtractionRules","cardinality":"SINGLE"},"bypassGracePeriodLogic":{"name":"bypassGracePeriodLogic","fieldType":"BOOLEAN","fieldPath":"bypassGracePeriodLogic","cardinality":"SINGLE"},"bypassClinicalReview":{"name":"bypassClinicalReview","fieldType":"BOOLEAN","fieldPath":"bypassClinicalReview","cardinality":"SINGLE"},"bypassInterestLogic":{"name":"bypassInterestLogic","fieldType":"BOOLEAN","fieldPath":"bypassInterestLogic","cardinality":"SINGLE"},"bypassMissingToothLogic":{"name":"bypassMissingToothLogic","fieldType":"BOOLEAN","fieldPath":"bypassMissingToothLogic","cardinality":"SINGLE"},"bypassAllRules":{"name":"bypassAllRules","fieldType":"BOOLEAN","fieldPath":"bypassAllRules","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapSettlement":{"name":"CapSettlement","children":{"CapSettlementDetail":{"targetName":"CapSettlementDetail","navigationExpression":{"expressionString":"settlementDetail","originalExpressionString":"settlementDetail","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"AccessTrackInfo":{"targetName":"AccessTrackInfo","navigationExpression":{"expressionString":"accessTrackInfo","originalExpressionString":"accessTrackInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapSettlementLossInfo":{"targetName":"CapSettlementLossInfo","navigationExpression":{"expressionString":"settlementLossInfo","originalExpressionString":"settlementLossInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapSettlementAbsenceInfo":{"targetName":"CapSettlementAbsenceInfo","navigationExpression":{"expressionString":"settlementAbsenceInfo","originalExpressionString":"settlementAbsenceInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapSettlementResult":{"targetName":"CapSettlementResult","navigationExpression":{"expressionString":"settlementResult","originalExpressionString":"settlementResult","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"settlementType":{"name":"settlementType","fieldType":"STRING","fieldPath":"settlementType","cardinality":"SINGLE"},"settlementDetail":{"name":"settlementDetail","fieldType":"CapSettlementDetail","fieldPath":"settlementDetail","cardinality":"SINGLE","forbidTarget":true},"claimLossIdentification":{"name":"claimLossIdentification","fieldType":"ExternalLink","fieldPath":"claimLossIdentification","cardinality":"SINGLE"},"accessTrackInfo":{"name":"accessTrackInfo","fieldType":"AccessTrackInfo","fieldPath":"accessTrackInfo","cardinality":"SINGLE","forbidTarget":true},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"settlementNumber":{"name":"settlementNumber","fieldType":"STRING","fieldPath":"settlementNumber","cardinality":"SINGLE"},"settlementResult":{"name":"settlementResult","fieldType":"CapSettlementResult","fieldPath":"settlementResult","cardinality":"SINGLE","forbidTarget":true},"settlementAbsenceInfo":{"name":"settlementAbsenceInfo","fieldType":"CapSettlementAbsenceInfo","fieldPath":"settlementAbsenceInfo","cardinality":"SINGLE","forbidTarget":true},"settlementLossInfo":{"name":"settlementLossInfo","fieldType":"CapSettlementLossInfo","fieldPath":"settlementLossInfo","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["RootEntity"],"system":false},"OccurrableCoverableItemCondition":{"name":"OccurrableCoverableItemCondition","children":{},"fields":{"accumulatorType":{"name":"accumulatorType","fieldType":"STRING","fieldPath":"accumulatorType","cardinality":"SINGLE"},"amount":{"name":"amount","fieldType":"DECIMAL","fieldPath":"amount","cardinality":"SINGLE"},"maxOccurrences":{"name":"maxOccurrences","fieldType":"INTEGER","fieldPath":"maxOccurrences","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition","Accumulative"],"system":false},"CapTimeline":{"name":"CapTimeline","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapTimelineRow":{"targetName":"CapTimelineRow","navigationExpression":{"expressionString":"rows","originalExpressionString":"rows","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"rows":{"name":"rows","fieldType":"CapTimelineRow","fieldPath":"rows","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["MessageTypeHolder"],"system":false},"LossDetail":{"name":"LossDetail","children":{},"fields":{"lossDesc":{"name":"lossDesc","fieldType":"STRING","fieldPath":"lossDesc","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"Period":{"name":"Period","children":{},"fields":{"endDate":{"name":"endDate","fieldType":"DATETIME","fieldPath":"endDate","cardinality":"SINGLE"},"startDate":{"name":"startDate","fieldType":"DATETIME","fieldPath":"startDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyFrequencyLimitationsEntity":{"name":"CapPolicyFrequencyLimitationsEntity","children":{"CapPolicyBasicLimitationsEntity":{"targetName":"CapPolicyBasicLimitationsEntity","navigationExpression":{"expressionString":"basicLimitations","originalExpressionString":"basicLimitations","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPolicyPreventiveLimitationsEntity":{"targetName":"CapPolicyPreventiveLimitationsEntity","navigationExpression":{"expressionString":"preventiveLimitations","originalExpressionString":"preventiveLimitations","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPolicyMajorLimitationsEntity":{"targetName":"CapPolicyMajorLimitationsEntity","navigationExpression":{"expressionString":"majorLimitations","originalExpressionString":"majorLimitations","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"basicLimitations":{"name":"basicLimitations","fieldType":"CapPolicyBasicLimitationsEntity","fieldPath":"basicLimitations","cardinality":"SINGLE","forbidTarget":true},"preventiveLimitations":{"name":"preventiveLimitations","fieldType":"CapPolicyPreventiveLimitationsEntity","fieldPath":"preventiveLimitations","cardinality":"SINGLE","forbidTarget":true},"majorLimitations":{"name":"majorLimitations","fieldType":"CapPolicyMajorLimitationsEntity","fieldPath":"majorLimitations","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapDentalCalculationStatusEntity":{"name":"CapDentalCalculationStatusEntity","children":{"CapDentalRemarkMessageEntity":{"targetName":"CapDentalRemarkMessageEntity","navigationExpression":{"expressionString":"remarkMessages","originalExpressionString":"remarkMessages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"},"flag":{"name":"flag","fieldType":"STRING","fieldPath":"flag","cardinality":"SINGLE"},"fee":{"name":"fee","fieldType":"MONEY","fieldPath":"fee","cardinality":"SINGLE"},"questions":{"name":"questions","fieldType":"STRING","fieldPath":"questions","cardinality":"MULTIPLE"},"procedureID":{"name":"procedureID","fieldType":"STRING","fieldPath":"procedureID","cardinality":"SINGLE"},"submittedCode":{"name":"submittedCode","fieldType":"STRING","fieldPath":"submittedCode","cardinality":"SINGLE"},"coveredCode":{"name":"coveredCode","fieldType":"STRING","fieldPath":"coveredCode","cardinality":"SINGLE"},"remarkMessages":{"name":"remarkMessages","fieldType":"CapDentalRemarkMessageEntity","fieldPath":"remarkMessages","cardinality":"MULTIPLE","forbidTarget":true},"statusReason":{"name":"statusReason","fieldType":"STRING","fieldPath":"statusReason","cardinality":"SINGLE"},"preauthorizationNumber":{"name":"preauthorizationNumber","fieldType":"STRING","fieldPath":"preauthorizationNumber","cardinality":"SINGLE"},"percentage":{"name":"percentage","fieldType":"DECIMAL","fieldPath":"percentage","cardinality":"SINGLE"},"predetInd":{"name":"predetInd","fieldType":"BOOLEAN","fieldPath":"predetInd","cardinality":"SINGLE"},"reasonCode":{"name":"reasonCode","fieldType":"STRING","fieldPath":"reasonCode","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ExternalLink":{"name":"ExternalLink","children":{},"fields":{},"inheritedContexts":[],"system":true},"ClaimantInfo":{"name":"ClaimantInfo","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"lastName":{"name":"lastName","fieldType":"STRING","fieldPath":"lastName","cardinality":"SINGLE"},"city":{"name":"city","fieldType":"STRING","fieldPath":"city","cardinality":"MULTIPLE"},"stateProvinceCd":{"name":"stateProvinceCd","fieldType":"STRING","fieldPath":"stateProvinceCd","cardinality":"MULTIPLE"},"postalCode":{"name":"postalCode","fieldType":"STRING","fieldPath":"postalCode","cardinality":"MULTIPLE"},"legalId":{"name":"legalId","fieldType":"STRING","fieldPath":"legalId","cardinality":"SINGLE"},"customerNumber":{"name":"customerNumber","fieldType":"STRING","fieldPath":"customerNumber","cardinality":"SINGLE"},"legalName":{"name":"legalName","fieldType":"STRING","fieldPath":"legalName","cardinality":"SINGLE"},"firstName":{"name":"firstName","fieldType":"STRING","fieldPath":"firstName","cardinality":"SINGLE"},"phoneNumber":{"name":"phoneNumber","fieldType":"STRING","fieldPath":"phoneNumber","cardinality":"MULTIPLE"},"streetAddress":{"name":"streetAddress","fieldType":"STRING","fieldPath":"streetAddress","cardinality":"MULTIPLE"},"taxId":{"name":"taxId","fieldType":"STRING","fieldPath":"taxId","cardinality":"SINGLE"},"customerId":{"name":"customerId","fieldType":"STRING","fieldPath":"customerId","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"email":{"name":"email","fieldType":"STRING","fieldPath":"email","cardinality":"MULTIPLE"}},"inheritedContexts":["PartyInfo","Retriable","MessageTypeHolder"],"system":false},"RootEntity":{"name":"RootEntity","children":{},"fields":{},"inheritedContexts":[],"system":false},"CapDentalOrthodonticEntity":{"name":"CapDentalOrthodonticEntity","children":{},"fields":{"months":{"name":"months","fieldType":"INTEGER","fieldPath":"months","cardinality":"SINGLE"},"orthoFrequencyCd":{"name":"orthoFrequencyCd","fieldType":"STRING","fieldPath":"orthoFrequencyCd","cardinality":"SINGLE"},"orthoMonthQuantity":{"name":"orthoMonthQuantity","fieldType":"INTEGER","fieldPath":"orthoMonthQuantity","cardinality":"SINGLE"},"downPayment":{"name":"downPayment","fieldType":"MONEY","fieldPath":"downPayment","cardinality":"SINGLE"},"appliancePlacedDate":{"name":"appliancePlacedDate","fieldType":"DATE","fieldPath":"appliancePlacedDate","cardinality":"SINGLE"},"frequency":{"name":"frequency","fieldType":"STRING","fieldPath":"frequency","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalanceItemAllocationReduction":{"name":"CapBalanceItemAllocationReduction","children":{},"fields":{"reductionSubType":{"name":"reductionSubType","fieldType":"STRING","fieldPath":"reductionSubType","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"},"reductionType":{"name":"reductionType","fieldType":"STRING","fieldPath":"reductionType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"LOBEntity":{"name":"LOBEntity","children":{},"fields":{"lobCd":{"name":"lobCd","fieldType":"STRING","fieldPath":"lobCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalBaseProcedure":{"name":"CapDentalBaseProcedure","children":{"CapDentalPreauthorizationEntity":{"targetName":"CapDentalPreauthorizationEntity","navigationExpression":{"expressionString":"preauthorization","originalExpressionString":"preauthorization","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalOrthodonticEntity":{"targetName":"CapDentalOrthodonticEntity","navigationExpression":{"expressionString":"ortho","originalExpressionString":"ortho","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalProcedureCoordinationOfBenefitsEntity":{"targetName":"CapDentalProcedureCoordinationOfBenefitsEntity","navigationExpression":{"expressionString":"cob","originalExpressionString":"cob","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalDiagnosisCodeEntity":{"targetName":"CapDentalDiagnosisCodeEntity","navigationExpression":{"expressionString":"diagnosisCodes","originalExpressionString":"diagnosisCodes","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"procedureType":{"name":"procedureType","fieldType":"STRING","fieldPath":"procedureType","cardinality":"SINGLE"},"toothSystem":{"name":"toothSystem","fieldType":"STRING","fieldPath":"toothSystem","cardinality":"SINGLE"},"quantity":{"name":"quantity","fieldType":"INTEGER","fieldPath":"quantity","cardinality":"SINGLE"},"procedureCode":{"name":"procedureCode","fieldType":"STRING","fieldPath":"procedureCode","cardinality":"SINGLE"},"preauthorization":{"name":"preauthorization","fieldType":"CapDentalPreauthorizationEntity","fieldPath":"preauthorization","cardinality":"SINGLE","forbidTarget":true},"description":{"name":"description","fieldType":"STRING","fieldPath":"description","cardinality":"SINGLE"},"ortho":{"name":"ortho","fieldType":"CapDentalOrthodonticEntity","fieldPath":"ortho","cardinality":"SINGLE"},"diagnosisCodes":{"name":"diagnosisCodes","fieldType":"CapDentalDiagnosisCodeEntity","fieldPath":"diagnosisCodes","cardinality":"MULTIPLE","forbidTarget":true},"preauthorizationNumber":{"name":"preauthorizationNumber","fieldType":"STRING","fieldPath":"preauthorizationNumber","cardinality":"SINGLE"},"surfaces":{"name":"surfaces","fieldType":"STRING","fieldPath":"surfaces","cardinality":"MULTIPLE"},"toothArea":{"name":"toothArea","fieldType":"STRING","fieldPath":"toothArea","cardinality":"SINGLE"},"cob":{"name":"cob","fieldType":"CapDentalProcedureCoordinationOfBenefitsEntity","fieldPath":"cob","cardinality":"SINGLE","forbidTarget":true},"predetInd":{"name":"predetInd","fieldType":"BOOLEAN","fieldPath":"predetInd","cardinality":"SINGLE"},"submittedFee":{"name":"submittedFee","fieldType":"MONEY","fieldPath":"submittedFee","cardinality":"SINGLE"},"toothCodes":{"name":"toothCodes","fieldType":"STRING","fieldPath":"toothCodes","cardinality":"MULTIPLE"},"dateOfService":{"name":"dateOfService","fieldType":"DATE","fieldPath":"dateOfService","cardinality":"SINGLE"},"priorProsthesisPlacementDate":{"name":"priorProsthesisPlacementDate","fieldType":"DATE","fieldPath":"priorProsthesisPlacementDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentDetails":{"name":"CapPaymentDetails","children":{"CapPaymentAddition":{"targetName":"CapPaymentAddition","navigationExpression":{"expressionString":"paymentAdditions","originalExpressionString":"paymentAdditions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentAllocation":{"targetName":"CapPaymentAllocation","navigationExpression":{"expressionString":"paymentAllocations","originalExpressionString":"paymentAllocations","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentReduction":{"targetName":"CapPaymentReduction","navigationExpression":{"expressionString":"paymentReductions","originalExpressionString":"paymentReductions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentTax":{"targetName":"CapPaymentTax","navigationExpression":{"expressionString":"paymentTaxes","originalExpressionString":"paymentTaxes","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentPayeeDetails":{"targetName":"CapPaymentPayeeDetails","navigationExpression":{"expressionString":"payeeDetails","originalExpressionString":"payeeDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"payeeDetails":{"name":"payeeDetails","fieldType":"CapPaymentPayeeDetails","fieldPath":"payeeDetails","cardinality":"SINGLE","forbidTarget":true},"paymentAllocations":{"name":"paymentAllocations","fieldType":"CapPaymentAllocation","fieldPath":"paymentAllocations","cardinality":"MULTIPLE","forbidTarget":true},"paymentAdditions":{"name":"paymentAdditions","fieldType":"CapPaymentAddition","fieldPath":"paymentAdditions","cardinality":"MULTIPLE","forbidTarget":true},"paymentReductions":{"name":"paymentReductions","fieldType":"CapPaymentReduction","fieldPath":"paymentReductions","cardinality":"MULTIPLE","forbidTarget":true},"paymentDate":{"name":"paymentDate","fieldType":"DATETIME","fieldPath":"paymentDate","cardinality":"SINGLE"},"paymentTaxes":{"name":"paymentTaxes","fieldType":"CapPaymentTax","fieldPath":"paymentTaxes","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapDentalPPOFrequencyEntity":{"name":"CapDentalPPOFrequencyEntity","children":{},"fields":{"procedureType":{"name":"procedureType","fieldType":"STRING","fieldPath":"procedureType","cardinality":"SINGLE"},"frequencyComment":{"name":"frequencyComment","fieldType":"STRING","fieldPath":"frequencyComment","cardinality":"SINGLE"},"frequencyRange":{"name":"frequencyRange","fieldType":"STRING","fieldPath":"frequencyRange","cardinality":"SINGLE"},"frequencyRule":{"name":"frequencyRule","fieldType":"STRING","fieldPath":"frequencyRule","cardinality":"SINGLE"},"frequencyPeriodType":{"name":"frequencyPeriodType","fieldType":"STRING","fieldPath":"frequencyPeriodType","cardinality":"SINGLE"},"frequencyPeriod":{"name":"frequencyPeriod","fieldType":"INTEGER","fieldPath":"frequencyPeriod","cardinality":"SINGLE"},"frequency":{"name":"frequency","fieldType":"STRING","fieldPath":"frequency","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalanceChangeLog":{"name":"CapBalanceChangeLog","children":{"CapBalanceChangeLogDetails":{"targetName":"CapBalanceChangeLogDetails","navigationExpression":{"expressionString":"balanceChangeLogDetails","originalExpressionString":"balanceChangeLogDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"},"balanceChangeLogDetails":{"name":"balanceChangeLogDetails","fieldType":"CapBalanceChangeLogDetails","fieldPath":"balanceChangeLogDetails","cardinality":"SINGLE","forbidTarget":true},"eventCaseLink":{"name":"eventCaseLink","fieldType":"ExternalLink","fieldPath":"eventCaseLink","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"CapDentalDentistEntity":{"name":"CapDentalDentistEntity","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"practiceTerm","originalExpressionString":"practiceTerm","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"dentistID":{"name":"dentistID","fieldType":"STRING","fieldPath":"dentistID","cardinality":"SINGLE"},"inOutNetwork":{"name":"inOutNetwork","fieldType":"STRING","fieldPath":"inOutNetwork","cardinality":"SINGLE"},"pcdID":{"name":"pcdID","fieldType":"STRING","fieldPath":"pcdID","cardinality":"SINGLE"},"feeScheduleType":{"name":"feeScheduleType","fieldType":"STRING","fieldPath":"feeScheduleType","cardinality":"SINGLE"},"providerTIN":{"name":"providerTIN","fieldType":"STRING","fieldPath":"providerTIN","cardinality":"SINGLE"},"dentistSpecialties":{"name":"dentistSpecialties","fieldType":"STRING","fieldPath":"dentistSpecialties","cardinality":"MULTIPLE"},"practiceType":{"name":"practiceType","fieldType":"STRING","fieldPath":"practiceType","cardinality":"SINGLE"},"practiceTerm":{"name":"practiceTerm","fieldType":"Term","fieldPath":"practiceTerm","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"InsurableRisk":{"name":"InsurableRisk","children":{"CoverableItem":{"targetName":"CoverableItem","navigationExpression":{"expressionString":"coverableItems","originalExpressionString":"coverableItems","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"coverableItems":{"name":"coverableItems","fieldType":"CoverableItem","fieldPath":"coverableItems","cardinality":"MULTIPLE","forbidTarget":true},"id":{"name":"id","fieldType":"STRING","fieldPath":"id","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemHolder"],"system":false},"CapDentalClaimDataEntity":{"name":"CapDentalClaimDataEntity","children":{"CapProviderRole":{"targetName":"CapProviderRole","navigationExpression":{"expressionString":"providerRole","originalExpressionString":"providerRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapAlternatePayeeRole":{"targetName":"CapAlternatePayeeRole","navigationExpression":{"expressionString":"alternatePayeeRole","originalExpressionString":"alternatePayeeRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalClaimCoordinationOfBenefitsEntity":{"targetName":"CapDentalClaimCoordinationOfBenefitsEntity","navigationExpression":{"expressionString":"cob","originalExpressionString":"cob","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalProviderFeesEntity":{"targetName":"CapDentalProviderFeesEntity","navigationExpression":{"expressionString":"providerFees","originalExpressionString":"providerFees","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalProviderDiscountEntity":{"targetName":"CapDentalProviderDiscountEntity","navigationExpression":{"expressionString":"providerDiscount","originalExpressionString":"providerDiscount","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalTreatmentReasonEntity":{"targetName":"CapDentalTreatmentReasonEntity","navigationExpression":{"expressionString":"treatmentReason","originalExpressionString":"treatmentReason","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPatientRole":{"targetName":"CapPatientRole","navigationExpression":{"expressionString":"patientRole","originalExpressionString":"patientRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPolicyholderRole":{"targetName":"CapPolicyholderRole","navigationExpression":{"expressionString":"policyholderRole","originalExpressionString":"policyholderRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"providerFees":{"name":"providerFees","fieldType":"CapDentalProviderFeesEntity","fieldPath":"providerFees","cardinality":"MULTIPLE"},"providerRole":{"name":"providerRole","fieldType":"CapProviderRole","fieldPath":"providerRole","cardinality":"SINGLE"},"digitalImageNumbers":{"name":"digitalImageNumbers","fieldType":"STRING","fieldPath":"digitalImageNumbers","cardinality":"MULTIPLE"},"payeeType":{"name":"payeeType","fieldType":"STRING","fieldPath":"payeeType","cardinality":"SINGLE"},"cleanClaimDate":{"name":"cleanClaimDate","fieldType":"DATE","fieldPath":"cleanClaimDate","cardinality":"SINGLE"},"patientRole":{"name":"patientRole","fieldType":"CapPatientRole","fieldPath":"patientRole","cardinality":"SINGLE"},"isUnknownOrIntProvider":{"name":"isUnknownOrIntProvider","fieldType":"BOOLEAN","fieldPath":"isUnknownOrIntProvider","cardinality":"SINGLE"},"treatmentReason":{"name":"treatmentReason","fieldType":"CapDentalTreatmentReasonEntity","fieldPath":"treatmentReason","cardinality":"SINGLE","forbidTarget":true},"dateOfBirth":{"name":"dateOfBirth","fieldType":"DATE","fieldPath":"dateOfBirth","cardinality":"SINGLE"},"remark":{"name":"remark","fieldType":"STRING","fieldPath":"remark","cardinality":"SINGLE"},"source":{"name":"source","fieldType":"STRING","fieldPath":"source","cardinality":"SINGLE"},"providerDiscount":{"name":"providerDiscount","fieldType":"CapDentalProviderDiscountEntity","fieldPath":"providerDiscount","cardinality":"SINGLE"},"missingTooths":{"name":"missingTooths","fieldType":"STRING","fieldPath":"missingTooths","cardinality":"MULTIPLE"},"alternatePayeeRole":{"name":"alternatePayeeRole","fieldType":"CapAlternatePayeeRole","fieldPath":"alternatePayeeRole","cardinality":"SINGLE"},"transactionType":{"name":"transactionType","fieldType":"STRING","fieldPath":"transactionType","cardinality":"SINGLE"},"policyholderRole":{"name":"policyholderRole","fieldType":"CapPolicyholderRole","fieldPath":"policyholderRole","cardinality":"SINGLE"},"initialDateOfService":{"name":"initialDateOfService","fieldType":"DATETIME","fieldPath":"initialDateOfService","cardinality":"SINGLE"},"placeOfTreatment":{"name":"placeOfTreatment","fieldType":"STRING","fieldPath":"placeOfTreatment","cardinality":"SINGLE"},"cob":{"name":"cob","fieldType":"CapDentalClaimCoordinationOfBenefitsEntity","fieldPath":"cob","cardinality":"MULTIPLE","forbidTarget":true},"receivedDate":{"name":"receivedDate","fieldType":"DATE","fieldPath":"receivedDate","cardinality":"SINGLE"}},"inheritedContexts":["CapDentalBaseClaimData"],"system":false},"CapDentalWaiveClaimValueEntity":{"name":"CapDentalWaiveClaimValueEntity","children":{},"fields":{"waiveServiceFrequencyLimit":{"name":"waiveServiceFrequencyLimit","fieldType":"BOOLEAN","fieldPath":"waiveServiceFrequencyLimit","cardinality":"SINGLE"},"waiveOrthoWaitingPeriod":{"name":"waiveOrthoWaitingPeriod","fieldType":"BOOLEAN","fieldPath":"waiveOrthoWaitingPeriod","cardinality":"SINGLE"},"waivePreventiveWaitingPeriod":{"name":"waivePreventiveWaitingPeriod","fieldType":"BOOLEAN","fieldPath":"waivePreventiveWaitingPeriod","cardinality":"SINGLE"},"waiveDeductible":{"name":"waiveDeductible","fieldType":"BOOLEAN","fieldPath":"waiveDeductible","cardinality":"SINGLE"},"waiveMaximumLimitAmounts":{"name":"waiveMaximumLimitAmounts","fieldType":"BOOLEAN","fieldPath":"waiveMaximumLimitAmounts","cardinality":"SINGLE"},"waiveMajorWaitingPeriod":{"name":"waiveMajorWaitingPeriod","fieldType":"BOOLEAN","fieldPath":"waiveMajorWaitingPeriod","cardinality":"SINGLE"},"waiveReplacementLimit":{"name":"waiveReplacementLimit","fieldType":"BOOLEAN","fieldPath":"waiveReplacementLimit","cardinality":"SINGLE"},"waiveBasicWaitingPeriod":{"name":"waiveBasicWaitingPeriod","fieldType":"BOOLEAN","fieldPath":"waiveBasicWaitingPeriod","cardinality":"SINGLE"},"waiveEligibilityPriorStartDate":{"name":"waiveEligibilityPriorStartDate","fieldType":"BOOLEAN","fieldPath":"waiveEligibilityPriorStartDate","cardinality":"SINGLE"},"waiveEligibilityAfterStartDate":{"name":"waiveEligibilityAfterStartDate","fieldType":"BOOLEAN","fieldPath":"waiveEligibilityAfterStartDate","cardinality":"SINGLE"},"waiveLateEntrantWaitingPeriod":{"name":"waiveLateEntrantWaitingPeriod","fieldType":"BOOLEAN","fieldPath":"waiveLateEntrantWaitingPeriod","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyPreventiveLimitationsEntity":{"name":"CapPolicyPreventiveLimitationsEntity","children":{},"fields":{"preventiveFluorideTreatment":{"name":"preventiveFluorideTreatment","fieldType":"STRING","fieldPath":"preventiveFluorideTreatment","cardinality":"SINGLE"},"preventiveFluorideTreatmentAgeLimit":{"name":"preventiveFluorideTreatmentAgeLimit","fieldType":"STRING","fieldPath":"preventiveFluorideTreatmentAgeLimit","cardinality":"SINGLE"},"preventiveOralEvaluations":{"name":"preventiveOralEvaluations","fieldType":"STRING","fieldPath":"preventiveOralEvaluations","cardinality":"SINGLE"},"preventiveBitewingRadiographs":{"name":"preventiveBitewingRadiographs","fieldType":"STRING","fieldPath":"preventiveBitewingRadiographs","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapTimelineRow":{"name":"CapTimelineRow","children":{"CapTimelinePeriod":{"targetName":"CapTimelinePeriod","navigationExpression":{"expressionString":"periods","originalExpressionString":"periods","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapTimelineEvent":{"targetName":"CapTimelineEvent","navigationExpression":{"expressionString":"rowEvents","originalExpressionString":"rowEvents","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapTimelineClaimInfo":{"targetName":"CapTimelineClaimInfo","navigationExpression":{"expressionString":"claimInfo","originalExpressionString":"claimInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapTimelineMetric":{"targetName":"CapTimelineMetric","navigationExpression":{"expressionString":"metrics","originalExpressionString":"metrics","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"claimInfo":{"name":"claimInfo","fieldType":"CapTimelineClaimInfo","fieldPath":"claimInfo","cardinality":"SINGLE","forbidTarget":true},"periods":{"name":"periods","fieldType":"CapTimelinePeriod","fieldPath":"periods","cardinality":"MULTIPLE","forbidTarget":true},"metrics":{"name":"metrics","fieldType":"CapTimelineMetric","fieldPath":"metrics","cardinality":"MULTIPLE","forbidTarget":true},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"rowEvents":{"name":"rowEvents","fieldType":"CapTimelineEvent","fieldPath":"rowEvents","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapLoss":{"name":"CapLoss","children":{"LossDetail":{"targetName":"LossDetail","navigationExpression":{"expressionString":"lossDetail","originalExpressionString":"lossDetail","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"lossSubStatusCd":{"name":"lossSubStatusCd","fieldType":"STRING","fieldPath":"lossSubStatusCd","cardinality":"SINGLE"},"reasonDescription":{"name":"reasonDescription","fieldType":"STRING","fieldPath":"reasonDescription","cardinality":"SINGLE"},"lossNumber":{"name":"lossNumber","fieldType":"STRING","fieldPath":"lossNumber","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"lossDetail":{"name":"lossDetail","fieldType":"LossDetail","fieldPath":"lossDetail","cardinality":"SINGLE","forbidTarget":true},"reasonCd":{"name":"reasonCd","fieldType":"STRING","fieldPath":"reasonCd","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"CapDentalServiceOverrideEntity":{"name":"CapDentalServiceOverrideEntity","children":{"CapDentalBypassServiceLogicEntity":{"targetName":"CapDentalBypassServiceLogicEntity","navigationExpression":{"expressionString":"bypassServiceLogic","originalExpressionString":"bypassServiceLogic","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalOverrideServiceValueEntity":{"targetName":"CapDentalOverrideServiceValueEntity","navigationExpression":{"expressionString":"overrideServiceValue","originalExpressionString":"overrideServiceValue","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalWaiveServiceValueEntity":{"targetName":"CapDentalWaiveServiceValueEntity","navigationExpression":{"expressionString":"waiveServiceValue","originalExpressionString":"waiveServiceValue","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"isAllowed":{"name":"isAllowed","fieldType":"BOOLEAN","fieldPath":"isAllowed","cardinality":"SINGLE"},"isDenied":{"name":"isDenied","fieldType":"BOOLEAN","fieldPath":"isDenied","cardinality":"SINGLE"},"overrideRemark":{"name":"overrideRemark","fieldType":"STRING","fieldPath":"overrideRemark","cardinality":"SINGLE"},"overrideServiceValue":{"name":"overrideServiceValue","fieldType":"CapDentalOverrideServiceValueEntity","fieldPath":"overrideServiceValue","cardinality":"SINGLE","forbidTarget":true},"suppressMemberEob":{"name":"suppressMemberEob","fieldType":"BOOLEAN","fieldPath":"suppressMemberEob","cardinality":"SINGLE"},"suppressProviderEob":{"name":"suppressProviderEob","fieldType":"BOOLEAN","fieldPath":"suppressProviderEob","cardinality":"SINGLE"},"serviceSource":{"name":"serviceSource","fieldType":"STRING","fieldPath":"serviceSource","cardinality":"SINGLE"},"bypassServiceLogic":{"name":"bypassServiceLogic","fieldType":"CapDentalBypassServiceLogicEntity","fieldPath":"bypassServiceLogic","cardinality":"SINGLE","forbidTarget":true},"waiveServiceValue":{"name":"waiveServiceValue","fieldType":"CapDentalWaiveServiceValueEntity","fieldPath":"waiveServiceValue","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"WorkflowInfo":{"name":"WorkflowInfo","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"workStatusCd":{"name":"workStatusCd","fieldType":"STRING","fieldPath":"workStatusCd","cardinality":"SINGLE"},"workQueueCd":{"name":"workQueueCd","fieldType":"STRING","fieldPath":"workQueueCd","cardinality":"SINGLE"},"workUserId":{"name":"workUserId","fieldType":"STRING","fieldPath":"workUserId","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"workCaseId":{"name":"workCaseId","fieldType":"STRING","fieldPath":"workCaseId","cardinality":"SINGLE"}},"inheritedContexts":["Retriable","MessageTypeHolder"],"system":false},"CapRootIdentifier":{"name":"CapRootIdentifier","children":{},"fields":{"links":{"name":"links","fieldType":"ExternalLink","fieldPath":"links","cardinality":"MULTIPLE"}},"inheritedContexts":["RootEntity"],"system":false},"MessageTypeHolder":{"name":"MessageTypeHolder","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"ClaimSystemIdentifierHolder":{"name":"ClaimSystemIdentifierHolder","children":{},"fields":{"caseSystemId":{"name":"caseSystemId","fieldType":"ExternalLink","fieldPath":"caseSystemId","cardinality":"SINGLE"},"claimSystemId":{"name":"claimSystemId","fieldType":"ExternalLink","fieldPath":"claimSystemId","cardinality":"SINGLE"},"claimModelName":{"name":"claimModelName","fieldType":"STRING","fieldPath":"claimModelName","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"RequestedReserveItem":{"name":"RequestedReserveItem","children":{},"fields":{"amount":{"name":"amount","fieldType":"MONEY","fieldPath":"amount","cardinality":"SINGLE"},"description":{"name":"description","fieldType":"STRING","fieldPath":"description","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalSettlementEntity":{"name":"CapDentalSettlementEntity","children":{"AccessTrackInfo":{"targetName":"AccessTrackInfo","navigationExpression":{"expressionString":"accessTrackInfo","originalExpressionString":"accessTrackInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapSettlementAbsenceInfo":{"targetName":"CapSettlementAbsenceInfo","navigationExpression":{"expressionString":"settlementAbsenceInfo","originalExpressionString":"settlementAbsenceInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalClaimInfoEntity":{"targetName":"CapDentalClaimInfoEntity","navigationExpression":{"expressionString":"settlementLossInfo","originalExpressionString":"settlementLossInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalDecisionResultEntity":{"targetName":"CapDentalDecisionResultEntity","navigationExpression":{"expressionString":"settlementResult","originalExpressionString":"settlementResult","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalSettlementDetailEntity":{"targetName":"CapDentalSettlementDetailEntity","navigationExpression":{"expressionString":"settlementDetail","originalExpressionString":"settlementDetail","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"settlementType":{"name":"settlementType","fieldType":"STRING","fieldPath":"settlementType","cardinality":"SINGLE"},"settlementDetail":{"name":"settlementDetail","fieldType":"CapDentalSettlementDetailEntity","fieldPath":"settlementDetail","cardinality":"SINGLE","forbidTarget":true},"claimLossIdentification":{"name":"claimLossIdentification","fieldType":"ExternalLink","fieldPath":"claimLossIdentification","cardinality":"SINGLE"},"accessTrackInfo":{"name":"accessTrackInfo","fieldType":"AccessTrackInfo","fieldPath":"accessTrackInfo","cardinality":"SINGLE","forbidTarget":true},"settlementResult":{"name":"settlementResult","fieldType":"CapDentalDecisionResultEntity","fieldPath":"settlementResult","cardinality":"SINGLE","forbidTarget":true},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"settlementNumber":{"name":"settlementNumber","fieldType":"STRING","fieldPath":"settlementNumber","cardinality":"SINGLE"},"settlementAbsenceInfo":{"name":"settlementAbsenceInfo","fieldType":"CapSettlementAbsenceInfo","fieldPath":"settlementAbsenceInfo","cardinality":"SINGLE","forbidTarget":true},"settlementLossInfo":{"name":"settlementLossInfo","fieldType":"CapDentalClaimInfoEntity","fieldPath":"settlementLossInfo","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["CapSettlement","RootEntity"],"system":false},"AddressInfo":{"name":"AddressInfo","children":{},"fields":{"addressType":{"name":"addressType","fieldType":"STRING","fieldPath":"addressType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalanceSuspenseItem":{"name":"CapBalanceSuspenseItem","children":{},"fields":{"paymentDate":{"name":"paymentDate","fieldType":"DATETIME","fieldPath":"paymentDate","cardinality":"SINGLE"},"paymentNumber":{"name":"paymentNumber","fieldType":"STRING","fieldPath":"paymentNumber","cardinality":"SINGLE"},"suspenseAmount":{"name":"suspenseAmount","fieldType":"MONEY","fieldPath":"suspenseAmount","cardinality":"SINGLE"},"direction":{"name":"direction","fieldType":"STRING","fieldPath":"direction","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalDecisionResultEntity":{"name":"CapDentalDecisionResultEntity","children":{"CapDentalResultEntryEntity":{"targetName":"CapDentalResultEntryEntity","navigationExpression":{"expressionString":"entries","originalExpressionString":"entries","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"proposal":{"name":"proposal","fieldType":"STRING","fieldPath":"proposal","cardinality":"SINGLE"},"entries":{"name":"entries","fieldType":"CapDentalResultEntryEntity","fieldPath":"entries","cardinality":"MULTIPLE","forbidTarget":true},"payeeRef":{"name":"payeeRef","fieldType":"STRING","fieldPath":"payeeRef","cardinality":"SINGLE"},"reserve":{"name":"reserve","fieldType":"DECIMAL","fieldPath":"reserve","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"paymentAmount":{"name":"paymentAmount","fieldType":"MONEY","fieldPath":"paymentAmount","cardinality":"SINGLE"}},"inheritedContexts":["CapSettlementResult"],"system":false},"SinglePolicyHolder":{"name":"SinglePolicyHolder","children":{"CapPolicyInfo":{"targetName":"CapPolicyInfo","navigationExpression":{"expressionString":"policy","originalExpressionString":"policy","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"policyId":{"name":"policyId","fieldType":"STRING","fieldPath":"policyId","cardinality":"SINGLE"},"policy":{"name":"policy","fieldType":"CapPolicyInfo","fieldPath":"policy","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapInsuredInfo":{"name":"CapInsuredInfo","children":{},"fields":{"isMain":{"name":"isMain","fieldType":"BOOLEAN","fieldPath":"isMain","cardinality":"SINGLE"},"registryTypeId":{"name":"registryTypeId","fieldType":"STRING","fieldPath":"registryTypeId","cardinality":"SINGLE"},"insuredRoleNameCd":{"name":"insuredRoleNameCd","fieldType":"STRING","fieldPath":"insuredRoleNameCd","cardinality":"SINGLE"}},"inheritedContexts":["InsuredInfoAware"],"system":false},"CapClaimHeader":{"name":"CapClaimHeader","children":{"CapClaimHeaderCase":{"targetName":"CapClaimHeaderCase","navigationExpression":{"expressionString":"claimCase","originalExpressionString":"claimCase","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"ClaimSubjectInfo":{"targetName":"ClaimSubjectInfo","navigationExpression":{"expressionString":"subjects","originalExpressionString":"subjects","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"ClaimantAware":{"targetName":"ClaimantAware","navigationExpression":{"expressionString":"claimant","originalExpressionString":"claimant","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapClaimHeaderPolicy":{"targetName":"CapClaimHeaderPolicy","navigationExpression":{"expressionString":"policies","originalExpressionString":"policies","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"InsuredAware":{"targetName":"InsuredAware","navigationExpression":{"expressionString":"insured","originalExpressionString":"insured","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"insured":{"name":"insured","fieldType":"InsuredAware","fieldPath":"insured","cardinality":"SINGLE","forbidTarget":true},"subjects":{"name":"subjects","fieldType":"ClaimSubjectInfo","fieldPath":"subjects","cardinality":"MULTIPLE","forbidTarget":true},"policies":{"name":"policies","fieldType":"CapClaimHeaderPolicy","fieldPath":"policies","cardinality":"MULTIPLE","forbidTarget":true},"totalIncurred":{"name":"totalIncurred","fieldType":"MONEY","fieldPath":"totalIncurred","cardinality":"SINGLE"},"primaryURI":{"name":"primaryURI","fieldType":"ExternalLink","fieldPath":"primaryURI","cardinality":"SINGLE"},"claimType":{"name":"claimType","fieldType":"STRING","fieldPath":"claimType","cardinality":"SINGLE"},"claimCase":{"name":"claimCase","fieldType":"CapClaimHeaderCase","fieldPath":"claimCase","cardinality":"SINGLE","forbidTarget":true},"claimDate":{"name":"claimDate","fieldType":"DATETIME","fieldPath":"claimDate","cardinality":"SINGLE"},"caseNumber":{"name":"caseNumber","fieldType":"STRING","fieldPath":"caseNumber","cardinality":"SINGLE"},"reportedDate":{"name":"reportedDate","fieldType":"DATETIME","fieldPath":"reportedDate","cardinality":"SINGLE"},"mappingType":{"name":"mappingType","fieldType":"STRING","fieldPath":"mappingType","cardinality":"SINGLE"},"claimDates":{"name":"claimDates","fieldType":"DATETIME","fieldPath":"claimDates","cardinality":"MULTIPLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"claimModelName":{"name":"claimModelName","fieldType":"STRING","fieldPath":"claimModelName","cardinality":"SINGLE"},"claimant":{"name":"claimant","fieldType":"ClaimantAware","fieldPath":"claimant","cardinality":"SINGLE","forbidTarget":true},"claimNumber":{"name":"claimNumber","fieldType":"STRING","fieldPath":"claimNumber","cardinality":"SINGLE"}},"inheritedContexts":["ClaimHeaderLink"],"system":false},"CapPolicyholderRole":{"name":"CapPolicyholderRole","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyRole","ClaimPartyAware"],"system":false}},"pathsToNodes":{"CapAlternatePayeeRole":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapAlternatePayeeRole"]}],"CapDentalPolicyInfoInsuredDetailsEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalPolicyInfoInsuredDetailsEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalPolicyInfoInsuredDetailsEntity"]}],"CapDentalClaimInfoEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity"]}],"CapDentalAccumulatorEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalAccumulatorEntity"]},{"path":["CapDentalSettlementEntity","CapDentalDecisionResultEntity","CapDentalResultEntryEntity","CapDentalAccumulatorEntity"]}],"CapDentalTermEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalTermEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalTermEntity"]}],"CapPolicyMajorLimitationsEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyFrequencyLimitationsEntity","CapPolicyMajorLimitationsEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyFrequencyLimitationsEntity","CapPolicyMajorLimitationsEntity"]}],"CapSettlementDetail":[{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity"]}],"ClaimPolicyAware":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity"]}],"CapDentalPatientEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity"]}],"AccessTrackInfo":[{"path":["CapDentalSettlementEntity","AccessTrackInfo"]}],"CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"]}],"CapDentalBypassClaimLogicEntity":[{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity","CapDentalClaimOverrideEntity","CapDentalBypassClaimLogicEntity"]}],"CapDentalProviderFeesEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapDentalProviderFeesEntity"]}],"CapDentalRemarkMessageEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalCalculationStatusEntity","CapDentalRemarkMessageEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalCalculationStatusEntity","CapDentalRemarkMessageEntity"]},{"path":["CapDentalSettlementEntity","CapDentalDecisionResultEntity","CapDentalResultEntryEntity","CapDentalCalculationStatusEntity","CapDentalRemarkMessageEntity"]}],"CapDentalUnverifiedInfoEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalUnverifiedInfoEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalUnverifiedInfoEntity"]}],"CapSettlement":[{"path":["CapDentalSettlementEntity"]}],"CapDentalPolicyInfoCoinsuranceEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalPolicyInfoCoinsuranceEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalPolicyInfoCoinsuranceEntity"]}],"CapDentalWaiveServiceValueEntity":[{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity","CapDentalServiceOverrideEntity","CapDentalWaiveServiceValueEntity"]}],"CapDentalSettlementDetailEntity":[{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity"]}],"MessageType":[{"path":["CapDentalSettlementEntity","CapDentalDecisionResultEntity","MessageType"]}],"CapPolicyInfo":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity"]}],"CapPolicyServiceCategoryEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyServiceCategoryEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyServiceCategoryEntity"]}],"CapSettlementAbsenceInfo":[{"path":["CapDentalSettlementEntity","CapSettlementAbsenceInfo"]}],"Period":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPreauthorizationEntity","Period"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPreauthorizationEntity","Period"]},{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity","CapDentalServiceOverrideEntity","CapDentalOverrideServiceValueEntity","Period"]},{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity","CapDentalClaimOverrideEntity","CapDentalOverrideClaimValueEntity","Period"]}],"Term":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalAccumulatorEntity","Term"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","Term"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalTermEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalDentistEntity","Term"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapDentalClaimCoordinationOfBenefitsEntity","Term"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","Term"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalTermEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalDentistEntity","Term"]},{"path":["CapDentalSettlementEntity","CapDentalDecisionResultEntity","CapDentalResultEntryEntity","CapDentalAccumulatorEntity","Term"]}],"CapPolicyFrequencyLimitationsEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyFrequencyLimitationsEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyFrequencyLimitationsEntity"]}],"CapDentalCalculationStatusEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalCalculationStatusEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalCalculationStatusEntity"]},{"path":["CapDentalSettlementEntity","CapDentalDecisionResultEntity","CapDentalResultEntryEntity","CapDentalCalculationStatusEntity"]}],"CapPatientRole":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapPatientRole"]}],"RootEntity":[{"path":["CapDentalSettlementEntity"]}],"CapPolicyEligibilityEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyEligibilityEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyEligibilityEntity"]}],"CapDentalPreauthorizationEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPreauthorizationEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPreauthorizationEntity"]}],"CapDentalOrthodonticEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalOrthodonticEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalOrthodonticEntity"]}],"CapDentalClaimCoordinationOfBenefitsEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapDentalClaimCoordinationOfBenefitsEntity"]}],"InsuredInfoAware":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalPolicyInfoInsuredDetailsEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalPolicyInfoInsuredDetailsEntity"]}],"CapDentalDHMOFrequencyEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalDHMOFrequencyEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalDHMOFrequencyEntity"]}],"CapDentalProcedureEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity"]}],"CapDentalMaximumEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalMaximumEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalMaximumEntity"]}],"CapDentalBypassServiceLogicEntity":[{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity","CapDentalServiceOverrideEntity","CapDentalBypassServiceLogicEntity"]}],"CapProviderRole":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapProviderRole"]}],"CapPolicyBasicLimitationsEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyFrequencyLimitationsEntity","CapPolicyBasicLimitationsEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyFrequencyLimitationsEntity","CapPolicyBasicLimitationsEntity"]}],"CapSettlementLossInfo":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity"]}],"CapDentalProcedureCoordinationOfBenefitsEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalProcedureCoordinationOfBenefitsEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalProcedureCoordinationOfBenefitsEntity"]}],"CapDentalTreatmentReasonEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapDentalTreatmentReasonEntity"]}],"CapDentalDiagnosisCodeEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalDiagnosisCodeEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalDiagnosisCodeEntity"]}],"CapDentalOverrideServiceValueEntity":[{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity","CapDentalServiceOverrideEntity","CapDentalOverrideServiceValueEntity"]}],"CapDentalBaseProcedure":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity"]}],"CapDentalCalculationResultEntity":[{"path":["CapDentalSettlementEntity","CapDentalDecisionResultEntity","CapDentalResultEntryEntity","CapDentalCalculationResultEntity"]}],"CapDentalBaseClaimData":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity"]}],"CapDentalPolicyInfoEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity"]}],"CapDentalPPOFrequencyEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPPOFrequencyEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPPOFrequencyEntity"]}],"CapDentalResultEntryEntity":[{"path":["CapDentalSettlementEntity","CapDentalDecisionResultEntity","CapDentalResultEntryEntity"]}],"CapSettlementResult":[{"path":["CapDentalSettlementEntity","CapDentalDecisionResultEntity"]}],"CapDentalDentistEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalDentistEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalDentistEntity"]}],"CapDentalClaimDataEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity"]}],"CapDentalWaiveClaimValueEntity":[{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity","CapDentalClaimOverrideEntity","CapDentalWaiveClaimValueEntity"]}],"CapPolicyPreventiveLimitationsEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyFrequencyLimitationsEntity","CapPolicyPreventiveLimitationsEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapPolicyFrequencyLimitationsEntity","CapPolicyPreventiveLimitationsEntity"]}],"CapDeductibleDetailEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDeductibleDetailEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDeductibleDetailEntity"]}],"CapDentalServiceOverrideEntity":[{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity","CapDentalServiceOverrideEntity"]}],"CapDentalClaimOverrideEntity":[{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity","CapDentalClaimOverrideEntity"]}],"CapDentalSettlementEntity":[{"path":["CapDentalSettlementEntity"]}],"ClaimPartyRole":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapProviderRole"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapAlternatePayeeRole"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapPatientRole"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapPolicyholderRole"]}],"CapDentalOverrideClaimValueEntity":[{"path":["CapDentalSettlementEntity","CapDentalSettlementDetailEntity","CapDentalClaimOverrideEntity","CapDentalOverrideClaimValueEntity"]}],"CapDentalFeeRateEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalFeeRateEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalFeeRateEntity"]}],"ClaimPartyAware":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapProviderRole"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapAlternatePayeeRole"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapPatientRole"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapPolicyholderRole"]}],"CapDentalDecisionResultEntity":[{"path":["CapDentalSettlementEntity","CapDentalDecisionResultEntity"]}],"CapDentalProviderDiscountEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapDentalProviderDiscountEntity"]}],"CapClaimHeaderPolicy":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity"]}],"CapInsuredInfo":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalPolicyInfoInsuredDetailsEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalPolicyInfoEntity","CapDentalPolicyInfoInsuredDetailsEntity"]}],"CapDentalConsultantReviewEntity":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalPatientEntity","CapDentalProcedureEntity","CapDentalConsultantReviewEntity"]},{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalProcedureEntity","CapDentalConsultantReviewEntity"]}],"CapPolicyholderRole":[{"path":["CapDentalSettlementEntity","CapDentalClaimInfoEntity","CapDentalClaimDataEntity","CapPolicyholderRole"]}]},"metadata":{"namespace":"CapDentalSettlement","targetEnvironment":"JAVASCRIPT"}}