/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React from 'react'
import {observer} from 'mobx-react'

import {DENTAL_FINANCIAL_ACTIONS_WRAPPER} from '../classnames'
import {AuthorityApproval} from './AuthorityApproval'
import {PaymentActionsDropdown} from './PaymentActionsDropdown'

export const PaymentActions: React.FC = observer(() => {
    return (
        <div className={DENTAL_FINANCIAL_ACTIONS_WRAPPER}>
            <AuthorityApproval />
            <PaymentActionsDropdown />
        </div>
    )
})
