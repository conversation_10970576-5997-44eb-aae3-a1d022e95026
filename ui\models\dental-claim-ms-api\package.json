{"name": "@eisgroup/dental-claim-ms-api", "version": "25.7.0-SNAPSHOT.202508080205", "description": "Autogenerated dental-claim-ms-api", "main": "target/dist/js/src/index.js", "typings": "target/dist/definitions/src/index.d.ts", "license": "UNLICENSED", "files": ["target/dist/"], "publishConfig": {"registry": "https://genesis-npm-release.exigengroup.com/repository/genesis-npm-release/"}, "dependencies": {}, "scripts": {"build": "tsc --project tsconfig.build.json  --emitDeclarationOnly", "postbuild": "cross-env BABEL_ENV=build node ../../node_modules/@babel/cli/bin/babel.js src --out-dir target/dist/js/src --source-maps --extensions .js,.jsx,.ts,.tsx --no-comments --config-file @eisgroup/infra-scripts/config/models-babel.config", "clean:target": "rm -rf ./target", "clean:node-modules": "rm -rf ./node_modules", "clean:all": "yarn clean:target && yarn clean:node-modules", "generate-models": "mvn clean && mvn install"}}