import React, {<PERSON>} from 'react'
import {sumBy} from 'lodash'

import {MoneyFormat} from '@eisgroup/dental-core'
import {CapDentalBalance, CapDentalSettlement} from '@eisgroup/dental-models'
import {useLookupValues} from '@eisgroup/form'
import {useTranslate} from '@eisgroup/i18n'
import {Col, Row} from '@eisgroup/ui-kit'

import {formatDate} from '../../../../../utils/helpers'
import {EntityLink} from '../../../../../utils/helpers/entities'
import {
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND,
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ALLOCATION,
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_INTEREST,
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_PAYMENT_AMOUNT,
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ROW,
    RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_SHOULD_HAVE_PAID
} from './classnames'

import CapDentalBalanceItemEntity = CapDentalBalance.CapDentalBalanceItemEntity
import CapDentalSettlementEntity = CapDentalSettlement.CapDentalSettlementEntity

interface RecalculatedPaymentExpandProps {
    balanceItem: CapDentalBalanceItemEntity
    settlements: CapDentalSettlementEntity[]
}

export const RecalculatedPaymentExpand: FC<RecalculatedPaymentExpandProps> = props => {
    const {balanceItem, settlements} = props
    const {actualAllocations} = balanceItem
    const {values: procedureCodeList} = useLookupValues('CapDNProcedureCode', true)
    const {t} = useTranslate()

    const expandData = actualAllocations.map(allocation => {
        const currentAllocationSource = allocation.allocationSource?._uri
            ? EntityLink.from(allocation.allocationSource?._uri)
            : undefined
        const currentAllocationSourceRootId = currentAllocationSource?.rootId
        const currentAllocationSourceRevision = currentAllocationSource?.revisionNo
        const currentAllocationprocedureID = allocation.allocationPayableItem?.procedureID
        const settlement = settlements.find(
            settlement =>
                settlement._key.rootId === currentAllocationSourceRootId &&
                settlement._key.revisionNo.toString() === currentAllocationSourceRevision
        )
        const submittedProcedures = settlement?.settlementLossInfo?.submittedProcedures
        const settlementResultEntry = settlement?.settlementResult?.entries?.find(
            entry => entry.serviceSource === currentAllocationprocedureID
        )
        const submitPrecedure = submittedProcedures?.find(
            procedure => procedure._key.id === settlementResultEntry?.serviceSource
        )
        const procedureCode = settlementResultEntry?.status?.submittedCode ?? ''
        const procedureDesc = procedureCodeList?.find(one => one.code === procedureCode)?.displayValue ?? ''
        const dateOfService = submitPrecedure?.dateOfService ? formatDate(submitPrecedure?.dateOfService) : ''

        const serviceDetailTitle = `${procedureCode}-${procedureDesc}(${dateOfService})`
        const paymentTotalAllocation = allocation?.allocationGrossAmount?.amount
        const shouldHavePaidTotalAllocation =
            sumBy(allocation?.scheduledAllocations, schedule => schedule?.allocationGrossAmount!.amount) || 0

        return {
            key: allocation._key.rootId,
            serviceDetailTitle,
            paymentTotalAllocation,
            shouldHavePaidTotalAllocation
        }
    })
    return (
        <div className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND}>
            {expandData.map(data => {
                const {key, serviceDetailTitle, paymentTotalAllocation, shouldHavePaidTotalAllocation} = data
                return (
                    <Row className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ROW} key={key}>
                        <Col span={8}>
                            <h4>{serviceDetailTitle}</h4>
                        </Col>
                        <Col span={8} className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_PAYMENT_AMOUNT}>
                            <div className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ALLOCATION}>
                                <label>
                                    {t(
                                        'dental-product:dental-claim-balance-balance-recalculated-payments-total-allocation'
                                    )}
                                </label>
                                <MoneyFormat value={paymentTotalAllocation} />
                            </div>
                            <div className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_INTEREST}>
                                <label>
                                    {t(
                                        'dental-product:dental-claim-balance-balance-recalculated-payments-total-interests'
                                    )}
                                </label>
                                {/** interests */}
                            </div>
                        </Col>
                        <Col span={8} className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_SHOULD_HAVE_PAID}>
                            <div className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_ALLOCATION}>
                                <label>
                                    {t(
                                        'dental-product:dental-claim-balance-balance-recalculated-payments-total-allocation'
                                    )}
                                </label>
                                <MoneyFormat value={shouldHavePaidTotalAllocation} />
                            </div>
                            <div className={RECALCULATED_PAYMENTS_ALLOCATION_TABLE_EXPAND_INTEREST}>
                                <label>
                                    {t(
                                        'dental-product:dental-claim-balance-balance-recalculated-payments-total-interests'
                                    )}
                                </label>
                                {/** interests */}
                            </div>
                        </Col>
                    </Row>
                )
            })}
        </div>
    )
}
